"""
简化测试 - 验证基本模块导入和功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本模块导入"""
    print("🔧 测试基本模块导入...")
    
    try:
        # 测试三维效应模块
        from core.physics.three_dimensional_effects import ThreeDimensionalEffectsManager
        print("✅ 三维效应模块导入成功")
        
        # 测试高级压缩性修正
        from core.physics.corrections import AdvancedCompressibilityCorrection
        print("✅ 高级压缩性修正模块导入成功")
        
        # 测试BEMT求解器
        from core.aerodynamics.solvers.bemt_solver import BEMTSolver
        print("✅ BEMT求解器导入成功")
        
        # 测试UVLM求解器
        from core.aerodynamics.solvers.uvlm_solver import UVLMSolver
        print("✅ UVLM求解器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_three_dimensional_effects():
    """测试三维效应模块"""
    print("\n🔧 测试三维效应模块...")
    
    try:
        from core.physics.three_dimensional_effects import ThreeDimensionalEffectsManager
        
        # 创建配置
        config = {
            'enable_nonlinear_induced_velocity': True,
            'enable_blade_interaction': True,
            'enable_du_selig_radial_flow': True,
            'nonlinear_induced_config': {},
            'blade_interaction_config': {'blade_count': 3},
            'du_selig_config': {}
        }
        
        # 创建管理器
        manager = ThreeDimensionalEffectsManager(config)
        print(f"✅ 三维效应管理器创建成功，加载了 {len(manager.models)} 个模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 三维效应模块测试失败: {e}")
        return False

def test_advanced_compressibility():
    """测试高级压缩性修正"""
    print("\n🔧 测试高级压缩性修正...")
    
    try:
        from core.physics.corrections import AdvancedCompressibilityCorrection
        
        # 创建修正器
        correction = AdvancedCompressibilityCorrection("advanced")
        
        # 测试修正计算
        Cl_corr, Cd_corr = correction.apply_correction(
            Cl=0.8, Cd=0.03, mach=0.7, thickness_ratio=0.12
        )
        
        print(f"✅ 压缩性修正计算成功: Cl={Cl_corr:.3f}, Cd={Cd_corr:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级压缩性修正测试失败: {e}")
        return False

def test_free_wake_manager():
    """测试自由尾迹管理器"""
    print("\n🔧 测试自由尾迹管理器...")
    
    try:
        from core.aerodynamics.solvers.uvlm_solver import FreeWakeManager
        
        # 创建配置
        config = {
            'max_wake_age': 5.0,
            'wake_time_step': 0.01,
            'pc_iterations': 3,
            'vortex_core_radius': 0.03
        }
        
        # 创建管理器
        wake_manager = FreeWakeManager(config)
        print("✅ 自由尾迹管理器创建成功")
        
        # 测试基本功能
        wake_geometry = wake_manager.get_wake_geometry()
        print(f"✅ 尾迹几何获取成功，节点数: {wake_geometry['node_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自由尾迹管理器测试失败: {e}")
        return False

def test_time_history_manager():
    """测试时间历史管理器"""
    print("\n🔧 测试时间历史管理器...")
    
    try:
        from core.acoustics.solvers.fwh_solver import TimeHistoryManager
        
        # 创建配置
        config = {
            'max_history_time': 5.0,
            'adaptive_window': True,
            'c0': 343.0
        }
        
        # 创建管理器
        history_manager = TimeHistoryManager(config)
        print("✅ 时间历史管理器创建成功")
        
        # 添加测试数据
        import numpy as np
        position = np.array([1.0, 0.0, 0.0])
        velocity = np.array([0.0, 1.0, 0.0])
        force = np.array([1.0, 0.0, 0.0])
        
        history_manager.add_data_point(0.0, position, velocity, force)
        print("✅ 数据点添加成功")
        
        # 获取统计信息
        stats = history_manager.get_statistics()
        print(f"✅ 统计信息获取成功，数据点数: {stats['current_data_points']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间历史管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化功能测试...")
    print("=" * 50)
    
    tests = [
        ("基本模块导入", test_basic_imports),
        ("三维效应模块", test_three_dimensional_effects),
        ("高级压缩性修正", test_advanced_compressibility),
        ("自由尾迹管理器", test_free_wake_manager),
        ("时间历史管理器", test_time_history_manager),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有简化测试通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
