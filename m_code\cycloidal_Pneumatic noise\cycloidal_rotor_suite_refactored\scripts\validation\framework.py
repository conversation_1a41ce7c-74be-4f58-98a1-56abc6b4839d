"""
验证框架核心实现
===============

基于文档要求实现的完整验证框架
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import logging
from pathlib import Path

from .metrics import MetricsCalculator
from .test_cases import StandardTestCases
from .data_manager import ExperimentalDataManager

@dataclass
class ValidationResult:
    """验证结果数据类"""
    test_case_name: str
    solver_name: str
    metrics: Dict[str, float]
    simulation_data: Dict[str, Any]
    reference_data: Dict[str, Any]
    execution_time: float
    convergence_achieved: bool
    error_analysis: Dict[str, Any]

class ValidationFramework:
    """
    验证框架主类
    
    提供完整的验证功能，包括：
    - 标准测试用例执行
    - 误差分析和统计
    - 实验数据对比
    - 收敛性验证
    - 结果报告生成
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化验证框架
        
        Args:
            config: 验证配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化子模块
        self.metrics_calculator = MetricsCalculator()
        self.test_cases = StandardTestCases()
        self.data_manager = ExperimentalDataManager()
        
        # 验证结果存储
        self.validation_results: List[ValidationResult] = []
        
        # 配置参数
        self.tolerance_levels = self.config.get('tolerance_levels', {
            'strict': 0.05,    # 5% 误差
            'moderate': 0.10,  # 10% 误差
            'relaxed': 0.20    # 20% 误差
        })
        
        self.output_directory = Path(self.config.get('output_directory', './validation_results'))
        self.output_directory.mkdir(exist_ok=True)
        
        print("✅ 验证框架初始化完成")
        print(f"   输出目录: {self.output_directory}")
        print(f"   容差级别: {list(self.tolerance_levels.keys())}")
    
    def run_validation_suite(self, solver_name: str, solver_instance: Any,
                           test_case_names: Optional[List[str]] = None) -> Dict[str, ValidationResult]:
        """
        运行完整的验证测试套件
        
        Args:
            solver_name: 求解器名称
            solver_instance: 求解器实例
            test_case_names: 要运行的测试用例名称列表（None表示运行所有）
            
        Returns:
            验证结果字典
        """
        print(f"\n🚀 开始验证测试套件: {solver_name}")
        print("=" * 60)
        
        # 获取测试用例
        if test_case_names is None:
            test_case_names = self.test_cases.get_available_test_cases()
        
        results = {}
        
        for test_case_name in test_case_names:
            print(f"\n🔧 执行测试用例: {test_case_name}")
            
            try:
                result = self.run_single_validation(
                    test_case_name, solver_name, solver_instance
                )
                results[test_case_name] = result
                self.validation_results.append(result)
                
                # 输出简要结果
                self._print_validation_summary(result)
                
            except Exception as e:
                self.logger.error(f"测试用例 {test_case_name} 执行失败: {e}")
                print(f"❌ 测试用例 {test_case_name} 执行失败: {e}")
        
        # 生成总体报告
        self._generate_validation_report(results, solver_name)
        
        return results
    
    def run_single_validation(self, test_case_name: str, solver_name: str,
                            solver_instance: Any) -> ValidationResult:
        """
        运行单个验证测试
        
        Args:
            test_case_name: 测试用例名称
            solver_name: 求解器名称
            solver_instance: 求解器实例
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        # 获取测试用例配置和参考数据
        test_config = self.test_cases.get_test_case(test_case_name)
        reference_data = self.data_manager.get_reference_data(test_case_name)
        
        # 配置求解器
        self._configure_solver(solver_instance, test_config)
        
        # 执行仿真
        simulation_data = self._run_simulation(solver_instance, test_config)
        
        # 计算误差指标
        metrics = self.metrics_calculator.calculate_all_metrics(
            simulation_data, reference_data
        )
        
        # 误差分析
        error_analysis = self._perform_error_analysis(
            simulation_data, reference_data, metrics
        )
        
        execution_time = time.time() - start_time
        
        # 检查收敛性
        convergence_achieved = self._check_convergence(simulation_data)
        
        return ValidationResult(
            test_case_name=test_case_name,
            solver_name=solver_name,
            metrics=metrics,
            simulation_data=simulation_data,
            reference_data=reference_data,
            execution_time=execution_time,
            convergence_achieved=convergence_achieved,
            error_analysis=error_analysis
        )
    
    def _configure_solver(self, solver_instance: Any, test_config: Dict[str, Any]) -> None:
        """配置求解器"""
        # 设置几何参数
        if hasattr(solver_instance, 'set_rotor_geometry'):
            geometry = test_config.get('geometry', {})
            solver_instance.set_rotor_geometry(**geometry)
        
        # 设置运行条件
        if hasattr(solver_instance, 'set_operating_conditions'):
            conditions = test_config.get('operating_conditions', {})
            solver_instance.set_operating_conditions(**conditions)
    
    def _run_simulation(self, solver_instance: Any, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行仿真"""
        # 简化的仿真执行
        if hasattr(solver_instance, 'solve'):
            results = solver_instance.solve()
            return {
                'forces': getattr(results, 'forces', np.array([0.0, 0.0, 0.0])),
                'moments': getattr(results, 'moments', np.array([0.0, 0.0, 0.0])),
                'convergence_history': getattr(results, 'convergence_history', []),
                'execution_time': getattr(results, 'execution_time', 0.0)
            }
        else:
            # 模拟数据用于测试
            return {
                'forces': np.array([100.0, 50.0, 200.0]),
                'moments': np.array([10.0, 5.0, 20.0]),
                'convergence_history': [1e-1, 1e-3, 1e-6],
                'execution_time': 1.0
            }
    
    def _perform_error_analysis(self, simulation_data: Dict[str, Any],
                              reference_data: Dict[str, Any],
                              metrics: Dict[str, float]) -> Dict[str, Any]:
        """执行误差分析"""
        analysis = {
            'overall_accuracy': 'good' if metrics.get('rmse', 1.0) < 0.1 else 'poor',
            'force_accuracy': 'good' if metrics.get('force_rmse', 1.0) < 0.15 else 'poor',
            'moment_accuracy': 'good' if metrics.get('moment_rmse', 1.0) < 0.15 else 'poor',
            'tolerance_level': self._determine_tolerance_level(metrics)
        }
        return analysis
    
    def _determine_tolerance_level(self, metrics: Dict[str, float]) -> str:
        """确定容差级别"""
        rmse = metrics.get('rmse', 1.0)
        
        if rmse <= self.tolerance_levels['strict']:
            return 'strict'
        elif rmse <= self.tolerance_levels['moderate']:
            return 'moderate'
        elif rmse <= self.tolerance_levels['relaxed']:
            return 'relaxed'
        else:
            return 'failed'
    
    def _check_convergence(self, simulation_data: Dict[str, Any]) -> bool:
        """检查收敛性"""
        convergence_history = simulation_data.get('convergence_history', [])
        if not convergence_history:
            return False
        
        final_residual = convergence_history[-1]
        return final_residual < 1e-5
    
    def _print_validation_summary(self, result: ValidationResult) -> None:
        """打印验证结果摘要"""
        status = "✅ 通过" if result.error_analysis['tolerance_level'] != 'failed' else "❌ 失败"
        print(f"   {status} - RMSE: {result.metrics.get('rmse', 0.0):.4f}")
        print(f"   执行时间: {result.execution_time:.2f}s")
        print(f"   收敛状态: {'✅' if result.convergence_achieved else '❌'}")
    
    def _generate_validation_report(self, results: Dict[str, ValidationResult],
                                  solver_name: str) -> None:
        """生成验证报告"""
        report_file = self.output_directory / f"{solver_name}_validation_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"验证报告 - {solver_name}\n")
            f.write("=" * 50 + "\n\n")
            
            for test_case_name, result in results.items():
                f.write(f"测试用例: {test_case_name}\n")
                f.write(f"  RMSE: {result.metrics.get('rmse', 0.0):.6f}\n")
                f.write(f"  执行时间: {result.execution_time:.2f}s\n")
                f.write(f"  容差级别: {result.error_analysis['tolerance_level']}\n")
                f.write(f"  收敛状态: {result.convergence_achieved}\n\n")
        
        print(f"\n📄 验证报告已生成: {report_file}")
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要统计"""
        if not self.validation_results:
            return {}
        
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for r in self.validation_results 
                          if r.error_analysis['tolerance_level'] != 'failed')
        
        avg_rmse = np.mean([r.metrics.get('rmse', 0.0) for r in self.validation_results])
        avg_execution_time = np.mean([r.execution_time for r in self.validation_results])
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests / total_tests * 100,
            'average_rmse': avg_rmse,
            'average_execution_time': avg_execution_time
        }
