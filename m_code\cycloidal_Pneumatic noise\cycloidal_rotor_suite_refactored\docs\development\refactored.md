我将创建一个全新的重构版本。让我按照您的要求，从创建 `core` 模块开始。

## 第一步：创建新的目录结构和统一接口

````python path=cycloidal_rotor_suite_refactored/core/__init__.py mode=EDIT
"""
Cycloidal Rotor Suite - 重构版本核心模块
==========================================

提供统一的计算引擎接口和模块化架构

主要模块:
- interfaces: 统一接口定义
- aerodynamics: 气动力学求解器
- acoustics: 声学分析模块
- geometry: 几何建模
- coupling: 多物理场耦合

作者: Refactored Architecture Team
日期: 2025-01-22
"""

from .interfaces import SolverInterface, SolverConfig, SolverResults
from .factories import SolverFactory

__version__ = "2.0.0"
__all__ = [
    "SolverInterface", 
    "SolverConfig", 
    "SolverResults",
    "SolverFactory"
]
````

````python path=cycloidal_rotor_suite_refactored/core/interfaces/__init__.py mode=EDIT
"""
核心接口定义模块
==============

定义所有求解器和模块的统一接口
"""

from .solver_interface import SolverInterface, SolverConfig, SolverResults
from .data_interface import DataExchangeInterface, AerodynamicData, AcousticData
from .coupling_interface import CouplingInterface

__all__ = [
    "SolverInterface", "SolverConfig", "SolverResults",
    "DataExchangeInterface", "AerodynamicData", "AcousticData", 
    "CouplingInterface"
]
````

````python path=cycloidal_rotor_suite_refactored/core/interfaces/solver_interface.py mode=EDIT
"""
统一求解器接口定义
================

定义所有求解器必须实现的标准接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
import numpy as np
from enum import Enum

class FidelityLevel(Enum):
    """保真度级别枚举"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    ULTRA_HIGH = "ultra_high"

class SolverType(Enum):
    """求解器类型枚举"""
    AERODYNAMIC = "aerodynamic"
    ACOUSTIC = "acoustic"
    COUPLING = "coupling"

@dataclass
class SolverConfig:
    """求解器配置基类"""
    fidelity_level: FidelityLevel
    time_step: float
    max_iterations: int = 1000
    convergence_tolerance: float = 1e-6
    enable_gpu: bool = False
    solver_specific_params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """配置验证"""
        if self.time_step <= 0:
            raise ValueError("时间步长必须大于0")
        if self.max_iterations <= 0:
            raise ValueError("最大迭代次数必须大于0")

@dataclass
class SolverResults:
    """求解器结果基类"""
    forces: np.ndarray
    moments: np.ndarray
    time_stamp: float
    convergence_achieved: bool
    iterations_used: int
    computation_time: float
    pressure_distribution: Optional[np.ndarray] = None
    velocity_field: Optional[np.ndarray] = None
    convergence_history: Optional[List[float]] = None
    solver_specific_data: Dict[str, Any] = field(default_factory=dict)

class SolverInterface(ABC):
    """统一求解器接口"""
    
    def __init__(self, config: SolverConfig):
        self.config = config
        self._is_initialized = False
        self._current_time = 0.0
        self._iteration_count = 0
    
    @abstractmethod
    def initialize(self, geometry_data: Dict[str, Any]) -> None:
        """
        初始化求解器
        
        Args:
            geometry_data: 几何数据字典
        """
        pass
    
    @abstractmethod
    def solve_timestep(self, current_time: float, 
                      boundary_conditions: Dict[str, Any]) -> SolverResults:
        """
        求解单个时间步
        
        Args:
            current_time: 当前时间
            boundary_conditions: 边界条件
            
        Returns:
            求解结果
        """
        pass
    
    @abstractmethod
    def finalize(self) -> Dict[str, Any]:
        """
        完成计算并返回最终结果
        
        Returns:
            最终计算结果和统计信息
        """
        pass
    
    @property
    @abstractmethod
    def solver_type(self) -> SolverType:
        """返回求解器类型"""
        pass
    
    @property
    @abstractmethod
    def solver_name(self) -> str:
        """返回求解器名称"""
        pass
    
    @property
    def fidelity_level(self) -> FidelityLevel:
        """返回保真度级别"""
        return self.config.fidelity_level
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._is_initialized
    
    def get_solver_info(self) -> Dict[str, Any]:
        """获取求解器信息"""
        return {
            "name": self.solver_name,
            "type": self.solver_type.value,
            "fidelity": self.fidelity_level.value,
            "initialized": self.is_initialized,
            "current_time": self._current_time,
            "iteration_count": self._iteration_count
        }
    
    def validate_boundary_conditions(self, boundary_conditions: Dict[str, Any]) -> bool:
        """验证边界条件"""
        required_keys = self.get_required_boundary_conditions()
        return all(key in boundary_conditions for key in required_keys)
    
    @abstractmethod
    def get_required_boundary_conditions(self) -> List[str]:
        """获取必需的边界条件键"""
        pass
````

````python path=cycloidal_rotor_suite_refactored/core/interfaces/data_interface.py mode=EDIT
"""
数据交换接口定义
==============

定义模块间数据交换的标准格式和接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import numpy as np

@dataclass
class AerodynamicData:
    """气动数据标准格式"""
    forces: np.ndarray  # 力向量 [Fx, Fy, Fz]
    moments: np.ndarray  # 力矩向量 [Mx, My, Mz]
    pressure: np.ndarray  # 压力分布
    velocity: np.ndarray  # 速度场
    time_stamp: float  # 时间戳
    blade_positions: np.ndarray  # 叶片位置
    circulation: Optional[np.ndarray] = None  # 环量分布
    wake_geometry: Optional[np.ndarray] = None  # 尾迹几何
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AcousticData:
    """声学数据标准格式"""
    sound_pressure: np.ndarray  # 声压
    frequency_spectrum: np.ndarray  # 频谱
    directivity: np.ndarray  # 指向性
    time_stamp: float  # 时间戳
    observer_positions: np.ndarray  # 观测点位置
    source_positions: Optional[np.ndarray] = None  # 声源位置
    phase_data: Optional[np.ndarray] = None  # 相位数据
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class GeometryData:
    """几何数据标准格式"""
    blade_coordinates: np.ndarray  # 叶片坐标
    rotor_center: np.ndarray  # 转子中心
    rotor_radius: float  # 转子半径
    blade_count: int  # 叶片数量
    chord_distribution: np.ndarray  # 弦长分布
    twist_distribution: np.ndarray  # 扭转分布
    mesh_data: Optional[Dict[str, np.ndarray]] = None  # 网格数据
    metadata: Dict[str, Any] = field(default_factory=dict)

class DataExchangeInterface(ABC):
    """数据交换接口"""
    
    @abstractmethod
    def convert_aero_to_acoustic(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """
        将气动数据转换为声学计算输入
        
        Args:
            aero_data: 气动数据
            
        Returns:
            声学计算输入数据
        """
        pass
    
    @abstractmethod
    def validate_data_consistency(self, data: Dict[str, Any]) -> bool:
        """
        验证数据一致性
        
        Args:
            data: 待验证数据
            
        Returns:
            验证结果
        """
        pass
    
    @abstractmethod
    def interpolate_temporal_data(self, data_series: List[Any], 
                                target_times: np.ndarray) -> List[Any]:
        """
        时间插值
        
        Args:
            data_series: 时间序列数据
            target_times: 目标时间点
            
        Returns:
            插值后的数据
        """
        pass
    
    @abstractmethod
    def synchronize_data_timestamps(self, aero_data: List[AerodynamicData],
                                  acoustic_data: List[AcousticData]) -> tuple:
        """
        同步气动和声学数据的时间戳
        
        Args:
            aero_data: 气动数据列表
            acoustic_data: 声学数据列表
            
        Returns:
            同步后的数据元组
        """
        pass

class StandardDataExchange(DataExchangeInterface):
    """标准数据交换实现"""
    
    def __init__(self):
        self.interpolation_method = "linear"
        self.tolerance = 1e-6
    
    def convert_aero_to_acoustic(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """将气动数据转换为声学输入"""
        return {
            "source_strength": self._calculate_source_strength(aero_data),
            "source_positions": aero_data.blade_positions,
            "time_stamp": aero_data.time_stamp,
            "velocity_field": aero_data.velocity,
            "pressure_field": aero_data.pressure
        }
    
    def validate_data_consistency(self, data: Dict[str, Any]) -> bool:
        """验证数据一致性"""
        required_keys = ["time_stamp", "positions"]
        return all(key in data for key in required_keys)
    
    def interpolate_temporal_data(self, data_series: List[Any], 
                                target_times: np.ndarray) -> List[Any]:
        """线性时间插值"""
        # 简化实现，实际应该根据数据类型进行插值
        return data_series  # 占位符实现
    
    def synchronize_data_timestamps(self, aero_data: List[AerodynamicData],
                                  acoustic_data: List[AcousticData]) -> tuple:
        """同步时间戳"""
        # 找到共同的时间范围
        aero_times = [data.time_stamp for data in aero_data]
        acoustic_times = [data.time_stamp for data in acoustic_data]
        
        common_start = max(min(aero_times), min(acoustic_times))
        common_end = min(max(aero_times), max(acoustic_times))
        
        # 过滤数据
        sync_aero = [data for data in aero_data 
                    if common_start <= data.time_stamp <= common_end]
        sync_acoustic = [data for data in acoustic_data 
                        if common_start <= data.time_stamp <= common_end]
        
        return sync_aero, sync_acoustic
    
    def _calculate_source_strength(self, aero_data: AerodynamicData) -> np.ndarray:
        """计算声源强度"""
        # 基于压力梯度计算声源强度
        if aero_data.pressure is not None:
            return np.gradient(aero_data.pressure)
        else:
            # 基于力的简化计算
            return np.linalg.norm(aero_data.forces)
````

````python path=cycloidal_rotor_suite_refactored/core/interfaces/coupling_interface.py mode=EDIT
"""
多物理场耦合接口定义
==================

定义气动-声学耦合计算的标准接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import numpy as np
from .data_interface import AerodynamicData, AcousticData

@dataclass
class CouplingConfig:
    """耦合配置"""
    coupling_method: str  # "one_way", "two_way", "iterative"
    time_synchronization: bool = True
    spatial_interpolation: bool = True
    convergence_tolerance: float = 1e-6
    max_coupling_iterations: int = 10

class CouplingInterface(ABC):
    """多物理场耦合接口"""
    
    def __init__(self, config: CouplingConfig):
        self.config = config
        self._coupling_history: List[Dict[str, Any]] = []
    
    @abstractmethod
    def couple_aero_acoustic(self, aero_data: AerodynamicData,
                           acoustic_data: Optional[AcousticData] = None) -> AcousticData:
        """
        气动-声学耦合计算
        
        Args:
            aero_data: 气动数据
            acoustic_data: 声学数据（双向耦合时需要）
            
        Returns:
            耦合后的声学数据
        """
        pass
    
    @abstractmethod
    def check_coupling_convergence(self, current_data: Dict[str, Any],
                                 previous_data: Dict[str, Any]) -> bool:
        """
        检查耦合收敛性
        
        Args:
            current_data: 当前迭代数据
            previous_data: 前一迭代数据
            
        Returns:
            是否收敛
        """
        pass
    
    @abstractmethod
    def interpolate_between_grids(self, source_data: np.ndarray,
                                source_grid: np.ndarray,
                                target_grid: np.ndarray) -> np.ndarray:
        """
        网格间插值
        
        Args:
            source_data: 源数据
            source_grid: 源网格
            target_grid: 目标网格
            
        Returns:
            插值后的数据
        """
        pass
    
    def get_coupling_history(self) -> List[Dict[str, Any]]:
        """获取耦合历史"""
        return self._coupling_history
    
    def reset_coupling_history(self):
        """重置耦合历史"""
        self._coupling_history.clear()
````

## 第二步：创建工厂模式实现

````python path=cycloidal_rotor_suite_refactored/core/factories/__init__.py mode=EDIT
"""
工厂模式实现模块
==============

提供求解器和组件的创建和管理
"""

from .solver_factory import SolverFactory
from .component_factory import ComponentFactory

__all__ = ["SolverFactory", "ComponentFactory"]
````

````python path=cycloidal_rotor_suite_refactored/core/factories/solver_factory.py mode=EDIT
"""
求解器工厂实现
============

统一的求解器创建和管理接口
"""

from typing import Dict, Type, List, Optional
import logging
from ..interfaces.solver_interface import SolverInterface, SolverConfig, SolverType, FidelityLevel

class SolverRegistry:
    """求解器注册表"""
    
    def __init__(self):
        self._aerodynamic_solvers: Dict[str, Type[SolverInterface]] = {}
        self._acoustic_solvers: Dict[str, Type[SolverInterface]] = {}
        self._coupling_solvers: Dict[str, Type[SolverInterface]] = {}
        self._solver_metadata: Dict[str, Dict[str, any]] = {}
    
    def register_solver(self, solver_name: str, solver_class: Type[SolverInterface],
                       solver_type: SolverType, metadata: Optional[Dict] = None):
        """注册求解器"""
        if solver_type == SolverType.AERODYNAMIC:
            self._aerodynamic_solvers[solver_name] = solver_class
        elif solver_type == SolverType.ACOUSTIC:
            self._acoustic_solvers[solver_name] = solver_class
        elif solver_type == SolverType.COUPLING:
            self._coupling_solvers[solver_name] = solver_class
        
        if metadata:
            self._solver_metadata[solver_name] = metadata
    
    def get_solver_class(self, solver_name: str, solver_type: SolverType) -> Type[SolverInterface]:
        """获取求解器类"""
        if solver_type == SolverType.AERODYNAMIC:
            return self._aerodynamic_solvers.get(solver_name)
        elif solver_type == SolverType.ACOUSTIC:
            return self._acoustic_solvers.get(solver_name)
        elif solver_type == SolverType.COUPLING:
            return self._coupling_solvers.get(solver_name)
        return None
    
    def list_solvers(self, solver_type: Optional[SolverType] = None) -> Dict[str, List[str]]:
        """列出可用求解器"""
        if solver_type == SolverType.AERODYNAMIC:
            return {"aerodynamic": list(self._aerodynamic_solvers.keys())}
        elif solver_type == SolverType.ACOUSTIC:
            return {"acoustic": list(self._acoustic_solvers.keys())}
        elif solver_type == SolverType.COUPLING:
            return {"coupling": list(self._coupling_solvers.keys())}
        else:
            return {
                "aerodynamic": list(self._aerodynamic_solvers.keys()),
                "acoustic": list(self._acoustic_solvers.keys()),
                "coupling": list(self._coupling_solvers.keys())
            }

class SolverFactory:
    """求解器工厂"""
    
    def __init__(self):
        self.registry = SolverRegistry()
        self.logger = logging.getLogger(__name__)
        self._setup_default_solvers()
    
    def _setup_default_solvers(self):
        """设置默认求解器映射"""
        # 这里将在后续步骤中注册具体的求解器
        pass
    
    def create_solver(self, solver_name: str, solver_type: SolverType,
                     config: SolverConfig) -> SolverInterface:
        """
        创建求解器实例
        
        Args:
            solver_name: 求解器名称
            solver_type: 求解器类型
            config: 求解器配置
            
        Returns:
            求解器实例
        """
        solver_class = self.registry.get_solver_class(solver_name, solver_type)
        
        if solver_class is None:
            available = self.registry.list_solvers(solver_type)
            raise ValueError(
                f"未找到求解器: {solver_name} (类型: {solver_type.value})\n"
                f"可用求解器: {available}"
            )
        
        try:
            solver = solver_class(config)
            self.logger.info(f"成功创建求解器: {solver_name} ({solver_type.value})")
            return solver
        except Exception as e:
            self.logger.error(f"创建求解器失败: {solver_name} - {str(e)}")
            raise
    
    def create_aerodynamic_solver(self, solver_name: str, config: SolverConfig) -> SolverInterface:
        """创建气动求解器"""
        return self.create_solver(solver_name, SolverType.AERODYNAMIC, config)
    
    def create_acoustic_solver(self, solver_name: str, config: SolverConfig) -> SolverInterface:
        """创建声学求解器"""
        return self.create_solver(solver_name, SolverType.ACOUSTIC, config)
    
    def create_coupling_solver(self, solver_name: str, config: SolverConfig) -> SolverInterface:
        """创建耦合求解器"""
        return self.create_solver(solver_name, SolverType.COUPLING, config)
    
    def register_solver(self, solver_name: str, solver_class: Type[SolverInterface],
                       solver_type: SolverType, metadata: Optional[Dict] = None):
        """注册新求解器"""
        self.registry.register_solver(solver_name, solver_class, solver_type, metadata)
        self.logger.info(f"注册求解器: {solver_name} ({solver_type.value})")
    
    def get_available_solvers(self) -> Dict[str, List[str]]:
        """获取可用求解器列表"""
        return self.registry.list_solvers()
    
    def recommend_solver(self, requirements: Dict[str, any]) -> str:
        """
        根据需求推荐求解器
        
        Args:
            requirements: 需求字典
                - fidelity_level: 保真度要求
                - computational_budget: 计算预算
                - accuracy_requirement: 精度要求
                - problem_type: 问题类型
        
        Returns:
            推荐的求解器名称
        """
        fidelity = requirements.get('fidelity_level', FidelityLevel.MEDIUM)
        problem_type = requirements.get('problem_type', 'cycloidal_rotor')
        
        # 基于保真度和问题类型的推荐逻辑
        if fidelity == FidelityLevel.LOW:
            return "BEMT"
        elif fidelity == FidelityLevel.MEDIUM:
            return "LiftingLine"
        elif fidelity in [FidelityLevel.HIGH, FidelityLevel.ULTRA_HIGH]:
            return "UVLM"
        else:
            return "BEMT"  # 默认选择

# 全局工厂实例
_global_factory = None

def get_solver_factory() -> SolverFactory:
    """获取全局求解器工厂实例"""
    global _global_factory
    if _global_factory is None:
        _global_factory = SolverFactory()
    return _global_factory

def create_solver(solver_name: str, solver_type: SolverType, config: SolverConfig) -> SolverInterface:
    """便捷函数：创建求解器"""
    factory = get_solver_factory()
    return factory.create_solver(solver_name, solver_type, config)
````

````python path=cycloidal_rotor_suite_refactored/core/factories/component_factory.py mode=EDIT
"""
组件工厂实现
==========

创建和管理各种计算组件
"""

from typing import Dict, Type, Any, Optional
from ..interfaces.data_interface import DataExchangeInterface, StandardDataExchange
from ..interfaces.coupling_interface import CouplingInterface

class ComponentFactory:
    """组件工厂"""
    
    def __init__(self):
        self._data_exchange_classes: Dict[str, Type[DataExchangeInterface]] = {}
        self._coupling_classes: Dict[str, Type[CouplingInterface]] = {}
        self._setup_default_components()
    
    def _setup_default_components(self):
        """设置默认组件"""
        self.register_data_exchange("standard", StandardDataExchange)
    
    def register_data_exchange(self, name: str, exchange_class: Type[DataExchangeInterface]):
        """注册数据交换组件"""
        self._data_exchange_classes[name] = exchange_class
    
    def register_coupling(self, name: str, coupling_class: Type[CouplingInterface]):
        """注册耦合组件"""
        self._coupling_classes[name] = coupling_class
    
    def create_data_exchange(self, name: str = "standard", **kwargs) -> DataExchangeInterface:
        """创建数据交换组件"""
        if name not in self._data_exchange_classes:
            raise ValueError(f"未知的数据交换组件: {name}")
        
        exchange_class = self._data_exchange_classes[name]
        return exchange_class(**kwargs)
    
    def create_coupling(self, name: str, config: Any, **kwargs) -> CouplingInterface:
        """创建耦合组件"""
        if name not in self._coupling_classes:
            raise ValueError(f"未知的耦合组件: {name}")
        
        coupling_class = self._coupling_classes[name]
        return coupling_class(config, **kwargs)
    
    def get_available_components(self) -> Dict[str, list]:
        """获取可用组件列表"""
        return {
            "data_exchange": list(self._data_exchange_classes.keys()),
            "coupling": list(self._coupling_classes.keys())
        }
````

## 第三步：创建气动力学求解器模块

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/__init__.py mode=EDIT
"""
气动力学求解器模块
================

提供多保真度气动力学求解器实现
"""

from .base import AerodynamicSolverBase
from .solvers import BEMTSolver, UVLMSolver, LiftingLineSolver

__all__ = [
    "AerodynamicSolverBase",
    "BEMTSolver", 
    "UVLMSolver", 
    "LiftingLineSolver"
]
````

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/base.py mode=EDIT
"""
气动力学求解器基类
================

定义所有气动力学求解器的通用接口和功能
"""

from abc import abstractmethod
from typing import Dict, Any, List
import numpy as np
from ..interfaces.solver_interface import SolverInterface, SolverConfig, SolverResults, SolverType

class AerodynamicSolverBase(SolverInterface):
    """气动力学求解器基类"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.rotor_geometry = None
        self.blade_elements = None
        self.wake_data = None
        self._force_history = []
        self._moment_history = []
    
    @property
    def solver_type(self) -> SolverType:
        """返回求解器类型"""
        return SolverType.AERODYNAMIC
    
    def initialize(self, geometry_data: Dict[str, Any]) -> None:
        """初始化气动求解器"""
        self.rotor_geometry = geometry_data
        self._setup_blade_elements(geometry_data)
        self._initialize_wake()
        self._is_initialized = True
    
    @abstractmethod
    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置叶片单元"""
        pass
    
    @abstractmethod
    def _initialize_wake(self) -> None:
        """初始化尾迹"""
        pass
    
    @abstractmethod
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        pass
    
    @abstractmethod
    def _compute_blade_loads(self, velocity_field: np.ndarray, 
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        pass
    
    def solve_timestep(self, current_time: float, 
                      boundary_conditions: Dict[str, Any]) -> SolverResults:
        """求解单个时间步"""
        if not self._is_initialized:
            raise RuntimeError("求解器未初始化")
        
        self._current_time = current_time
        
        # 获取边界条件
        rotor_rpm = boundary_conditions.get('rotor_rpm', 0.0)
        freestream_velocity = boundary_conditions.get('freestream_velocity', np.zeros(3))
        blade_pitch = boundary_conditions.get('blade_pitch', 0.0)
        
        # 计算叶片位置
        blade_positions = self._compute_blade_positions(current_time, rotor_rpm)
        
        # 计算诱导速度
        induced_velocity = self._compute_induced_velocity(blade_positions)
        
        # 计算总速度场
        total_velocity = freestream_velocity + induced_velocity
        
        # 计算叶片载荷
        forces, moments = self._compute_blade_loads(total_velocity, blade_positions)
        
        # 更新尾迹
        self._update_wake(blade_positions, induced_velocity)
        
        # 记录历史
        self._force_history.append(forces)
        self._moment_history.append(moments)
        
        # 检查收敛性
        convergence_achieved = self._check_convergence()
        
        return SolverResults(
            forces=forces,
            moments=moments,
            time_stamp=current_time,
            convergence_achieved=convergence_achieved,
            iterations_used=self._iteration_count,
            computation_time=0.0,  # 需要实际计时
            velocity_field=total_velocity,
            solver_specific_data={
                'blade_positions': blade_positions,
                'induced_velocity': induced_velocity,
                'wake_data': self.wake_data
            }
        )
    
    def _compute_blade_positions(self, time: float, rpm: float) -> np.ndarray:
        """计算叶片位置"""
        if self.rotor_geometry is None:
            raise RuntimeError("转子几何未初始化")
        
        blade_count = self.rotor_geometry.get('blade_count', 3)
        rotor_radius = self.rotor_geometry.get('rotor_radius', 1.0)
        
        angular_velocity = rpm * 2 * np.pi / 60  # 转换为rad/s
        
        positions = []
        for i in range(blade_count):
            angle = angular_velocity * time + i * 2 * np.pi / blade_count
            x = rotor_radius * np.cos(angle)
            y = rotor_radius * np.sin(angle)
            z = 0.0
            positions.append([x, y, z])
        
        return np.array(positions)
    
    @abstractmethod
    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹"""
        pass
    
    def _check_convergence(self) -> bool:
        """检查收敛性"""
        if len(self._force_history) < 2:
            return False
        
        current_force = self._force_history[-1]
        previous_force = self._force_history[-2]
        
        relative_change = np.linalg.norm(current_force - previous_force) / np.linalg.norm(current_force)
        return relative_change < self.config.convergence_tolerance
    
    def get_required_boundary_conditions(self) -> List[str]:
        """获取必需的边界条件"""
        return ['rotor_rpm', 'freestream_velocity', 'blade_pitch']
    
    def finalize(self) -> Dict[str, Any]:
        """完成计算"""
        return {
            'total_iterations': self._iteration_count,
            'force_history': np.array(self._force_history),
            'moment_history': np.array(self._moment_history),
            'final_wake_data': self.wake_data
        }
````

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/solvers/__init__.py mode=EDIT
"""
气动力学求解器实现
================

包含各种保真度的气动力学求解器
"""

from .bemt_solver import BEMTSolver
from .uvlm_solver import UVLMSolver  
from .lifting_line_solver import LiftingLineSolver

__all__ = ["BEMTSolver", "UVLMSolver", "LiftingLineSolver"]
````

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/solvers/bemt_solver.py mode=EDIT
"""
BEMT求解器实现
=============

叶素动量理论求解器 - 低保真度快速计算
"""

import numpy as np
from typing import Dict, Any
from ..base import AerodynamicSolverBase
from ...interfaces.solver_interface import SolverConfig, FidelityLevel

class BEMTSolver(AerodynamicSolverBase):
    """叶素动量理论求解器"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.blade_elements_count = 20
        self.airfoil_data = None
        self.induction_factors = None
    
    @property
    def solver_name(self) -> str:
        return "BEMT"
    
    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置叶片单元"""
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        blade_count = geometry_data.get('blade_count', 3)
        
        # 创建径向分布
        r_stations = np.linspace(0.2 * rotor_radius, rotor_radius, self.blade_elements_count)
        
        # 获取弦长和扭转分布
        chord_distribution = geometry_data.get('chord_distribution', 
                                             np.ones(self.blade_elements_count) * 0.1)
        twist_distribution = geometry_data.get('twist_distribution',
                                             np.zeros(self.blade_elements_count))
        
        self.blade_elements = {
            'r_stations': r_stations,
            'chord': chord_distribution,
            'twist': twist_distribution,
            'blade_count': blade_count
        }
        
        # 初始化诱导因子
        self.induction_factors = {
            'axial': np.zeros(self.blade_elements_count),
            'tangential': np.zeros(self.blade_elements_count)
        }
    
    def _initialize_wake(self) -> None:
        """初始化尾迹（BEMT中简化处理）"""
        self.wake_data = {
            'type': 'momentum_theory',
            'induction_factors': self.induction_factors
        }
    
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        # BEMT中使用动量理论计算诱导速度
        r_stations = self.blade_elements['r_stations']
        axial_induction = self.induction_factors['axial']
        tangential_induction = self.induction_factors['tangential']
        
        # 简化的诱导速度计算
        induced_velocity = np.zeros_like(blade_positions)
        
        for i, pos in enumerate(blade_positions):
            r = np.linalg.norm(pos[:2])  # 径向距离
            
            # 插值获取该位置的诱导因子
            a = np.interp(r, r_stations, axial_induction)
            a_prime = np.interp(r, r_stations, tangential_induction)
            
            # 计算诱导速度分量
            induced_velocity[i, 0] = 0.0  # 轴向
            induced_velocity[i, 1] = 0.0  # 径向
            induced_velocity[i, 2] = -a * 10.0  # 简化的轴向诱导速度
        
        return induced_velocity
    
    def _compute_blade_loads(self, velocity_field: np.ndarray, 
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        total_force = np.zeros(3)
        total_moment = np.zeros(3)
        
        blade_count = self.blade_elements['blade_count']
        r_stations = self.blade_elements['r_stations']
        chord = self.blade_elements['chord']
        twist = self.blade_elements['twist']
        
        # 空气密度
        rho = 1.225  # kg/m³
        
        for i, pos in enumerate(blade_positions):
            r = np.linalg.norm(pos[:2])
            
            # 插值获取该位置的几何参数
            c = np.interp(r, r_stations, chord)
            theta = np.interp(r, r_stations, twist)
            
            # 计算相对速度
            V_rel = velocity_field[i]
            V_mag = np.linalg.norm(V_rel)
            
            if V_mag < 1e-6:
                continue
            
            # 简化的升力和阻力计算
            alpha = theta  # 简化的攻角计算
            Cl = 2 * np.pi * alpha  # 线性升力系数
            Cd = 0.01  # 简化的阻力系数
            
            # 计算升力和阻力
            q = 0.5 * rho * V_mag**2
            L = q * c * Cl
            D = q * c * Cd
            
            # 转换到全局坐标系
            force_local = np.array([0, L, D])
            moment_local = np.array([0, 0, L * r])
            
            total_force += force_local
            total_moment += moment_local
        
        return total_force, total_moment
    
    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹（BEMT中更新诱导因子）"""
        # 基于当前载荷更新诱导因子
        # 这里是简化实现，实际应该迭代求解
        
        r_stations = self.blade_elements['r_stations']
        
        for i, r in enumerate(r_stations):
            # 简化的诱导因子更新
            self.induction_factors['axial'][i] *= 0.9  # 阻尼更新
            self.induction_factors['tangential'][i] *= 0.9
        
        self.wake_data['induction_factors'] = self.induction_factors
        self._iteration_count += 1
````

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/solvers/uvlm_solver.py mode=EDIT
"""
UVLM求解器实现
=============

非定常涡格法求解器 - 高保真度计算
"""

import numpy as np
from typing import Dict, Any, List
from ..base import AerodynamicSolverBase
from ...interfaces.solver_interface import SolverConfig, FidelityLevel

class UVLMSolver(AerodynamicSolverBase):
    """非定常涡格法求解器"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.vortex_panels = None
        self.wake_panels = None
        self.circulation_history = []
        self.panel_count_chordwise = 10
        self.panel_count_spanwise = 20
    
    @property
    def solver_name(self) -> str:
        return "UVLM"
    
    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置涡格面板"""
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        blade_count = geometry_data.get('blade_count', 3)
        chord_distribution = geometry_data.get('chord_distribution', 
                                             np.ones(self.panel_count_spanwise) * 0.1)
        
        self.blade_elements = {
            'rotor_radius': rotor_radius,
            'blade_count': blade_count,
            'chord_distribution': chord_distribution
        }
        
        # 创建涡格面板
        self._create_vortex_panels()
    
    def _create_vortex_panels(self):
        """创建涡格面板"""
        blade_count = self.blade_elements['blade_count']
        rotor_radius = self.blade_elements['rotor_radius']
        
        panels = []
        
        for blade_idx in range(blade_count):
            blade_angle = blade_idx * 2 * np.pi / blade_count
            
            # 径向分布
            r_stations = np.linspace(0.2 * rotor_radius, rotor_radius, self.panel_count_spanwise)
            
            # 弦向分布
            for i in range(self.panel_count_spanwise - 1):
                for j in range(self.panel_count_chordwise - 1):
                    # 面板四个角点
                    r1, r2 = r_stations[i], r_stations[i + 1]
                    chord = self.blade_elements['chord_distribution'][i]
                    
                    x1 = j * chord / self.panel_count_chordwise
                    x2 = (j + 1) * chord / self.panel_count_chordwise
                    
                    # 转换到全局坐标系
                    panel = {
                        'corners': self._transform_panel_to_global(
                            [[x1, r1, 0], [x2, r1, 0], [x2, r2, 0], [x1, r2, 0]], 
                            blade_angle
                        ),
                        'blade_idx': blade_idx,
                        'circulation': 0.0,
                        'control_point': None
                    }
                    
                    # 计算控制点
                    panel['control_point'] = np.mean(panel['corners'], axis=0)
                    panels.append(panel)
        
        self.vortex_panels = panels
    
    def _transform_panel_to_global(self, local_coords: List[List[float]], blade_angle: float) -> np.ndarray:
        """将面板坐标转换到全局坐标系"""
        coords = np.array(local_coords)
        
        # 旋转矩阵
        cos_theta = np.cos(blade_angle)
        sin_theta = np.sin(blade_angle)
        
        rotation_matrix = np.array([
            [cos_theta, -sin_theta, 0],
            [sin_theta, cos_theta, 0],
            [0, 0, 1]
        ])
        
        return np.dot(coords, rotation_matrix.T)
    
    def _initialize_wake(self) -> None:
        """初始化尾迹"""
        self.wake_panels = []
        self.wake_data = {
            'type': 'vortex_lattice',
            'panels': self.wake_panels,
            'circulation_history': self.circulation_history
        }
    
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        if not self.vortex_panels:
            return np.zeros_like(blade_positions)
        
        induced_velocity = np.zeros_like(blade_positions)
        
        # 计算每个控制点的诱导速度
        for i, pos in enumerate(blade_positions):
            velocity = np.zeros(3)
            
            # 来自所有涡格面板的诱导
            for panel in self.vortex_panels:
                if panel['circulation'] != 0:
                    panel_velocity = self._compute_panel_induced_velocity(
                        pos, panel['corners'], panel['circulation']
                    )
                    velocity += panel_velocity
            
            # 来自尾迹的诱导
            for wake_panel in self.wake_panels:
                if wake_panel['circulation'] != 0:
                    wake_velocity = self._compute_panel_induced_velocity(
                        pos, wake_panel['corners'], wake_panel['circulation']
                    )
                    velocity += wake_velocity
            
            induced_velocity[i] = velocity
        
        return induced_velocity
    
    def _compute_panel_induced_velocity(self, point: np.ndarray, 
                                      panel_corners: np.ndarray, 
                                      circulation: float) -> np.ndarray:
        """计算单个面板在指定点的诱导速度"""
        # 使用Biot-Savart定律计算涡线段的诱导速度
        velocity = np.zeros(3)
        
        for i in range(4):
            p1 = panel_corners[i]
            p2 = panel_corners[(i + 1) % 4]
            
            # 涡线段诱导速度
            r1 = point - p1
            r2 = point - p2
            r1_mag = np.linalg.norm(r1)
            r2_mag = np.linalg.norm(r2)
            
            if r1_mag < 1e-10 or r2_mag < 1e-10:
                continue
            
            r1_cross_r2 = np.cross(r1, r2)
            r1_cross_r2_mag = np.linalg.norm(r1_cross_r2)
            
            if r1_cross_r2_mag < 1e-10:
                continue
            
            # Biot-Savart公式
            cos_alpha = np.dot(r1, r2) / (r1_mag * r2_mag)
            sin_alpha = r1_cross_r2_mag / (r1_mag * r2_mag)
            
            if sin_alpha > 1e-10:
                velocity += (circulation / (4 * np.pi)) * (r1_cross_r2 / r1_cross_r2_mag**2) * \
                           (r1_mag + r2_mag) * sin_alpha
        
        return velocity
    
    def _compute_blade_loads(self, velocity_field: np.ndarray, 
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        # 求解环量分布
        self._solve_circulation_distribution(velocity_field)
        
        # 计算力和力矩
        total_force = np.zeros(3)
        total_moment = np.zeros(3)
        
        rho = 1.225  # 空气密度
        
        for panel in self.vortex_panels:
            if panel['circulation'] == 0:
                continue
            
            # 使用Kutta-Joukowski定理计算力
            control_point = panel['control_point']
            panel_idx = self._find_nearest_velocity_point(control_point, blade_positions)
            
            if panel_idx >= 0:
                local_velocity = velocity_field[panel_idx]
                
                # 计算面板法向量
                corners = panel['corners']
                v1 = corners[1] - corners[0]
                v2 = corners[3] - corners[0]
                normal = np.cross(v1, v2)
                normal = normal / np.linalg.norm(normal)
                
                # Kutta-Joukowski定理
                force_per_unit_span = rho * panel['circulation'] * np.cross(local_velocity, normal)
                
                # 面板面积
                panel_area = 0.5 * np.linalg.norm(np.cross(v1, v2))
                force = force_per_unit_span * panel_area
                
                total_force += force
                
                # 计算力矩
                moment_arm = control_point
                total_moment += np.cross(moment_arm, force)
        
        return total_force, total_moment
    
    def _solve_circulation_distribution(self, velocity_field: np.ndarray):
        """求解环量分布"""
        n_panels = len(self.vortex_panels)
        
        if n_panels == 0:
            return
        
        # 构建影响系数矩阵
        A = np.zeros((n_panels, n_panels))
        b = np.zeros(n_panels)
        
        for i, panel_i in enumerate(self.vortex_panels):
            control_point = panel_i['control_point']
            
            # 计算法向量
            corners = panel_i['corners']
            v1 = corners[1] - corners[0]
            v2 = corners[3] - corners[0]
            normal = np.cross(v1, v2)
            normal = normal / np.linalg.norm(normal)
            
            # 外流速度在法向的分量
            velocity_idx = self._find_nearest_velocity_point(control_point, 
                                                           np.array([p['control_point'] for p in self.vortex_panels]))
            if velocity_idx >= 0 and velocity_idx < len(velocity_field):
                external_velocity = velocity_field[velocity_idx]
                b[i] = -np.dot(external_velocity, normal)
            
            # 影响系数
            for j, panel_j in enumerate(self.vortex_panels):
                if i == j:
                    A[i, j] = 0.5  # 自诱导项
                else:
                    induced_vel = self._compute_panel_induced_velocity(
                        control_point, panel_j['corners'], 1.0
                    )
                    A[i, j] = np.dot(induced_vel, normal)
        
        # 求解线性系统
        try:
            circulation = np.linalg.solve(A, b)
            for i, panel in enumerate(self.vortex_panels):
                panel['circulation'] = circulation[i]
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用最小二乘解
            circulation = np.linalg.lstsq(A, b, rcond=None)[0]
            for i, panel in enumerate(self.vortex_panels):
                panel['circulation'] = circulation[i]
    
    def _find_nearest_velocity_point(self, point: np.ndarray, velocity_points: np.ndarray) -> int:
        """找到最近的速度点索引"""
        if len(velocity_points) == 0:
            return -1
        
        distances = np.linalg.norm(velocity_points - point, axis=1)
        return np.argmin(distances)
    
    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹"""
        # 保存当前环量分布
        current_circulation = [panel['circulation'] for panel in self.vortex_panels]
        self.circulation_history.append(current_circulation)
        
        # 脱落新的尾迹面板
        self._shed_wake_panels()
        
        # 对流现有尾迹
        self._convect_wake(velocity)
        
        self.wake_data['circulation_history'] = self.circulation_history
        self._iteration_count += 1
    
    def _shed_wake_panels(self):
        """脱落尾迹面板"""
        # 从叶片后缘脱落尾迹
        # 这里是简化实现
        pass
    
    def _convect_wake(self, velocity: np.ndarray):
        """对流尾迹"""
        # 根据局部速度对流尾迹面板
        # 这里是简化实现
        pass
````

````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/solvers/lifting_line_solver.py mode=EDIT
"""
升力线求解器实现
==============

升力线理论求解器 - 中等保真度计算
"""

import numpy as np
from typing import Dict, Any
from ..base import AerodynamicSolverBase
from ...interfaces.solver_interface import SolverConfig, FidelityLevel

class LiftingLineSolver(AerodynamicSolverBase):
    """升力线理论求解器"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.lifting_line_stations = 30
        self.circulation_distribution = None
        self.bound_vortex_strength = None
    
    @property
    def solver_name(self) -> str:
        return "LiftingLine"
    
    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置升力线站点"""
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        blade_count = geometry_data.get('blade_count', 3)
        
        # 创建升力线站点
        r_stations = np.linspace(0.1 * rotor_radius, rotor_radius, self.lifting_line_stations)
        
        # 获取几何分布
        chord_distribution = geometry_data.get('chord_distribution', 
                                             np.ones(self.lifting_line_stations) * 0.1)
        twist_distribution = geometry_data.get('twist_distribution',
                                             np.zeros(self.lifting_line_stations))
        
        self.blade_elements = {
            'r_stations': r_stations,
            'chord': chord_distribution,
            'twist': twist_distribution,
            'blade_count': blade_count,
            'rotor_radius': rotor_radius
        }
        
        # 初始化环量分布
        self.circulation_distribution = np.zeros(self.lifting_line_stations)
        self.bound_vortex_strength = np.zeros(self.lifting_line_stations)
    
    def _initialize_wake(self) -> None:
        """初始化尾迹"""
        self.wake_data = {
            'type': 'lifting_line',
            'circulation_distribution': self.circulation_distribution,
            'wake_vortices': []
        }
    
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        induced_velocity = np.zeros_like(blade_positions)
        
        r_stations = self.blade_elements['r_stations']
        blade_count = self.blade_elements['blade_count']
        
        for i, pos in enumerate(blade_positions):
            velocity = np.zeros(3)
            
            # 来自所有叶片的诱导
            for blade_idx in range(blade_count):
                blade_angle = blade_idx * 2 * np.pi / blade_count
                
                # 计算该叶片上各站点的诱导
                for j, r in enumerate(r_stations):
                    if self.circulation_distribution[j] == 0:
                        continue
                    
                    # 站点位置
                    station_pos = np.array([
                        r * np.cos(blade_angle),
                        r * np.sin(blade_angle),
                        0.0
                    ])
                    
                    # 计算诱导速度
                    station_velocity = self._compute_vortex_induced_velocity(
                        pos, station_pos, self.circulation_distribution[j]
                    )
                    velocity += station_velocity
            
            # 来自尾迹的诱导
            velocity += self._compute_wake_induced_velocity(pos)
            
            induced_velocity[i] = velocity
        
        return induced_velocity
    
    def _compute_vortex_induced_velocity(self, field_point: np.ndarray, 
                                       vortex_point: np.ndarray, 
                                       circulation: float) -> np.ndarray:
        """计算涡线诱导速度"""
        r_vec = field_point - vortex_point
        r_mag = np.linalg.norm(r_vec)
        
        if r_mag < 1e-10:
            return np.zeros(3)
        
        # 简化的涡线诱导速度（假设为无限长直涡线）
        # 实际应该考虑有限长度和几何形状
        vortex_direction = np.array([0, 0, 1])  # 假设涡线沿z轴
        
        velocity = (circulation / (2 * np.pi)) * np.cross(vortex_direction, r_vec) / r_mag**2
        
        return velocity
    
    def _compute_wake_induced_velocity(self, field_point: np.ndarray) -> np.ndarray:
        """计算尾迹诱导速度"""
        # 简化的尾迹诱导计算
        wake_velocity = np.zeros(3)
        
        for wake_vortex in self.wake_data.get('wake_vortices', []):
            wake_pos = wake_vortex['position']
            wake_strength = wake_vortex['strength']
            
            vortex_velocity = self._compute_vortex_induced_velocity(
                field_point, wake_pos, wake_strength
            )
            wake_velocity += vortex_velocity
        
        return wake_velocity
    
    def _compute_blade_loads(self, velocity_field: np.ndarray, 
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        # 求解环量分布
        self._solve_lifting_line_equation(velocity_field, blade_positions)
        
        # 计算力和力矩
        total_force = np.zeros(3)
        total_moment = np.zeros(3)
        
        r_stations = self.blade_elements['r_stations']
        chord = self.blade_elements['chord']
        blade_count = self.blade_elements['blade_count']
        rho = 1.225  # 空气密度
        
        for i, r in enumerate(r_stations):
            if i >= len(self.circulation_distribution):
                continue
            
            circulation = self.circulation_distribution[i]
            if circulation == 0:
                continue
            
            # 找到对应的速度
            station_velocity = self._interpolate_velocity_at_station(
                r, velocity_field, blade_positions
            )
            
            # 使用Kutta-Joukowski定理计算升力
            lift_per_unit_span = rho * circulation * np.linalg.norm(station_velocity)
            
            # 计算站点间距
            if i < len(r_stations) - 1:
                dr = r_stations[i + 1] - r_stations[i]
            else:
                dr = r_stations[i] - r_stations[i - 1]
            
            # 总升力
            lift = lift_per_unit_span * dr * blade_count
            
            # 升力方向（垂直于相对速度）
            if np.linalg.norm(station_velocity) > 1e-6:
                lift_direction = np.array([0, 0, 1])  # 简化为z方向
                force = lift * lift_direction
            else:
                force = np.zeros(3)
            
            total_force += force
            
            # 计算力矩
            moment_arm = np.array([r, 0, 0])  # 简化的力臂
            total_moment += np.cross(moment_arm, force)
        
        return total_force, total_moment
    
    def _solve_lifting_line_equation(self, velocity_field: np.ndarray, 
                                   blade_positions: np.ndarray):
        
````python path=cycloidal_rotor_suite_refactored/core/aerodynamics/solvers/lifting_line_solver.py mode=EDIT
    def _solve_lifting_line_equation(self, velocity_field: np.ndarray, 
                                   blade_positions: np.ndarray):
        """求解升力线方程"""
        n_stations = len(self.blade_elements['r_stations'])
        r_stations = self.blade_elements['r_stations']
        chord = self.blade_elements['chord']
        twist = self.blade_elements['twist']
        
        # 构建升力线方程系统
        A = np.zeros((n_stations, n_stations))
        b = np.zeros(n_stations)
        
        for i in range(n_stations):
            r_i = r_stations[i]
            c_i = chord[i]
            theta_i = twist[i]
            
            # 获取该站点的速度
            station_velocity = self._interpolate_velocity_at_station(
                r_i, velocity_field, blade_positions
            )
            
            # 计算攻角和升力系数
            V_mag = np.linalg.norm(station_velocity)
            if V_mag < 1e-6:
                continue
            
            # 简化的攻角计算
            alpha = theta_i  # 实际应该考虑诱导角
            Cl = 2 * np.pi * alpha  # 线性升力系数
            
            # 右端项（来自外流的环量需求）
            b[i] = 0.5 * V_mag * c_i * Cl
            
            # 影响系数矩阵
            for j in range(n_stations):
                r_j = r_stations[j]
                
                if i == j:
                    # 自诱导项
                    A[i, j] = 1.0 + (c_i * Cl) / (4 * np.pi * r_i)
                else:
                    # 相互诱导项
                    distance = abs(r_i - r_j)
                    if distance > 1e-10:
                        A[i, j] = 1.0 / (4 * np.pi * distance)
                    else:
                        A[i, j] = 0.0
        
        # 求解线性系统
        try:
            self.circulation_distribution = np.linalg.solve(A, b)
        except np.linalg.LinAlgError:
            # 使用最小二乘解
            self.circulation_distribution = np.linalg.lstsq(A, b, rcond=None)[0]
        
        # 更新束缚涡强度
        self.bound_vortex_strength = self.circulation_distribution.copy()
    
    def _interpolate_velocity_at_station(self, r: float, 
                                       velocity_field: np.ndarray,
                                       blade_positions: np.ndarray) -> np.ndarray:
        """在指定径向位置插值速度"""
        if len(blade_positions) == 0:
            return np.zeros(3)
        
        # 找到最近的叶片位置
        distances = []
        for pos in blade_positions:
            pos_r = np.linalg.norm(pos[:2])
            distances.append(abs(pos_r - r))
        
        nearest_idx = np.argmin(distances)
        
        if nearest_idx < len(velocity_field):
            return velocity_field[nearest_idx]
        else:
            return np.zeros(3)
    
    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹"""
        # 脱落新的尾迹涡
        self._shed_wake_vortices(blade_positions)
        
        # 对流现有尾迹
        self._convect_wake_vortices(velocity)
        
        # 更新尾迹数据
        self.wake_data['circulation_distribution'] = self.circulation_distribution
        self._iteration_count += 1
    
    def _shed_wake_vortices(self, blade_positions: np.ndarray):
        """脱落尾迹涡"""
        r_stations = self.blade_elements['r_stations']
        blade_count = self.blade_elements['blade_count']
        
        # 计算环量梯度（脱落涡强度）
        circulation_gradient = np.gradient(self.circulation_distribution)
        
        for blade_idx in range(blade_count):
            blade_angle = blade_idx * 2 * np.pi / blade_count
            
            for i, r in enumerate(r_stations):
                if abs(circulation_gradient[i]) > 1e-10:
                    # 创建新的尾迹涡
                    wake_vortex = {
                        'position': np.array([
                            r * np.cos(blade_angle),
                            r * np.sin(blade_angle),
                            0.0
                        ]),
                        'strength': -circulation_gradient[i],  # 负号表示脱落
                        'age': 0,
                        'blade_idx': blade_idx
                    }
                    
                    self.wake_data['wake_vortices'].append(wake_vortex)
    
    def _convect_wake_vortices(self, velocity: np.ndarray):
        """对流尾迹涡"""
        dt = self.config.time_step
        
        for wake_vortex in self.wake_data['wake_vortices']:
            # 获取该位置的速度
            vortex_pos = wake_vortex['position']
            
            # 简化的速度插值
            if len(velocity) > 0:
                local_velocity = velocity[0]  # 简化处理
            else:
                local_velocity = np.array([10.0, 0.0, 0.0])  # 默认对流速度
            
            # 更新位置
            wake_vortex['position'] += local_velocity * dt
            wake_vortex['age'] += 1
        
        # 移除老化的尾迹涡
        max_age = 100  # 最大年龄
        self.wake_data['wake_vortices'] = [
            vortex for vortex in self.wake_data['wake_vortices'] 
            if vortex['age'] < max_age
        ]
````

## 第四步：创建声学求解器模块

````python path=cycloidal_rotor_suite_refactored/core/acoustics/__init__.py mode=EDIT
"""
声学求解器模块
============

提供多保真度声学分析求解器实现
"""

from .base import AcousticSolverBase
from .solvers import FWHSolver, BPMSolver

__all__ = [
    "AcousticSolverBase",
    "FWHSolver", 
    "BPMSolver"
]
````

````python path=cycloidal_rotor_suite_refactored/core/acoustics/base.py mode=EDIT
"""
声学求解器基类
============

定义所有声学求解器的通用接口和功能
"""

from abc import abstractmethod
from typing import Dict, Any, List
import numpy as np
from ..interfaces.solver_interface import SolverInterface, SolverConfig, SolverResults, SolverType
from ..interfaces.data_interface import AerodynamicData, AcousticData

class AcousticSolverBase(SolverInterface):
    """声学求解器基类"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.observer_positions = None
        self.source_data_history = []
        self.acoustic_results_history = []
        self.sound_speed = 343.0  # m/s
        self.air_density = 1.225  # kg/m³
    
    @property
    def solver_type(self) -> SolverType:
        """返回求解器类型"""
        return SolverType.ACOUSTIC
    
    def initialize(self, geometry_data: Dict[str, Any]) -> None:
        """初始化声学求解器"""
        self._setup_observer_grid(geometry_data)
        self._initialize_acoustic_parameters(geometry_data)
        self._is_initialized = True
    
    @abstractmethod
    def _setup_observer_grid(self, geometry_data: Dict[str, Any]) -> None:
        """设置观测点网格"""
        pass
    
    @abstractmethod
    def _initialize_acoustic_parameters(self, geometry_data: Dict[str, Any]) -> None:
        """初始化声学参数"""
        pass
    
    @abstractmethod
    def _compute_acoustic_sources(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """计算声学源项"""
        pass
    
    @abstractmethod
    def _propagate_sound(self, source_data: Dict[str, Any]) -> AcousticData:
        """声传播计算"""
        pass
    
    def solve_timestep(self, current_time: float, 
                      boundary_conditions: Dict[str, Any]) -> SolverResults:
        """求解单个时间步"""
        if not self._is_initialized:
            raise RuntimeError("声学求解器未初始化")
        
        self._current_time = current_time
        
        # 获取气动数据
        aero_data = boundary_conditions.get('aerodynamic_data')
        if aero_data is None:
            raise ValueError("缺少气动数据输入")
        
        # 计算声学源项
        source_data = self._compute_acoustic_sources(aero_data)
        self.source_data_history.append(source_data)
        
        # 声传播计算
        acoustic_data = self._propagate_sound(source_data)
        self.acoustic_results_history.append(acoustic_data)
        
        # 转换为标准结果格式
        forces = np.zeros(3)  # 声学求解器通常不计算力
        moments = np.zeros(3)
        
        return SolverResults(
            forces=forces,
            moments=moments,
            time_stamp=current_time,
            convergence_achieved=True,  # 声学计算通常是直接计算
            iterations_used=1,
            computation_time=0.0,
            solver_specific_data={
                'acoustic_data': acoustic_data,
                'source_data': source_data,
                'observer_positions': self.observer_positions
            }
        )
    
    def get_required_boundary_conditions(self) -> List[str]:
        """获取必需的边界条件"""
        return ['aerodynamic_data']
    
    def finalize(self) -> Dict[str, Any]:
        """完成计算"""
        return {
            'total_timesteps': len(self.acoustic_results_history),
            'source_history': self.source_data_history,
            'acoustic_history': self.acoustic_results_history,
            'observer_grid': self.observer_positions
        }
    
    def compute_frequency_spectrum(self, time_series: np.ndarray, 
                                 sampling_rate: float) -> tuple:
        """计算频谱"""
        n_samples = len(time_series)
        frequencies = np.fft.fftfreq(n_samples, 1/sampling_rate)
        spectrum = np.fft.fft(time_series)
        
        # 只返回正频率部分
        positive_freq_idx = frequencies >= 0
        return frequencies[positive_freq_idx], spectrum[positive_freq_idx]
    
    def compute_sound_pressure_level(self, pressure: np.ndarray, 
                                   reference_pressure: float = 2e-5) -> np.ndarray:
        """计算声压级"""
        rms_pressure = np.sqrt(np.mean(pressure**2))
        spl = 20 * np.log10(rms_pressure / reference_pressure)
        return spl
    
    def compute_directivity_pattern(self, acoustic_data_list: List[AcousticData]) -> Dict[str, Any]:
        """计算指向性图案"""
        if not acoustic_data_list:
            return {}
        
        # 提取各观测点的声压数据
        observer_positions = acoustic_data_list[0].observer_positions
        n_observers = len(observer_positions)
        
        # 计算各方向的平均声压级
        directivity = np.zeros(n_observers)
        
        for i in range(n_observers):
            pressure_time_series = []
            for acoustic_data in acoustic_data_list:
                if i < len(acoustic_data.sound_pressure):
                    pressure_time_series.append(acoustic_data.sound_pressure[i])
            
            if pressure_time_series:
                pressure_array = np.array(pressure_time_series)
                directivity[i] = self.compute_sound_pressure_level(pressure_array)
        
        return {
            'observer_positions': observer_positions,
            'directivity_spl': directivity,
            'max_spl': np.max(directivity),
            'min_spl': np.min(directivity)
        }
````

````python path=cycloidal_rotor_suite_refactored/core/acoustics/solvers/__init__.py mode=EDIT
"""
声学求解器实现
============

包含各种声学分析求解器
"""

from .fwh_solver import FWHSolver
from .bpm_solver import BPMSolver

__all__ = ["FWHSolver", "BPMSolver"]
````

````python path=cycloidal_rotor_suite_refactored/core/acoustics/solvers/fwh_solver.py mode=EDIT
"""
FWH声学求解器实现
===============

Ffowcs Williams-Hawkings方程求解器 - 高保真度声学计算
"""

import numpy as np
from typing import Dict, Any
from ..base import AcousticSolverBase
from ...interfaces.data_interface import AerodynamicData, AcousticData
from ...interfaces.solver_interface import SolverConfig

class FWHSolver(AcousticSolverBase):
    """Ffowcs Williams-Hawkings方程求解器"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.integration_surface = None
        self.retarded_time_data = {}
        self.mach_number_threshold = 0.3
    
    @property
    def solver_name(self) -> str:
        return "FWH"
    
    def _setup_observer_grid(self, geometry_data: Dict[str, Any]) -> None:
        """设置观测点网格"""
        # 创建球面观测网格
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        
        # 观测距离
        observer_distances = geometry_data.get('observer_distances', [5.0, 10.0, 20.0])
        
        # 角度网格
        n_theta = geometry_data.get('n_theta', 36)  # 方位角分辨率
        n_phi = geometry_data.get('n_phi', 18)      # 仰角分辨率
        
        theta = np.linspace(0, 2*np.pi, n_theta, endpoint=False)
        phi = np.linspace(0, np.pi, n_phi)
        
        observer_positions = []
        
        for r in observer_distances:
            for t in theta:
                for p in phi:
                    x = r * np.sin(p) * np.cos(t)
                    y = r * np.sin(p) * np.sin(t)
                    z = r * np.cos(p)
                    observer_positions.append([x, y, z])
        
        self.observer_positions = np.array(observer_positions)
    
    def _initialize_acoustic_parameters(self, geometry_data: Dict[str, Any]) -> None:
        """初始化声学参数"""
        # 设置积分面（通常是叶片表面）
        self.integration_surface = {
            'type': 'blade_surface',
            'blade_count': geometry_data.get('blade_count', 3),
            'rotor_radius': geometry_data.get('rotor_radius', 1.0)
        }
        
        # 初始化延迟时间数据存储
        self.retarded_time_data = {
            'source_positions': [],
            'source_strengths': [],
            'source_times': []
        }
    
    def _compute_acoustic_sources(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """计算FWH声学源项"""
        # 提取气动数据
        blade_positions = aero_data.blade_positions
        pressure = aero_data.pressure
        velocity = aero_data.velocity
        forces = aero_data.forces
        
        # 计算厚度源项（单极子源）
        thickness_sources = self._compute_thickness_sources(blade_positions, velocity)
        
        # 计算载荷源项（偶极子源）
        loading_sources = self._compute_loading_sources(blade_positions, forces, pressure)
        
        # 计算四极子源项（如果需要）
        quadrupole_sources = self._compute_quadrupole_sources(aero_data)
        
        return {
            'thickness_sources': thickness_sources,
            'loading_sources': loading_sources,
            'quadrupole_sources': quadrupole_sources,
            'source_positions': blade_positions,
            'time_stamp': aero_data.time_stamp
        }
    
    def _compute_thickness_sources(self, positions: np.ndarray, 
                                 velocity: np.ndarray) -> Dict[str, Any]:
        """计算厚度源项"""
        # 厚度源强度 = ρ * V_n * S
        # 其中 V_n 是法向速度，S 是表面积
        
        thickness_strength = []
        
        for i, pos in enumerate(positions):
            if i < len(velocity):
                # 简化：假设法向速度为速度的z分量
                normal_velocity = velocity[i, 2] if velocity.ndim > 1 else velocity[i]
                
                # 简化的表面积计算
                surface_area = 0.1  # 假设值
                
                strength = self.air_density * normal_velocity * surface_area
                thickness_strength.append(strength)
            else:
                thickness_strength.append(0.0)
        
        return {
            'strength': np.array(thickness_strength),
            'positions': positions,
            'type': 'monopole'
        }
    
    def _compute_loading_sources(self, positions: np.ndarray, 
                               forces: np.ndarray, 
                               pressure: np.ndarray) -> Dict[str, Any]:
        """计算载荷源项"""
        # 载荷源强度基于表面压力和力
        
        loading_strength = []
        loading_direction = []
        
        for i, pos in enumerate(positions):
            if i < len(forces):
                # 使用力作为载荷源强度
                if forces.ndim > 1:
                    force_magnitude = np.linalg.norm(forces[i])
                    force_dir = forces[i] / (force_magnitude + 1e-10)
                else:
                    force_magnitude = abs(forces[i])
                    force_dir = np.array([0, 0, 1])  # 默认方向
                
                loading_strength.append(force_magnitude)
                loading_direction.append(force_dir)
            else:
                loading_strength.append(0.0)
                loading_direction.append(np.array([0, 0, 1]))
        
        return {
            'strength': np.array(loading_strength),
            'direction': np.array(loading_direction),
            'positions': positions,
            'type': 'dipole'
        }
    
    def _compute_quadrupole_sources(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """计算四极子源项"""
        # 四极子源主要来自湍流和非线性效应
        # 这里提供简化实现
        
        if aero_data.velocity is None:
            return {'strength': np.array([]), 'positions': np.array([]), 'type': 'quadrupole'}
        
        velocity = aero_data.velocity
        positions = aero_data.blade_positions
        
        quadrupole_strength = []
        
        for i, pos in enumerate(positions):
            if i < len(velocity):
                # 基于速度梯度的简化四极子强度
                if velocity.ndim > 1:
                    vel_magnitude = np.linalg.norm(velocity[i])
                else:
                    vel_magnitude = abs(velocity[i])
                
                # 四极子强度 ∝ ρ * V^2 / c^2
                mach_number = vel_magnitude / self.sound_speed
                
                if mach_number > self.mach_number_threshold:
                    strength = self.air_density * vel_magnitude**2 / self.sound_speed**2
                else:
                    strength = 0.0  # 低马赫数时忽略四极子源
                
                quadrupole_strength.append(strength)
            else:
                quadrupole_strength.append(0.0)
        
        return {
            'strength': np.array(quadrupole_strength),
            'positions': positions,
            'type': 'quadrupole'
        }
    
    def _propagate_sound(self, source_data: Dict[str, Any]) -> AcousticData:
        """FWH声传播计算"""
        n_observers = len(self.observer_positions)
        sound_pressure = np.zeros(n_observers)
        
        # 获取源项数据
        thickness_sources = source_data['thickness_sources']
        loading_sources = source_data['loading_sources']
        quadrupole_sources = source_data['quadrupole_sources']
        
        for i, observer_pos in enumerate(self.observer_positions):
            pressure = 0.0
            
            # 厚度源贡献（单极子）
            pressure += self._compute_monopole_contribution(
                observer_pos, thickness_sources, source_data['time_stamp']
            )
            
            # 载荷源贡献（偶极子）
            pressure += self._compute_dipole_contribution(
                observer_pos, loading_sources, source_data['time_stamp']
            )
            
            # 四极子源贡献
            pressure += self._compute_quadrupole_contribution(
                observer_pos, quadrupole_sources, source_data['time_stamp']
            )
            
            sound_pressure[i] = pressure
        
        # 计算频谱（如果有足够的时间历史）
        frequency_spectrum = np.array([])
        if len(self.acoustic_results_history) > 10:
            # 简化的频谱计算
            time_series = [result.sound_pressure[0] for result in self.acoustic_results_history[-10:]]
            frequencies, spectrum = self.compute_frequency_spectrum(
                np.array(time_series), 1.0/self.config.time_step
            )
            frequency_spectrum = np.abs(spectrum)
        
        return AcousticData(
            sound_pressure=sound_pressure,
            frequency_spectrum=frequency_spectrum,
            directivity=sound_pressure,  # 简化：直接使用声压作为指向性
            time_stamp=source_data['time_stamp'],
            observer_positions=self.observer_positions,
            source_positions=source_data['source_positions']
        )
    
    def _compute_monopole_contribution(self, observer_pos: np.ndarray, 
                                     thickness_sources: Dict[str, Any],
                                     current_time: float) -> float:
        """计算单极子源贡献"""
        total_pressure = 0.0
        
        source_positions = thickness_sources['positions']
        source_strengths = thickness_sources['strength']
        
        for j, source_pos in enumerate(source_positions):
            if j >= len(source_strengths):
                continue
            
            # 计算距离和延迟时间
            distance = np.linalg.norm(observer_pos - source_pos)
            retarded_time = current_time - distance / self.sound_speed
            
            if distance > 1e-10:
                # 单极子公式：p = (1/4πr) * ∂Q/∂t
                source_strength = source_strengths[j]
                
                # 简化的时间导数（使用当前强度）
                pressure_contribution = source_strength / (4 * np.pi * distance)
                total_pressure += pressure_contribution
        
        return total_pressure
    
    def _compute_dipole_contribution(self, observer_pos: np.ndarray,
                                   loading_sources: Dict[str, Any],
                                   current_time: float) -> float:
        """计算偶极子源贡献"""
        total_pressure = 0.0
        
        source_positions = loading_sources['positions']
        source_strengths = loading_sources['strength']
        source_directions = loading_sources['direction']
        
        for j, source_pos in enumerate(source_positions):
            if j >= len(source_strengths):
                continue
            
            # 计算距离向量
            r_vec = observer_pos - source_pos
            distance = np.linalg.norm(r_vec)
            
            if distance > 1e-10:
                r_hat = r_vec / distance
                
                # 偶极子方向
                if j < len(source_directions):
                    dipole_direction = source_directions[j]
                else:
                    dipole_direction = np.array([0, 0, 1])
                
                # 偶极子公式：p = (1/4πr²) * (r̂·F̂) * F
                cos_theta = np.dot(r_hat, dipole_direction)
                source_strength = source_strengths[j]
                
                pressure_contribution = (cos_theta * source_strength) / (4 * np.pi * distance**2)
                total_pressure += pressure_contribution
        
        return total_pressure
    
    def _compute_quadrupole_contribution(self, observer_pos: np.ndarray,
                                       quadrupole_sources: Dict[str, Any],
                                       current_time: float) -> float:
        """计算四极子源贡献"""
        total_pressure = 0.0
        
        source_positions = quadrupole_sources['positions']
        source_strengths = quadrupole_sources['strength']
        
        for j, source_pos in enumerate(source_positions):
            if j >= len(source_strengths):
                continue
            
            distance = np.linalg.norm(observer_pos - source_pos)
            
            if distance > 1e-10:
                # 简化的四极子公式：p ∝ 1/r³
                source_strength = source_strengths[j]
                pressure_contribution = source_strength / (4 * np.pi * distance**3)
                total_pressure += pressure_contribution
        
        return total_pressure
````

````python path=cycloidal_rotor_suite_refactored/core/acoustics/solvers/bpm_solver.py mode=EDIT
"""
BPM宽带噪声求解器实现
==================

Brooks-Pope-Marcolini宽带噪声模型 - 中等保真度声学计算
"""

import numpy as np
from typing import Dict, Any
from ..base import AcousticSolverBase
from ...interfaces.data_interface import AerodynamicData, AcousticData
from ...interfaces.solver_interface import SolverConfig

class BPMSolver(AcousticSolverBase):
    """BPM宽带噪声求解器"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        self.frequency_bands = None
        self.airfoil_parameters = {}
        
    @property
    def solver_name(self) -> str:
        return "BPM"
    
    def _setup_observer_grid(self, geometry_data: Dict[str, Any]) -> None:
        """设置观测点网格"""
        # BPM模型通常用于远场噪声预测
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        
        # 远场观测点
        observer_distance = geometry_data.get('observer_distance', 100.0)  # 100m远场
        n_angles = geometry_data.get('n_directivity_angles', 72)
        
        angles = np.linspace(0, 2*np.pi, n_angles, endpoint=False)
        
        observer_positions = []
        for angle in angles:
            x = observer_distance * np.cos(angle)
            y = observer_distance * np.sin(angle)
            z = 0.0  # 水平面
            observer_positions.append([x, y, z])
        
        self.observer_positions = np.array(observer_positions)
    
    def _initialize_acoustic_parameters(self, geometry_data: Dict[str, Any]) -> None:
        """初始化BPM模型参数"""
        # 设置频率带
        self.frequency_bands = np.logspace(1, 4, 50)  # 10 Hz to 10 kHz
        
        # 翼型参数
        self.airfoil_parameters = {
            'displacement_thickness_ratio': 0.02,  # δ*/c
            'boundary_layer_thickness_ratio': 0.1,  # δ/c
            'trailing_edge_thickness_ratio': 0.002,  # h/c
            'angle_of_attack_stall': 15.0 * np.pi / 180,  # 失速攻角
        }
    
    def _compute_acoustic_sources(self, aero_data: AerodynamicData) -> Dict[str, Any]:
        """计算BPM噪声源"""
        blade_positions = aero_data.blade_positions
        velocity = aero_data.velocity
        forces = aero_data.forces
        
        # 计算各种BPM噪声源
        turbulent_boundary_layer = self._compute_tbl_noise_sources(blade_positions, velocity)
        separation_stall = self._compute_separation_stall_sources(blade_positions, velocity, forces)
        trailing_edge = self._compute_trailing_edge_sources(blade_positions, velocity)
        tip_vortex = self._compute_tip_vortex_sources(blade_positions, velocity)
        
        return {
            'turbulent_boundary_layer': turbulent_boundary_layer,
            'separation_stall': separation_stall,
            'trailing_edge': trailing_edge,
            'tip_vortex': tip_vortex,
            'source_positions': blade_positions,
            'time_stamp': aero_data.time_stamp
        }
    
    def _compute_tbl_noise_sources(self, positions: np.ndarray, 
                                 velocity: np.ndarray) -> Dict[str, Any]:
        """计算湍流边界层噪声源"""
        tbl_sources = []
        
        for i, pos in enumerate(positions):
            if i < len(velocity):
                if velocity.ndim > 1:
                    vel_magnitude = np.linalg.norm(velocity[i])
                else:
                    vel_magnitude = abs(velocity[i])
                
                # BPM湍流边界层噪声公式
                # SPL = f(Re, M, α, geometry)
                
                # 计算雷诺数
                chord = 0.1  # 假设弦长
                reynolds_number = self.air_density * vel_magnitude * chord / (1.8e-5)  # 动力粘度
                
                # 计算马赫数
                mach_number = vel_magnitude / self.sound_speed
                
                # 简化的TBL噪声计算
                if reynolds_number > 1e4 and mach_number > 0.1:
                    # 压力面和吸力面噪声
                    pressure_side_spl = self._compute_tbl_pressure_side(reynolds_number, mach_number)
                    suction_side_spl = self._compute_tbl_suction_side(reynolds_number, mach_number)
                    
                    tbl_source = {
                        'pressure_side_spl': pressure_side_spl,
                        'suction_side_spl': suction_side_spl,
                        'reynolds_number': reynolds_number,
                        'mach_number': mach_number
                    }
                else:
                    tbl_source = {
                        'pressure_side_spl': np.zeros_like(self.frequency_bands),
                        'suction_side_spl': np.zeros_like(self.frequency_bands),
                        'reynolds_number': reynolds_number,
                        'mach_number': mach_number
                    }
                
                tbl_sources.append(tbl_source)
            else:
                tbl_sources.append({
                    'pressure_side_spl': np.zeros_like(self.frequency_bands),
                    'suction_side_spl': np.zeros_like(self.frequency_bands),
                    'reynolds_number': 0.0,
                    'mach_number': 0.0
                })
        
        return {
            'sources': tbl_sources,
            'frequencies': self.frequency_bands,
            'type': 'turbulent_boundary_layer'
        }
    
    def _compute_tbl_pressure_side(self, reynolds_number: float, mach_number: float) -> np.ndarray:
        """计算压力面湍流边界层噪声"""
        # BPM模型压力面公式
        spl = np.zeros_like(self.frequency_bands)
        
        for i, freq in enumerate(self.frequency_bands):
            # Strouhal数
            chord = 0.1
            strouhal = freq * chord / (mach_number * self.sound_speed)
            
            # BPM压力面公式（简化版）
            if strouhal > 0.1 and strouhal < 10:
                A = 10 * np.log10(self.airfoil_parameters['displacement_thickness_ratio'])
                B = 40 * np.log10(mach_number)
                C = -10 * np.log10(strouhal)
                
                spl[i] = max(0, A + B + C + 60)  # 基准值60dB
            else:
                spl[i] = 0.0
        
        return spl
    
    def _compute_tbl_suction_side(self, reynolds_number: float, mach_number: float) -> np.ndarray:
        """计算吸力面湍流边界层噪声"""
        # BPM模型吸力面公式
        spl = np.zeros_like(self.frequency_bands)
        
        for i, freq in enumerate(self.frequency_bands):
            chord = 0.1
            strouhal = freq * chord / (mach_number * self.sound_speed)
            
            # BMP吸力面公式（简化版）
            if strouhal > 0.1 and strouhal < 10:
                A = 10 * np.log10(self.airfoil_parameters['boundary_layer_thickness_ratio'])
                B = 50 * np.log10(mach_number)
                C = -15 * np.log10(strouhal)
                
                spl[i] = max(0, A + B + C + 65)  # 基准值65dB
            else:
                spl[i] = 0.0
        
        return spl
    
    def _compute_separation_stall_sources(self, positions: np.ndarray,
                                        velocity: np.ndarray,
                                        forces: np.ndarray) -> Dict[str, Any]:
        """计算分离失速噪声源"""
        separation_sources = []
        
        for i, pos in enumerate(positions):
            if i < len(velocity) and i < len(forces):
                # 估算攻角（简化）
                if velocity.ndim > 1:
                    vel_magnitude = np.linalg.norm(velocity[i])
                else:
                    vel_magnitude = abs(velocity[i])
                
                # 基于力系数估算攻角
                if forces.ndim > 1:
                    force_magnitude = np.linalg.norm(forces[i])
                else:
                    force_magnitude = abs(forces[i])
                
                # 简化的攻角估算
                if vel_magnitude > 1e-6:
                    lift_coefficient = force_magnitude / (0.5 * self.air_density * vel_magnitude**2 * 0.1)
                    estimated_aoa = lift_coefficient / (2 * np.pi)  # 线性升力理论
                else:
                    estimated_aoa = 0.0
                
                # 检查是否处于失速状态
                is_stalled = abs(estimated_aoa) > self.airfoil_parameters['angle_of_attack_stall']
                
                if is_stalled:
                    mach_number = vel_magnitude / self.sound_speed
                    separation_spl = self._compute_separation_noise(mach_number, estimated_aoa)
                else:
                    separation_spl = np.zeros_like(self.frequency_bands)
                
                separation_sources.append({
                    'spl': separation_spl,
                    'angle_of_attack': estimated_aoa,
                    'is_stalled': is_stalled
                })
            else:
                separation_sources.append({
                    'spl': np.zeros_like(self.frequency_bands),
                    'angle_of_attack': 0.0,
                    'is_stalled': False
                })
        
        return {
            'sources': separation_sources,
            'frequencies': self.frequency_bands,
            'type': 'separation_stall'
        }
    
    def _compute_separation_noise(self, mach_number: float, angle_of_attack: float) -> np.ndarray:
        """计算分离噪声"""
        spl = np.zeros_like(self.frequency_bands)
        
        # BPM分离噪声公式（简化版）
        stall_factor = abs(angle_of_attack) / self.airfoil_parameters['angle_of_attack_stall']
        
        for i, freq in enumerate(self.frequency_bands):
            if freq > 100 and freq < 5000:  # 分离噪声主要频率范围
                base_spl = 70 + 20 * np.log10(mach_number) + 10 * np.log10(stall_factor)
                frequency_factor = -10 * np.log10(freq / 1000)  # 频率衰减
                spl[i] = max(0, base_spl + frequency_factor)
        
        return spl
    
    def _compute_trailing_edge_sources(self, positions: np.ndarray,
                                     velocity: np.ndarray) -> Dict[str, Any]:
        """计算后缘噪声源"""
        te_sources = []
        
        for i, pos in enumerate(positions):
            if i < len(velocity):
                if velocity.ndim > 1:
                    vel_magnitude = np.linalg.norm(velocity[i])
                else:
                    vel_magnitude = abs(velocity[i])
                
                mach_number = vel_magnitude / self.sound_speed
                
                # BPM后缘噪声
                te_spl = self._compute_trailing_edge_noise(mach_number)
                
                te_sources.append({
                    'spl': te_spl,
                    'mach_number': mach_number
                })
            else:
                te_sources.append({
                    'spl': np.zeros_like(self.frequency_bands),
                    'mach_number': 0.0
                })
        
        return {
            'sources': te_sources,
            'frequencies': self.frequency_bands,
            'type': 'trailing_edge'
        }
    
    def _compute_trailing_edge_noise(self, mach_number: float) -> np.ndarray:
        """计算后缘噪声"""
        spl = np.zeros_like(self.frequency_bands)
        
        # BPM后缘噪声公式
        for i, freq in enumerate(self.frequency_bands):
            chord = 0.1
            strouhal = freq * chord / (mach_number * self.sound_speed)
            
            if strouhal > 0.1 and strouhal < 100:
                # 后缘厚度噪声
                thickness_ratio = self.airfoil_parameters['trailing_edge_thickness_ratio']
                
                A = 10 * np.log10(thickness_ratio)
                B = 60 * np.log10(mach_number)
                C = -20 * np.log10(strouhal)
                
                spl[i] = max(0, A + B + C + 50)  # 基准值50dB
        
        return spl
    
    def _compute_tip_vortex_sources(self, positions: np.ndarray,
                                  velocity: np.ndarray) -> Dict[str, Any]:
        """计算叶尖涡噪声源"""
        # 叶尖涡噪声主要来自最外侧的叶片段
        tip_sources = []
        
        if len(positions) > 0:
            # 找到最外侧位置
            radial_distances = [np.linalg.norm(pos[:2]) for pos in positions]
            max_radius_idx = np.argmax(radial_distances)
            
            if max_radius_idx < len(velocity):
                if velocity.ndim > 1:
                    tip_velocity = np.linalg.norm(velocity[max_radius_idx])
                else:
                    tip_velocity = abs(velocity[max_radius_idx])
                
                mach_number = tip_velocity / self.sound_speed
                
                # 叶尖涡噪声计算
                tip_spl = self._compute_tip_vortex_noise(mach_number)
                
                tip_sources.append({
                    'spl': tip_spl,
                    'tip_mach_number': mach_number,
                    'tip_position': positions[max_radius_idx]
                })
        
        if not tip_sources:
            tip_sources.append({
                'spl': np.zeros_like(self.frequency_bands),
                'tip_mach_number': 0.0,
                'tip_position': np.zeros(3)
            })
        
        return {
            'sources': tip_sources,
            'frequencies': self.frequency_bands,
            'type': 'tip_vortex'
        }
    
    def _compute_tip_vortex_noise(self, tip_mach_number: float) -> np.ndarray:
        """计算叶尖涡噪声"""
        spl = np.zeros_like(self.frequency_bands)
        
        # 叶尖涡噪声主要在低频
        for i, freq in enumerate(self.frequency_bands):
            if freq < 1000:  # 低频范围
                # 简化的叶尖涡噪声公式
                base_spl = 80 + 50 * np.log10(tip_mach_number)
                frequency_factor = -30 * np.log10(freq / 100)  # 低频增强
                spl[i] = max(0, base_spl + frequency_factor)
        
        return spl
    
    def _propagate_sound(self, source_data: Dict[str, Any]) -> AcousticData:
        """BPM声传播计算"""
        n_observers = len(self.observer_positions)
        n_frequencies = len(self.frequency_bands)
        
        # 初始化结果数组
        sound_pressure_spectrum = np.zeros((n_observers, n_frequencies))
        total_spl = np.zeros(n_observers)
        
        # 获取各种噪声源
        tbl_sources = source_data['turbulent_boundary_layer']['sources']
        separation_sources = source_data['separation_stall']['sources']
        te_sources = source_data['trailing_edge']['sources']
        tip_sources = source_data['tip_vortex']['sources']
        
        for i, observer_pos in enumerate(self.observer_positions):
            observer_spl_spectrum = np.zeros(n_frequencies)
            
            # 累加所有噪声源的贡献
            for tbl_source in tbl_sources:
                observer_spl_spectrum += tbl_source['pressure_side_spl']
                observer_spl_spectrum += tbl_source['suction_side_spl']
            
            for sep_source in separation_sources:
                observer_spl_spectrum += sep_source['spl']
            
            for te_source in te_sources:
                observer_spl_spectrum += te_source['spl']
            
            for tip_source in tip_sources:
                observer_spl_spectrum += tip_source['spl']
            
            # 应用传播效应（距离衰减、大气吸收等）
            observer_spl_spectrum = self._apply_propagation_effects(
                observer_spl_spectrum, observer_pos, source_data['source_positions']
            )
            
            sound_pressure_spectrum[i] = observer_spl_spectrum
            
            # 计算总声压级
            total_spl[i] = self._compute_total_spl(observer_spl_spectrum)
        
        return AcousticData(
            sound_pressure=total_spl,
            frequency_spectrum=sound_pressure_spectrum,
            directivity=total_spl,
            time_stamp=source_data['time_stamp'],
            observer_positions=self.observer_positions,
            source_positions=source_data['source_positions'],
            metadata={
                'frequency_bands': self.frequency_bands,
                'noise_components': {
                    'tbl': len(tbl_sources),
                    'separation': len(separation_sources),
                    'trailing_edge': len(te_sources),
                    'tip_vortex': len(tip_sources)
                }
            }
        )
    
    def _apply_propagation_effects(self, spl_spectrum: np.ndarray,
                                 observer_pos: np.ndarray,
                                 source_positions: np.ndarray) -> np.ndarray:
        """应用声传播效应"""
        if len(source_positions) == 0:
            return spl_spectrum
        
        # 计算平均传播距离
        distances = [np.linalg.norm(observer_pos - src_pos) for src_pos in source_positions]
        avg_distance = np.mean(distances)
        
        # 距离衰减（球面扩散）
        distance_attenuation = 20 * np.log10(avg_distance / 1.0)  # 参考距离1m
        
        # 大气吸收（简化模型）
        atmospheric_absorption = np.zeros_like(spl_spectrum)
        for i, freq in enumerate(self.frequency_bands):
            # 简化的大气吸收系数
            alpha = 0.1 * (freq / 1000)**1.5  # dB/km
            atmospheric_absorption[i] = alpha * avg_distance / 1000  # 转换为km
        
        # 应用衰减
        corrected_spl = spl_spectrum - distance_attenuation - atmospheric_absorption
        
        return np.maximum(corrected_spl, 0)  # 确保非负
    
    def _compute_total_spl(self, spl_spectrum: np.ndarray) -> float:
        """计算总声压级"""
        # 能量求和
        total_energy = np.sum(10**(spl_spectrum / 10))
        if total_energy > 0:
            total_spl = 10 * np.log10(total_energy)
        else:
            total_spl = 0.0
        
        return total_spl
````

## 第五步：注册求解器到工厂

````python path=cycloidal_rotor_suite_refactored/core/factories/solver_factory.py mode=EDIT
    def _setup_default_solvers(self):
        """设置默认求解器映射"""
        # 导入求解器类
        from ..aerodynamics.solvers import BEMTSolver, UVLMSolver, LiftingLineSolver
        from ..acoustics.solvers import FWHSolver, BPMSolver
        
        # 注册气动求解器
        self.register_solver("BEMT", BEMTSolver, SolverType.AERODYNAMIC, {
            "description": "叶素动量理论求解器",
            "fidelity": "low",
            "computational_cost": "low",
            "accuracy": "medium"
        })
        
        self.register_solver("LiftingLine", LiftingLineSolver, SolverType.AERODYNAMIC, {
            "description": "升力线理论求解器", 
            "fidelity": "medium",
            "computational_cost": "medium",
            "accuracy": "high"
        })
        
        self.register_solver("UVLM", UVLMSolver, SolverType.AERODYNAMIC, {
            "description": "非定常涡格法求解器",
            "fidelity": "high", 
            "computational_cost": "high",
            "accuracy": "very_high"
        })
        
        # 注册声学求解器
        self.register_solver("BPM", BPMSolver, SolverType.ACOUSTIC, {
            "description": "BPM宽带噪声模型",
            "fidelity": "medium",
            "computational_cost": "low", 
            "accuracy": "medium"
        })
        
        self.register_solver("FWH", FWHSolver, SolverType.ACOUSTIC, {
            "description": "Ffowcs Williams-Hawkings方程求解器",
            "fidelity": "high",
            "computational_cost": "high",
            "accuracy": "very_high"
        })
````

## 第六步：创建示例配置和测试

````python path=cycloidal_rotor_suite_refactored/examples/basic_usage.py mode=EDIT
"""
基本使用示例
==========

演示重构版本的基本使用方法
"""

import numpy as np
from core.interfaces.solver_interface import SolverConfig, FidelityLevel
from core.factories.solver_factory import get_solver_factory
from core.interfaces.solver_interface import SolverType

def main():
    """基本使用示例"""
    
    # 1. 创建求解器工厂
    factory = get_solver_factory()
    
    # 2. 配置气动求解器
    aero_config = SolverConfig(
        fidelity_level=FidelityLevel.MEDIUM,
        time_step=0.01,
        max_iterations=100,
        convergence_tolerance=1e-6
    )
    
    # 3. 创建气动求解器
    aero_solver = factory.create_aerodynamic_solver("LiftingLine", aero_config)
    
    # 4. 几何数据
    geometry_data = {
        'rotor_radius': 1.0,
        'blade_count': 3,
        'chord_distribution': np.ones(30) * 0.1,
        'twist_distribution': np.zeros(30)
    }
    
    # 5. 初始化求解器
    aero_solver.initialize(geometry_data)
    
    # 6. 边界条件
    boundary_conditions = {
        'rotor_rpm': 1200,
        'freestream_velocity': np.array([10.0, 0.0, 0.0]),
        'blade_pitch': 0.1
    }
    
    # 7. 求解时间步
    result = aero_solver.solve_timestep(0.0, boundary_conditions)
    
    print(f"求解器: {aero_solver.solver_name}")
    print(f"力: {result.forces}")
    print(f"力矩: {result.moments}")
    print(f"收敛: {result.convergence_achieved}")
    
    # 8. 创建声学求解器
    acoustic_config = SolverConfig(
        fidelity_level=FidelityLevel.MEDIUM,
        time_step=0.01
    )
    
    acoustic_solver = factory.create_acoustic_solver("BPM", acoustic_config)
    acoustic_solver.initialize(geometry_data)
    
    # 9. 声学计算（需要气动数据）
    from core.interfaces.data_interface import AerodynamicData
    
    aero_data = AerodynamicData(
        forces=result.forces,
        moments=result.moments,
        pressure=np.random.random(10),  # 示例数据
        velocity=np.random.random((10, 3)),
        time_stamp=0.0,
        blade_positions=np.random.random((3, 3))
    )
    
    acoustic_boundary_conditions = {
        'aerodynamic_data': aero_data
    }
    
    acoustic_result = acoustic_solver.solve_timestep(0.0, acoustic_boundary_conditions)
    
    print(f"\n声学求解器: {acoustic_solver.solver_name}")
    print(f"声压级: {np.max(acoustic_result.solver_specific_data['acoustic_data'].sound_pressure):.1f} dB")
    
    # 10. 完成计算
    aero_final = aero_solver.finalize()
    acoustic_final = acoustic_solver.finalize()
    
    print(f"\n计算完成:")
    print(f"气动迭代次数: {aero_final['total_iterations']}")
    print(f"声学时间步数: {acoustic_final['total_timesteps']}")

if __name__ == "__main__":
    main()
````

````python path=cycloidal_rotor_suite_refactored/tests/test_core_functionality.py mode=EDIT
"""
核心功能测试
==========

测试重构版本的核心功能
"""

import unittest
import numpy as np
from core.interfaces.solver_interface import SolverConfig, FidelityLevel, SolverType
from core.factories.solver_factory import get_solver_factory
from core.interfaces.data_interface import AerodynamicData

class TestCoreFunctionality(unittest.TestCase):
    """核心功能测试类"""
    
    def setUp(self):
        """测试设置"""
        self.factory = get_solver_factory()
        self.geometry_data = {
            'rotor_radius': 1.0,
            'blade_count': 3,
            'chord_distribution': np.ones(20) * 0.1,
            'twist_distribution': np.zeros(20)
        }
        
        self.config = SolverConfig(
            fidelity_level=FidelityLevel.MEDIUM,
            time_step=0.01,
            max_iterations=10
        )
    
    def test_solver_factory_creation(self):
        """测试求解器工厂创建"""
        # 测试获取可用求解器
        available = self.factory.get_available_solvers()
        
        self.assertIn('aerodynamic', available)
        self.assertIn('acoustic', available)
        self.assertGreater(len(available['aerodynamic']), 0)
        self.assertGreater(len(available['acoustic']), 0)
    
    def test_aerodynamic_solver_creation(self):
        """测试气动求解器创建"""
        # 测试BEMT求解器
        bemt_solver = self.factory.create_aerodynamic_solver("BEMT", self.config)
        self.assertEqual(bemt_solver.solver_name, "BEMT")
        self.assertEqual(bemt_solver.solver_type, SolverType.AERODYNAMIC)
        
        # 测试升力线求解器
        ll_solver = self.factory.create_aerodynamic_solver("LiftingLine", self.config)
        self.assertEqual(ll_solver.solver_name, "LiftingLine")
        
        # 测试UVLM求解器
        uvlm_solver = self.factory.create_aerodynamic_solver("UVLM", self.config)
        self.assertEqual(uvlm_solver.solver_name, "UVLM")
    
    def test_acoustic_solver_creation(self):
        """测试声学求解器创建"""
        # 测试BPM求解器
        bpm_solver = self.factory.create_acoustic_solver("BPM", self.config)
        self.assertEqual(bmp_solver.solver_name, "BPM")
        self.assertEqual(bpm_solver.solver_type, SolverType.ACOUSTIC)
        
        # 测试FWH求解器
        fwh_solver = self.factory.create_acoustic_solver("FWH", self.config)
        self.assertEqual(fwh_solver.solver_name, "FWH")
    
    def test_solver_initialization(self):
        """测试求解器初始化"""
        solver = self.factory.create_aerodynamic_solver("BEMT", self.config)
        
        # 初始化前
        self.assertFalse(solver.is_initialized)
        
        # 初始化
        solver.initialize(self.geometry_data)
        
        # 初始化后
        self.assertTrue(solver.is_initialized)
    
    def test_aerodynamic_solve_timestep(self):
        """测试气动求解时间步"""
        solver = self.factory.create_aerodynamic_solver("BEMT", self.config)
        solver.initialize(self.geometry_data)
        
        boundary_conditions = {
            'rotor_rpm': 1200,
            'freestream_velocity': np.array([10.0, 0.0, 0.0]),
            'blade_pitch': 0.1
        }
        
        result = solver.solve_timestep(0.0, boundary_conditions)
        
        # 检查结果格式
        self.assertEqual(len(result.forces), 3)
        self.assertEqual(len(result.moments), 3)
        self.assertEqual(result.time_stamp, 0.0)
        self.assertIsInstance(result.convergence_achieved, bool)
    
    def test_acoustic_solve_timestep(self):
        """测试声学求解时间步"""
        solver = self.factory.create_acoustic_solver("BPM", self.config)
        solver.initialize(self.geometry_data)
        
        # 创建气动数据
        aero_data = AerodynamicData(
            forces=np.array([100.0, 50.0, 200.0]),
            moments=np.array([10.0, 5.0, 20.0]),
            pressure=np.random.random(10),
            velocity=np.random.random((10, 3)) * 50,
            time_stamp=0.0,
            blade_positions=np.random.random((3, 3))
        )
        
        boundary_conditions = {
            'aerodynamic_data': aero_data
        }
        
        result = solver.solve_timestep(0.0, boundary_conditions)
        
        # 检查结果
        self.assertIn('acoustic_data', result.solver_specific_data)
        acoustic_data = result.solver_specific_data['acoustic_data']
        self.assertGreater(len(acoustic_data.sound_pressure), 0)
    
    def test_solver_recommendation(self):
        """测试求解器推荐"""
        # 低保真度推荐
        low_fidelity_req = {
            'fidelity_level': FidelityLevel.LOW,
            'computational_budget': 'low'
        }
        recommended = self.factory.recommend_solver(low_fidelity_req)
        self.assertEqual(recommended, "BEMT")
        
        # 高保真度推荐
        high_fidelity_req = {
            'fidelity_level': FidelityLevel.HIGH,
            'accuracy_requirement': 'high'
        }
        recommended = self.factory.recommend_solver(high_fidelity_req)
        self.assertEqual(recommended, "UVLM")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效求解器名称
        with self.assertRaises(ValueError):
            self.factory.create_aerodynamic_solver("InvalidSolver", self.config)
        
        # 测试未初始化求解器
        solver = self.factory
