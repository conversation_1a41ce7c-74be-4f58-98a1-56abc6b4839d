"""
新实现功能全面测试和验证
========================

基于adevice_complement5.md要求，对已实现的6个核心功能进行全面测试：
1. L-B动态失速模型与BEMT集成
2. UVLM自由尾迹演化
3. BPM完整噪声模型
4. FW-H时间历史管理
5. 跨声速修正算法
6. 三维效应模型

测试包括：功能测试、集成测试、性能测试、数值稳定性验证
"""

import sys
import os
import numpy as np
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Tuple, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试结果记录
test_results = {}
performance_metrics = {}

def safe_import():
    """安全导入所有必需模块"""
    try:
        from core.aerodynamics.solvers.bemt_solver import BEMTSolver, BladeElementWithLB
        from core.aerodynamics.solvers.uvlm_solver import UVLM<PERSON>olver, FreeWakeManager
        from core.acoustics.solvers.bmp_solver import BMPSolver
        from core.acoustics.solvers.fwh_solver import FWHSolver, TimeHistoryManager
        from core.physics.corrections import CompressibilityCorrection, AdvancedCompressibilityCorrection
        from core.physics.dynamic_stall import LeishmanBeddoesModel
        from core.physics.three_dimensional_effects import (
            NonlinearInducedVelocityModel, BladeInteractionModel, 
            DuSeligRadialFlowModel, ThreeDimensionalEffectsManager
        )
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel
        
        print("✅ 所有核心模块导入成功")
        return True, locals()
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False, {}

def test_lb_bemt_integration():
    """测试1: L-B动态失速模型与BEMT完整集成"""
    print("\n🔧 测试1: L-B动态失速模型与BEMT集成...")
    
    try:
        start_time = time.time()
        
        # 创建BEMT求解器配置
        config = SolverConfig(
            fidelity_level=FidelityLevel.HIGH,
            enable_dynamic_stall=True,
            max_iterations=50,
            convergence_tolerance=1e-6
        )
        
        solver = BEMTSolver(config)
        
        # 测试循环翼特殊参数传递
        test_params = {
            'V_inf': np.array([10.0, 0.0, 0.0]),
            'omega_rotor': 50.0,
            'blade_count': 3,
            'rotor_radius': 1.0
        }
        
        # 执行求解
        result = solver.solve(test_params['V_inf'], test_params['omega_rotor'])
        
        # 验证L-B模型状态记录
        if hasattr(solver, 'dynamic_stall_history'):
            history_keys = list(solver.dynamic_stall_history.keys())
            print(f"   ✅ 动态失速历史记录: {len(history_keys)} 个叶素")
        
        # 验证诱导因子计算
        induction_factors = solver._get_induction_factors()
        print(f"   ✅ 诱导因子计算: 轴向 {len(induction_factors['axial'])} 点")
        
        elapsed_time = time.time() - start_time
        performance_metrics['lb_bemt_integration'] = elapsed_time
        
        print(f"   ✅ L-B与BEMT集成测试成功 (耗时: {elapsed_time:.3f}s)")
        print(f"   - 推力: {result.get('thrust', 0):.2f} N")
        print(f"   - 功率: {result.get('power', 0):.2f} W")
        print(f"   - 收敛迭代: {result.get('iterations', 0)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ L-B与BEMT集成测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_uvlm_free_wake():
    """测试2: UVLM自由尾迹演化功能"""
    print("\n🔧 测试2: UVLM自由尾迹演化...")
    
    try:
        start_time = time.time()
        
        # 创建自由尾迹管理器
        wake_config = {
            'max_wake_age': 5.0,
            'wake_time_step': 0.01,
            'pc_iterations': 3,
            'wake_convergence_tol': 1e-6,
            'vortex_core_radius': 0.03,
            'enable_vatistas_core': True
        }
        
        wake_manager = FreeWakeManager(wake_config)
        
        # 模拟桨叶位置和环量分布
        blade_positions = np.array([
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [-1.0, 0.0, 0.0]
        ])
        circulation_distribution = np.array([0.5, 0.6, 0.4])
        
        # 测试尾迹演化
        for i in range(10):
            dt = 0.01
            wake_manager.evolve_wake_geometry(dt, blade_positions, circulation_distribution)
            
            # 旋转桨叶位置
            angle = i * 0.1
            rotation_matrix = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
            blade_positions = blade_positions @ rotation_matrix.T
        
        # 获取尾迹几何
        wake_geometry = wake_manager.get_wake_geometry()
        
        elapsed_time = time.time() - start_time
        performance_metrics['uvlm_free_wake'] = elapsed_time
        
        print(f"   ✅ UVLM自由尾迹演化测试成功 (耗时: {elapsed_time:.3f}s)")
        print(f"   - 尾迹节点数: {wake_geometry['node_count']}")
        print(f"   - 平均年龄: {np.mean(wake_geometry['ages']):.3f}s")
        print(f"   - 环量范围: {np.min(wake_geometry['circulations']):.3f} - {np.max(wake_geometry['circulations']):.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ UVLM自由尾迹测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_bmp_complete_noise_model():
    """测试3: BPM完整噪声模型（5种噪声机制）"""
    print("\n🔧 测试3: BPM完整噪声模型...")
    
    try:
        start_time = time.time()
        
        # 创建BMP求解器
        config = {
            'enable_tbl_te_noise': True,
            'enable_separation_noise': True,
            'enable_tip_vortex_noise': True,
            'enable_blunt_te_noise': True,
            'enable_laminar_boundary_layer_noise': True,
            'rotor_radius': 1.0,
            'air_density': 1.225,
            'sound_speed': 343.0,
            'kinematic_viscosity': 1.81e-5 / 1.225
        }
        
        solver = BMPSolver(config)
        
        # 测试各种噪声机制
        test_conditions = [
            {'velocity': 30.0, 'alpha': np.radians(2.0), 'r_station': 0.5, 'chord': 0.08},
            {'velocity': 50.0, 'alpha': np.radians(8.0), 'r_station': 0.7, 'chord': 0.10},
            {'velocity': 70.0, 'alpha': np.radians(15.0), 'r_station': 0.9, 'chord': 0.06}
        ]
        
        total_noise_levels = []
        
        for i, condition in enumerate(test_conditions):
            frequencies, psd = solver._calculate_broadband_noise_psd(**condition)
            
            # 计算总噪声级
            total_psd = np.trapz(psd, frequencies)
            spl = 10 * np.log10(total_psd / (20e-6)**2) if total_psd > 0 else 0
            total_noise_levels.append(spl)
            
            print(f"   - 工况{i+1}: SPL = {spl:.1f} dB, 最大PSD = {np.max(psd):.2e} Pa²/Hz")
        
        # 测试改进的TBL-TE噪声模型
        bl_params = {
            'delta_star_p': 0.001,
            'delta_star_s': 0.0015,
            'Re_c': 1e6,
            'alpha': np.radians(5.0)
        }
        
        frequencies = np.logspace(1, 4, 100)
        tbl_psd = solver._calculate_tbl_te_noise(frequencies, 50.0, bl_params)
        
        elapsed_time = time.time() - start_time
        performance_metrics['bmp_complete_noise'] = elapsed_time
        
        print(f"   ✅ BPM完整噪声模型测试成功 (耗时: {elapsed_time:.3f}s)")
        print(f"   - 平均噪声级: {np.mean(total_noise_levels):.1f} dB")
        print(f"   - TBL-TE最大PSD: {np.max(tbl_psd):.2e} Pa²/Hz")
        
        return True
        
    except Exception as e:
        print(f"   ❌ BPM完整噪声模型测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_fwh_time_history_manager():
    """测试4: FW-H时间历史管理器"""
    print("\n🔧 测试4: FW-H时间历史管理器...")
    
    try:
        start_time = time.time()
        
        # 创建时间历史管理器
        config = {
            'max_history_time': 5.0,
            'adaptive_window': True,
            'min_time_resolution': 1e-4,
            'window_safety_factor': 1.2,
            'c0': 343.0
        }
        
        history_manager = TimeHistoryManager(config)
        
        # 添加时间历史数据
        n_points = 100
        for i in range(n_points):
            t = i * 0.05
            position = np.array([np.cos(t), np.sin(t), 0.1 * t])
            velocity = np.array([-np.sin(t), np.cos(t), 0.1])
            force = np.array([np.sin(2*t), np.cos(2*t), 0.5])
            pressure = np.array([np.sin(t), np.cos(t), 0.0])
            
            history_manager.add_data_point(t, position, velocity, force, pressure)
        
        # 测试推迟时间数据查找
        observer_position = np.array([5.0, 0.0, 0.0])
        emission_time = 2.0
        
        retarded_data = history_manager.find_retarded_time_data(observer_position, emission_time)
        
        if retarded_data:
            print(f"   ✅ 推迟时间数据查找成功")
            print(f"   - 推迟时间: {retarded_data['retarded_time']:.4f}s")
            print(f"   - 传播距离: {retarded_data['distance']:.2f}m")
        
        # 测试数据插值
        target_time = 1.5
        interpolated_data = history_manager.interpolate_data_at_time(target_time)
        
        if interpolated_data:
            print(f"   ✅ 数据插值成功，目标时间: {target_time}s")
        
        # 获取统计信息
        stats = history_manager.get_statistics()
        
        elapsed_time = time.time() - start_time
        performance_metrics['fwh_time_history'] = elapsed_time
        
        print(f"   ✅ FW-H时间历史管理器测试成功 (耗时: {elapsed_time:.3f}s)")
        print(f"   - 数据点数: {stats['current_data_points']}")
        print(f"   - 缓存命中率: {stats['cache_hit_rate']:.1%}")
        print(f"   - 内存使用: {stats['memory_usage_mb']:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"   ❌ FW-H时间历史管理器测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_advanced_compressibility_correction():
    """测试5: 跨声速修正算法"""
    print("\n🔧 测试5: 跨声速修正算法...")

    try:
        start_time = time.time()

        # 创建高级压缩性修正
        correction = AdvancedCompressibilityCorrection("advanced")

        # 测试不同马赫数和攻角条件
        test_conditions = [
            {'Cl': 0.5, 'Cd': 0.02, 'mach': 0.3, 'thickness_ratio': 0.12, 'camber': 0.02},
            {'Cl': 0.8, 'Cd': 0.03, 'mach': 0.7, 'thickness_ratio': 0.10, 'camber': 0.01},
            {'Cl': 1.0, 'Cd': 0.05, 'mach': 0.9, 'thickness_ratio': 0.08, 'camber': 0.015},
            {'Cl': 0.6, 'Cd': 0.08, 'mach': 1.2, 'thickness_ratio': 0.06, 'camber': 0.005}
        ]

        correction_results = []

        for i, condition in enumerate(test_conditions):
            Cl_corr, Cd_corr = correction.apply_correction(**condition)

            # 计算修正因子
            Cl_factor = Cl_corr / condition['Cl'] if condition['Cl'] != 0 else 1.0
            Cd_factor = Cd_corr / condition['Cd'] if condition['Cd'] != 0 else 1.0

            correction_results.append((Cl_factor, Cd_factor))

            print(f"   - 马赫数{condition['mach']}: Cl修正 {Cl_factor:.3f}, Cd修正 {Cd_factor:.3f}")

        # 测试临界马赫数计算
        M_crit = correction._calculate_critical_mach_number(0.8, thickness_ratio=0.12, camber=0.02)
        print(f"   ✅ 临界马赫数计算: {M_crit:.3f}")

        # 测试激波-边界层相互作用
        Cl_shock, Cd_shock = correction._apply_shock_boundary_layer_interaction(
            0.8, 0.03, 0.85, 0.75, reynolds_number=1e6
        )
        print(f"   ✅ 激波-边界层相互作用: Cl={Cl_shock:.3f}, Cd={Cd_shock:.4f}")

        elapsed_time = time.time() - start_time
        performance_metrics['advanced_compressibility'] = elapsed_time

        print(f"   ✅ 跨声速修正算法测试成功 (耗时: {elapsed_time:.3f}s)")

        return True

    except Exception as e:
        print(f"   ❌ 跨声速修正算法测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_three_dimensional_effects():
    """测试6: 三维效应模型"""
    print("\n🔧 测试6: 三维效应模型...")

    try:
        start_time = time.time()

        # 创建三维效应管理器
        config = {
            'enable_nonlinear_induced_velocity': True,
            'enable_blade_interaction': True,
            'enable_du_selig_radial_flow': True,
            'nonlinear_induced_config': {
                'enable_vortex_interaction': True,
                'enable_wake_geometry_effects': True,
                'nonlinearity_factor': 1.0
            },
            'blade_interaction_config': {
                'blade_count': 3,
                'interaction_strength': 0.1,
                'phase_delay_factor': 0.2
            },
            'du_selig_config': {
                'enable_stall_delay': True,
                'enable_radial_flow': True,
                'c1': 1.5,
                'c2': 0.8
            }
        }

        effects_manager = ThreeDimensionalEffectsManager(config)

        # 测试非线性诱导速度模型
        nonlinear_model = effects_manager.models['nonlinear_induced']
        blade_positions = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [-1.0, 0.0, 0.0]])

        correction_factor = nonlinear_model.calculate_3d_correction(
            r=0.8, circulation=0.5, blade_positions=blade_positions
        )
        print(f"   ✅ 非线性诱导速度修正因子: {correction_factor:.3f}")

        # 测试桨叶间干扰模型
        interaction_model = effects_manager.models['blade_interaction']
        blade_loads = [0.5, 0.6, 0.4]

        interaction_factor = interaction_model.calculate_3d_correction(
            blade_index=0, azimuth_angle=np.pi/4, blade_loads=blade_loads
        )
        print(f"   ✅ 桨叶间干扰修正因子: {interaction_factor:.3f}")

        # 测试Du & Selig径向流动模型
        du_selig_model = effects_manager.models['du_selig']

        radial_correction = du_selig_model.calculate_3d_correction(
            r=0.7, chord=0.1, alpha=np.radians(8.0), omega=50.0
        )
        print(f"   ✅ Du & Selig径向流动修正因子: {radial_correction:.3f}")

        # 测试综合三维修正
        base_coefficients = (1.0, 0.05, 0.02)  # Cl, Cd, Cm
        correction_params = {
            'r': 0.8,
            'circulation': 0.5,
            'blade_positions': blade_positions,
            'blade_index': 0,
            'azimuth_angle': np.pi/4,
            'blade_loads': blade_loads,
            'chord': 0.1,
            'alpha': np.radians(8.0),
            'omega': 50.0
        }

        corrected_coefficients = effects_manager.apply_all_corrections(
            base_coefficients, correction_params
        )

        print(f"   ✅ 综合三维修正: Cl={corrected_coefficients[0]:.3f}, Cd={corrected_coefficients[1]:.4f}")

        elapsed_time = time.time() - start_time
        performance_metrics['three_dimensional_effects'] = elapsed_time

        print(f"   ✅ 三维效应模型测试成功 (耗时: {elapsed_time:.3f}s)")

        return True

    except Exception as e:
        print(f"   ❌ 三维效应模型测试失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_integration_and_performance():
    """测试7: 集成测试和性能验证"""
    print("\n🔧 测试7: 集成测试和性能验证...")

    try:
        start_time = time.time()

        # 创建完整的求解器配置
        config = SolverConfig(
            fidelity_level=FidelityLevel.HIGH,
            enable_dynamic_stall=True,
            enable_free_wake=True,
            max_iterations=30,
            convergence_tolerance=1e-6
        )

        # 创建BEMT求解器（包含L-B模型）
        bemt_solver = BEMTSolver(config)

        # 创建UVLM求解器（包含自由尾迹）
        uvlm_solver = UVLMSolver(config)

        # 创建噪声求解器
        noise_config = {
            'enable_tbl_te_noise': True,
            'enable_separation_noise': True,
            'enable_tip_vortex_noise': True,
            'rotor_radius': 1.0,
            'air_density': 1.225,
            'sound_speed': 343.0
        }
        bmp_solver = BMPSolver(noise_config)

        # 测试数据传递和接口兼容性
        V_inf = np.array([15.0, 0.0, 0.0])
        omega_rotor = 60.0

        # BEMT求解
        bemt_result = bemt_solver.solve(V_inf, omega_rotor)

        # 使用BEMT结果进行噪声计算
        if 'velocity_distribution' in bemt_result:
            velocity_dist = bemt_result['velocity_distribution']
            alpha_dist = bemt_result.get('alpha_distribution', [np.radians(5.0)] * len(velocity_dist))

            total_noise = 0.0
            for i, (vel, alpha) in enumerate(zip(velocity_dist[:3], alpha_dist[:3])):  # 限制计算量
                r_station = 0.5 + i * 0.2
                chord = 0.1

                frequencies, psd = bmp_solver._calculate_broadband_noise_psd(
                    vel, alpha, r_station, chord
                )
                total_noise += np.trapz(psd, frequencies)

            print(f"   ✅ 气动-声学耦合计算成功，总噪声: {total_noise:.2e} Pa²")

        # 性能基准测试
        n_iterations = 5
        computation_times = []

        for i in range(n_iterations):
            iter_start = time.time()

            # 简化的计算循环
            result = bemt_solver.solve(V_inf, omega_rotor)

            iter_time = time.time() - iter_start
            computation_times.append(iter_time)

        avg_computation_time = np.mean(computation_times)
        std_computation_time = np.std(computation_times)

        print(f"   ✅ 性能基准测试完成")
        print(f"   - 平均计算时间: {avg_computation_time:.3f} ± {std_computation_time:.3f} s")
        print(f"   - 计算稳定性: {(1 - std_computation_time/avg_computation_time)*100:.1f}%")

        elapsed_time = time.time() - start_time
        performance_metrics['integration_performance'] = elapsed_time

        print(f"   ✅ 集成测试和性能验证成功 (耗时: {elapsed_time:.3f}s)")

        return True

    except Exception as e:
        print(f"   ❌ 集成测试和性能验证失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始新实现功能全面测试...")
    print("=" * 60)
    
    # 安全导入模块
    import_success, modules = safe_import()
    if not import_success:
        print("❌ 模块导入失败，无法继续测试")
        return
    
    # 执行测试
    tests = [
        ("L-B动态失速与BEMT集成", test_lb_bemt_integration),
        ("UVLM自由尾迹演化", test_uvlm_free_wake),
        ("BPM完整噪声模型", test_bmp_complete_noise_model),
        ("FW-H时间历史管理", test_fwh_time_history_manager),
        ("跨声速修正算法", test_advanced_compressibility_correction),
        ("三维效应模型", test_three_dimensional_effects),
        ("集成测试和性能验证", test_integration_and_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            test_results[test_name] = result
        except Exception as e:
            print(f"   ❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
            test_results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    # 性能统计
    if performance_metrics:
        print(f"\n⏱️  性能统计:")
        for test_name, elapsed_time in performance_metrics.items():
            print(f"   {test_name}: {elapsed_time:.3f}s")
    
    if passed == total:
        print("🎉 所有测试通过！新实现功能验证成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查和修复")

if __name__ == "__main__":
    main()
