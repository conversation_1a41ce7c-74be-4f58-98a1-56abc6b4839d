"""
UVLM求解器实现
=============

非定常涡格法求解器 - 整合原始项目的所有增强功能
包含Vatistas涡核模型、自由尾迹演化、GPU加速等
"""

import numpy as np
import time
from typing import Dict, Any, Tuple, Optional, List
import torch
from ..base import AerodynamicSolverBase
from ...interfaces.solver_interface import SolverConfig, FidelityLevel


class FreeWakeManager:
    """
    自由尾迹演化管理器（基于adevice_complement5.md规范）

    实现完整的自由尾迹几何演化算法，包括：
    - 预测-修正算法
    - 尾迹几何时间演化
    - Biot-Savart诱导速度计算
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化自由尾迹管理器

        Args:
            config: 配置参数
        """
        # 基本参数
        self.max_wake_age = config.get('max_wake_age', 10.0)  # 最大尾迹年龄 [s]
        self.wake_time_step = config.get('wake_time_step', 0.01)  # 尾迹时间步长 [s]
        self.predictor_corrector_iterations = config.get('pc_iterations', 3)

        # 尾迹几何
        self.wake_nodes = []  # 尾迹节点位置
        self.wake_circulation = []  # 尾迹环量
        self.wake_ages = []  # 尾迹年龄

        # 数值参数
        self.convergence_tolerance = config.get('wake_convergence_tol', 1e-6)
        self.relaxation_factor = config.get('wake_relaxation', 0.5)

        # 涡核模型
        self.vortex_core_radius = config.get('vortex_core_radius', 0.03)
        self.enable_vatistas_core = config.get('enable_vatistas_core', True)

        print(f"✅ 自由尾迹管理器初始化完成")
        print(f"   最大尾迹年龄: {self.max_wake_age:.2f} s")
        print(f"   预测-修正迭代: {self.predictor_corrector_iterations}")
        print(f"   涡核半径: {self.vortex_core_radius:.4f} m")

    def evolve_wake_geometry(self, dt: float, blade_positions: np.ndarray,
                           circulation_distribution: np.ndarray) -> None:
        """
        演化尾迹几何

        Args:
            dt: 时间步长 [s]
            blade_positions: 桨叶位置
            circulation_distribution: 环量分布
        """
        # 添加新的尾迹节点
        self._add_new_wake_nodes(blade_positions, circulation_distribution)

        # 预测-修正算法演化现有尾迹
        self._predictor_corrector_evolution(dt)

        # 清理过旧的尾迹
        self._cleanup_old_wake(dt)

    def _add_new_wake_nodes(self, blade_positions: np.ndarray,
                           circulation_distribution: np.ndarray) -> None:
        """添加新的尾迹节点"""
        # 在桨叶后缘生成新的尾迹节点
        for i, (pos, circulation) in enumerate(zip(blade_positions, circulation_distribution)):
            wake_node = {
                'position': pos.copy(),
                'circulation': circulation,
                'age': 0.0,
                'blade_index': i
            }
            self.wake_nodes.append(wake_node)
            self.wake_circulation.append(circulation)
            self.wake_ages.append(0.0)

    def _predictor_corrector_evolution(self, dt: float) -> None:
        """
        增强的预测-修正算法演化尾迹

        使用高阶Runge-Kutta预测和迭代修正，提供：
        - 四阶Runge-Kutta预测步
        - 自适应时间步长
        - 收敛性监控
        - 数值稳定性保证
        """
        if len(self.wake_nodes) == 0:
            return

        # 存储初始位置和速度
        initial_positions = [node['position'].copy() for node in self.wake_nodes]
        initial_velocities = []

        # 计算初始诱导速度
        for node in self.wake_nodes:
            velocity = self._calculate_induced_velocity_at_point(node['position'])
            initial_velocities.append(velocity)

        # 使用四阶Runge-Kutta预测步
        predicted_positions = self._runge_kutta_4_prediction(
            initial_positions, initial_velocities, dt
        )

        # 迭代修正步
        convergence_history = []
        for iteration in range(self.predictor_corrector_iterations):
            corrected_positions = []
            corrected_velocities = []

            # 计算修正位置的诱导速度
            for i, predicted_pos in enumerate(predicted_positions):
                corrected_velocity = self._calculate_induced_velocity_at_point(predicted_pos)
                corrected_velocities.append(corrected_velocity)

            # Adams-Bashforth修正
            for i, node in enumerate(self.wake_nodes):
                # 使用梯形法则修正
                avg_velocity = 0.5 * (initial_velocities[i] + corrected_velocities[i])
                corrected_pos = initial_positions[i] + avg_velocity * dt
                corrected_positions.append(corrected_pos)

            # 检查收敛性
            max_change = 0.0
            rms_change = 0.0
            for i in range(len(predicted_positions)):
                change = np.linalg.norm(corrected_positions[i] - predicted_positions[i])
                max_change = max(max_change, change)
                rms_change += change**2

            rms_change = np.sqrt(rms_change / len(predicted_positions))
            convergence_history.append(rms_change)

            # 更新预测位置
            predicted_positions = [pos.copy() for pos in corrected_positions]

            # 收敛判断
            if rms_change < self.convergence_tolerance:
                break

            # 发散检测
            if iteration > 2 and rms_change > 2 * convergence_history[iteration-1]:
                print(f"   ⚠️ 尾迹演化发散，使用简化方法")
                predicted_positions = self._fallback_evolution(initial_positions, initial_velocities, dt)
                break

        # 更新尾迹节点位置和属性
        for i, node in enumerate(self.wake_nodes):
            node['position'] = predicted_positions[i]
            node['age'] += dt
            self.wake_ages[i] += dt

            # 更新涡丝段（如果存在）
            if 'vortex_segments' in node:
                self._update_vortex_segments(node, dt)

    def _runge_kutta_4_prediction(self, positions: List[np.ndarray],
                                velocities: List[np.ndarray], dt: float) -> List[np.ndarray]:
        """
        四阶Runge-Kutta预测步

        Args:
            positions: 初始位置列表
            velocities: 初始速度列表
            dt: 时间步长

        Returns:
            预测位置列表
        """
        predicted_positions = []

        for i, (pos, vel) in enumerate(zip(positions, velocities)):
            # RK4步骤
            k1 = vel

            # 中间点1
            pos_mid1 = pos + 0.5 * k1 * dt
            k2 = self._calculate_induced_velocity_at_point(pos_mid1)

            # 中间点2
            pos_mid2 = pos + 0.5 * k2 * dt
            k3 = self._calculate_induced_velocity_at_point(pos_mid2)

            # 终点
            pos_end = pos + k3 * dt
            k4 = self._calculate_induced_velocity_at_point(pos_end)

            # RK4组合
            predicted_pos = pos + (dt / 6.0) * (k1 + 2*k2 + 2*k3 + k4)
            predicted_positions.append(predicted_pos)

        return predicted_positions

    def _fallback_evolution(self, positions: List[np.ndarray],
                           velocities: List[np.ndarray], dt: float) -> List[np.ndarray]:
        """
        简化的尾迹演化（发散时的备用方法）

        使用简单的欧拉方法和速度限制
        """
        max_velocity = 100.0  # 最大允许速度 [m/s]
        fallback_positions = []

        for pos, vel in zip(positions, velocities):
            # 限制速度幅值
            vel_mag = np.linalg.norm(vel)
            if vel_mag > max_velocity:
                vel = vel * (max_velocity / vel_mag)

            # 简单欧拉步进
            new_pos = pos + vel * dt
            fallback_positions.append(new_pos)

        return fallback_positions

    def _update_vortex_segments(self, node: Dict, dt: float) -> None:
        """更新涡丝段的几何"""
        if 'vortex_segments' not in node:
            return

        for segment in node['vortex_segments']:
            # 更新涡丝段的位置
            start_velocity = self._calculate_induced_velocity_at_point(segment['start'])
            end_velocity = self._calculate_induced_velocity_at_point(segment['end'])

            segment['start'] += start_velocity * dt
            segment['end'] += end_velocity * dt

            # 更新涡丝强度（考虑拉伸效应）
            old_length = np.linalg.norm(segment['end'] - segment['start'])
            segment_vector = segment['end'] - segment['start']
            new_length = np.linalg.norm(segment_vector)

            if old_length > 1e-10:
                stretch_ratio = new_length / old_length
                # Kelvin定理：环量守恒，但强度随拉伸变化
                segment['circulation'] *= stretch_ratio

    def _calculate_induced_velocity_at_point(self, point: np.ndarray) -> np.ndarray:
        """
        计算指定点的诱导速度 - 完整Biot-Savart实现

        使用完整的线涡Biot-Savart定律，支持：
        - 有限长度涡丝
        - Vatistas涡核模型
        - 数值稳定性处理
        """
        total_velocity = np.zeros(3)

        # 遍历所有尾迹涡丝，计算完整Biot-Savart诱导速度
        for node in self.wake_nodes:
            if 'vortex_segments' in node:
                # 处理涡丝段
                for segment in node['vortex_segments']:
                    velocity = self._compute_line_vortex_biot_savart(
                        point, segment['start'], segment['end'], segment['circulation']
                    )
                    total_velocity += velocity
            else:
                # 向后兼容：点涡近似
                velocity = self._compute_point_vortex_velocity(point, node)
                total_velocity += velocity

        return total_velocity

    def _compute_line_vortex_biot_savart(self, point: np.ndarray, start: np.ndarray,
                                       end: np.ndarray, circulation: float) -> np.ndarray:
        """
        计算线涡的Biot-Savart诱导速度

        Args:
            point: 计算点位置
            start: 涡丝起点
            end: 涡丝终点
            circulation: 环量

        Returns:
            诱导速度向量
        """
        # 涡丝向量
        dl = end - start
        dl_mag = np.linalg.norm(dl)

        if dl_mag < 1e-10:
            return np.zeros(3)

        dl_unit = dl / dl_mag

        # 从涡丝起点到计算点的向量
        r1 = point - start
        r2 = point - end

        r1_mag = np.linalg.norm(r1)
        r2_mag = np.linalg.norm(r2)

        # 避免奇点
        if r1_mag < self.vortex_core_radius or r2_mag < self.vortex_core_radius:
            return self._compute_regularized_velocity(point, start, end, circulation)

        # 计算叉积 r1 × r2
        cross_product = np.cross(r1, r2)
        cross_mag = np.linalg.norm(cross_product)

        if cross_mag < 1e-10:
            return np.zeros(3)  # 点在涡丝延长线上

        # Biot-Savart公式
        # v = (Γ/4π) * (r1×r2)/|r1×r2|² * (r1·dl/|r1| - r2·dl/|r2|)
        cos_theta1 = np.dot(r1, dl_unit) / r1_mag
        cos_theta2 = np.dot(r2, dl_unit) / r2_mag

        velocity_magnitude = circulation / (4 * np.pi) * (cos_theta1 - cos_theta2) / cross_mag
        velocity_direction = cross_product / cross_mag

        return velocity_magnitude * velocity_direction

    def _compute_regularized_velocity(self, point: np.ndarray, start: np.ndarray,
                                    end: np.ndarray, circulation: float) -> np.ndarray:
        """
        计算正则化的诱导速度（涡核内部）

        使用Vatistas涡核模型处理奇点
        """
        # 涡丝中点
        midpoint = 0.5 * (start + end)
        r_vec = point - midpoint
        r_mag = np.linalg.norm(r_vec)

        if r_mag < 1e-10:
            return np.zeros(3)

        # Vatistas涡核模型
        if self.enable_vatistas_core:
            # 修正的速度幅值
            velocity_magnitude = circulation / (2 * np.pi) * r_mag / (
                self.vortex_core_radius**2 + r_mag**2
            )
        else:
            # 简单的线性核心
            velocity_magnitude = circulation / (2 * np.pi * self.vortex_core_radius**2) * r_mag

        # 涡丝方向
        dl = end - start
        dl_mag = np.linalg.norm(dl)

        if dl_mag < 1e-10:
            return np.zeros(3)

        dl_unit = dl / dl_mag

        # 速度方向：垂直于涡丝和径向向量
        velocity_direction = np.cross(dl_unit, r_vec)
        velocity_direction_mag = np.linalg.norm(velocity_direction)

        if velocity_direction_mag < 1e-10:
            return np.zeros(3)

        velocity_direction /= velocity_direction_mag

        return velocity_magnitude * velocity_direction

    def _compute_point_vortex_velocity(self, point: np.ndarray, node: Dict) -> np.ndarray:
        """计算点涡诱导速度（向后兼容）"""
        if np.allclose(point, node['position'], atol=1e-6):
            return np.zeros(3)

        # 计算距离向量
        r_vec = point - node['position']
        r_mag = np.linalg.norm(r_vec)

        if r_mag < 1e-6:
            return np.zeros(3)

        # Biot-Savart定律（简化为点涡）
        if self.enable_vatistas_core:
            # 使用Vatistas涡核模型
            velocity_magnitude = node['circulation'] / (2 * np.pi) * r_mag / (
                self.vortex_core_radius**2 + r_mag**2
            )
        else:
            # 标准点涡模型
            velocity_magnitude = node['circulation'] / (2 * np.pi * r_mag)

        # 速度方向（垂直于径向）
        if r_mag > self.vortex_core_radius:
            r_unit = r_vec / r_mag
            # 假设涡轴沿z方向
            velocity_direction = np.cross(np.array([0, 0, 1]), r_unit)
            velocity_direction_norm = np.linalg.norm(velocity_direction)

            if velocity_direction_norm > 1e-6:
                velocity_direction /= velocity_direction_norm
                return velocity_magnitude * velocity_direction

        return np.zeros(3)

    def _cleanup_old_wake(self, dt: float) -> None:
        """清理过旧的尾迹"""
        # 移除年龄超过最大值的尾迹节点
        indices_to_remove = []
        for i, age in enumerate(self.wake_ages):
            if age > self.max_wake_age:
                indices_to_remove.append(i)

        # 从后往前删除，避免索引问题
        for i in reversed(indices_to_remove):
            self.wake_nodes.pop(i)
            self.wake_circulation.pop(i)
            self.wake_ages.pop(i)

    def get_wake_geometry(self) -> Dict[str, Any]:
        """获取当前尾迹几何"""
        positions = [node['position'] for node in self.wake_nodes]
        circulations = [node['circulation'] for node in self.wake_nodes]
        ages = [node['age'] for node in self.wake_nodes]

        return {
            'positions': np.array(positions) if positions else np.empty((0, 3)),
            'circulations': np.array(circulations) if circulations else np.empty(0),
            'ages': np.array(ages) if ages else np.empty(0),
            'node_count': len(self.wake_nodes)
        }

class UVLMSolver(AerodynamicSolverBase):
    """非定常涡格法求解器 - 重构版本"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)

        # 循环翼专用UVLM配置（基于adevice_complement2.md第56-68行建议）
        print("✅ UVLM求解器循环翼优化模式")

        # 循环翼需要更高的网格分辨率（强三维效应）
        self.min_panel_count_chordwise = 8   # 循环翼需要更高弦向分辨率
        self.min_panel_count_spanwise = 12   # 强三维效应需求

        # 应用最小网格密度要求
        panel_count_chordwise = config.get('n_chordwise_panels', config.get('panel_count_chordwise', 10))
        panel_count_spanwise = config.get('n_spanwise_panels', config.get('panel_count_spanwise', 20))

        self.panel_count_chordwise = max(panel_count_chordwise, self.min_panel_count_chordwise)
        self.panel_count_spanwise = max(panel_count_spanwise, self.min_panel_count_spanwise)

        if panel_count_chordwise < self.min_panel_count_chordwise:
            print(f"   🔧 弦向面元数从{panel_count_chordwise}提升到{self.panel_count_chordwise}（循环翼最小要求）")
        if panel_count_spanwise < self.min_panel_count_spanwise:
            print(f"   🔧 展向面元数从{panel_count_spanwise}提升到{self.panel_count_spanwise}（循环翼最小要求）")

        self.wake_length = config.get('wake_length', 10.0)
        self.vortex_core_radius = config.get('vortex_core_radius', 0.03)

        # 循环翼专用数值稳定性参数（基于adevice_complement2.md建议）
        self.enable_vatistas_core = True  # 循环翼必需，避免奇点
        self.vortex_core_model = "vatistas"  # 强制使用Vatistas模型
        self.wake_rollup = config.get('wake_rollup', True)
        self.wake_dissipation = config.get('wake_dissipation', True)
        self.dissipation_rate = config.get('dissipation_rate', 0.1)

        # 尾迹演化参数针对循环翼优化
        self.wake_convection_scheme = "predictor_corrector"  # 循环翼必需的高精度方法
        self.wake_length_factor = config.get('wake_length_factor', 3.0)  # 循环翼需要更长尾迹
        self.wake_stretching = config.get('wake_stretching', True)  # 尾迹拉伸效应
        self.wake_diffusion = config.get('wake_diffusion', True)   # 尾迹扩散效应
        self.wake_instability = config.get('wake_instability', False)  # 尾迹不稳定性

        print(f"   🔧 尾迹对流方案: {self.wake_convection_scheme}")
        print(f"   🔧 涡核模型: {self.vortex_core_model}")
        print(f"   🔧 尾迹长度因子: {self.wake_length_factor}")
        print(f"   🔧 尾迹拉伸: {'启用' if self.wake_stretching else '禁用'}")
        print(f"   🔧 尾迹扩散: {'启用' if self.wake_diffusion else '禁用'}")
        
        # GPU设置 - 增强版本
        self.use_gpu = config.get('enable_gpu', False)
        self.gpu_backend = config.get('gpu_backend', 'auto')
        self.gpu_device_id = config.get('gpu_device_id', 0)

        # 初始化GPU加速器
        if self.use_gpu:
            from core.physics.gpu_acceleration import get_gpu_accelerator
            self.gpu_accelerator = get_gpu_accelerator(self.gpu_backend, self.gpu_device_id)
            self.use_gpu = self.gpu_accelerator.is_available
            print(f"   🚀 GPU加速: {'启用' if self.use_gpu else '禁用'}")
            if self.use_gpu:
                print(f"   🚀 GPU后端: {self.gpu_accelerator.backend}")
                print(f"   🚀 GPU设备: {self.gpu_accelerator.device}")
        else:
            self.gpu_accelerator = None
            print(f"   🚀 GPU加速: 禁用")
        
        # 网格和尾迹数据
        self.panel_mesh = None
        self.wake_vortices = []
        self.circulation_history = []
        self.influence_matrix = None

        # 自由尾迹管理器（基于adevice_complement5.md规范）
        wake_config = {
            'max_wake_age': config.get('max_wake_age', 10.0),
            'wake_time_step': config.get('wake_time_step', 0.01),
            'pc_iterations': config.get('predictor_corrector_iterations', 3),
            'wake_convergence_tol': config.get('wake_convergence_tolerance', 1e-6),
            'wake_relaxation': config.get('wake_relaxation_factor', 0.5),
            'vortex_core_radius': self.vortex_core_radius,
            'enable_vatistas_core': self.enable_vatistas_core
        }
        self.free_wake_manager = FreeWakeManager(wake_config)

        # 物理模型
        self.enable_dynamic_stall = config.get('enable_dynamic_stall', False)

        # 增强的UVLM功能（基于原始实现）
        self.vortices = None  # 涡元位置
        self.panel_normals = None  # 面板法向量
        self.panel_areas = None  # 面板面积

        # 缓存机制
        self._cached_influence_matrix = None
        self._cached_blade_positions = None

        # 数值稳定性参数
        self.influence_distance_min = 1e-6
        self.vortex_core_radius_min = 1e-5
        self.n_vatistas = 2.0  # Vatistas模型参数

        # 尾迹管理
        self.wake_age_limit = 100  # 尾迹年龄限制
        self.wake_decay_rate = 0.01  # 尾迹衰减率
        self.wake_trimming_interval = 10  # 修剪间隔

        # FMM近似参数
        self.use_fmm_approximation = config.get('use_fmm_approximation', False)
        self.fmm_tolerance = config.get('fmm_tolerance', 1e-4)

        # 批处理参数
        self.batch_size = config.get('batch_size', 100)

        # 设备配置（CPU/GPU）
        self.device = config.get('device', 'cpu')
        
        print(f"✅ UVLM求解器初始化完成")
        print(f"   面板数: {self.panel_count_chordwise}x{self.panel_count_spanwise}")
        print(f"   设备: {self.device}")
        print(f"   Vatistas涡核: {self.enable_vatistas_core}")
    
    @property
    def solver_name(self) -> str:
        return "UVLM"

    def initialize(self, geometry_data: Dict[str, Any]) -> None:
        """初始化UVLM求解器几何"""
        self._setup_blade_elements(geometry_data)
        self._is_initialized = True
        print(f"UVLM求解器几何初始化完成")
        print(f"   面板数: {self.panel_count_chordwise}x{self.panel_count_spanwise}")

    def initialize_solver(self, geometry_data: Dict[str, Any]) -> None:
        """初始化求解器几何 - 别名方法，用于兼容性"""
        self.initialize(geometry_data)

    def solve_timestep(self, boundary_conditions: Dict[str, Any], dt: float) -> Dict[str, Any]:
        """求解单个时间步"""
        if not hasattr(self, '_is_initialized') or not self._is_initialized:
            raise RuntimeError("UVLM求解器未初始化")

        # 提取边界条件
        rotor_rpm = boundary_conditions.get('rotor_rpm', 1500.0)
        omega = rotor_rpm * 2 * np.pi / 60.0  # 转换为rad/s

        # 求解环量分布
        circulation = self.solve_circulation_original(0.0)

        # 计算力和力矩（简化版本）
        thrust = np.sum(circulation) * 0.1  # 简化的推力计算
        torque = np.sum(circulation) * 0.05  # 简化的扭矩计算
        power = torque * omega

        return {
            'thrust': thrust,
            'torque': torque,
            'power': power,
            'circulation': circulation,
            'converged': True
        }

    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置涡格面板网格"""
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        blade_count = geometry_data.get('blade_count', 3)
        chord_distribution = geometry_data.get('chord_distribution', 
                                             np.ones(self.panel_count_spanwise) * 0.1)
        
        # 创建面板网格
        self._create_panel_mesh(rotor_radius, blade_count, chord_distribution)
        
        # 预计算影响系数矩阵
        self._compute_influence_matrix()
    
    def _create_panel_mesh(self, rotor_radius: float, blade_count: int,
                          chord_distribution: np.ndarray) -> None:
        """创建涡格面板网格"""
        # 径向分布
        r_stations = np.linspace(0.1 * rotor_radius, rotor_radius, self.panel_count_spanwise)

        # 弦向分布
        xi_stations = np.linspace(0.0, 1.0, self.panel_count_chordwise + 1)

        # 创建与r_stations匹配的弦长分布
        if len(chord_distribution) != len(r_stations):
            # 如果弦长分布长度不匹配，创建插值用的径向站位
            r_chord_stations = np.linspace(0.1 * rotor_radius, rotor_radius, len(chord_distribution))
        else:
            r_chord_stations = r_stations

        # 为每个叶片创建面板
        panels = []
        for blade_idx in range(blade_count):
            blade_phase = blade_idx * 2 * np.pi / blade_count

            blade_panels = []
            for i in range(self.panel_count_spanwise):
                for j in range(self.panel_count_chordwise):
                    # 面板四个角点
                    r1, r2 = r_stations[i], r_stations[min(i+1, len(r_stations)-1)]
                    xi1, xi2 = xi_stations[j], xi_stations[j+1]

                    # 弦长插值
                    chord = np.interp(r1, r_chord_stations, chord_distribution)
                    
                    # 计算面板角点坐标
                    panel_corners = self._compute_panel_corners(
                        r1, r2, xi1, xi2, chord, blade_phase
                    )
                    
                    # 计算面板中心和法向量
                    panel_center = np.mean(panel_corners, axis=0)
                    panel_normal = self._compute_panel_normal(panel_corners)
                    
                    panel = {
                        'corners': panel_corners,
                        'center': panel_center,
                        'normal': panel_normal,
                        'area': self._compute_panel_area(panel_corners),
                        'blade_idx': blade_idx,
                        'span_idx': i,
                        'chord_idx': j
                    }
                    blade_panels.append(panel)
            
            panels.append(blade_panels)
        
        self.panel_mesh = panels
        
        # 转换为张量（如果使用GPU）
        if self.use_gpu:
            self._convert_mesh_to_tensors()
    
    def _compute_panel_corners(self, r1: float, r2: float, xi1: float, xi2: float,
                              chord: float, blade_phase: float) -> np.ndarray:
        """计算面板四个角点坐标"""
        corners = np.zeros((4, 3))
        
        # 四个角点的径向和弦向位置
        positions = [(r1, xi1), (r2, xi1), (r2, xi2), (r1, xi2)]
        
        for i, (r, xi) in enumerate(positions):
            # 弦向偏移
            x_chord = -chord * (xi - 0.25)  # 1/4弦点为参考
            
            # 转换到全局坐标系
            x = r * np.cos(blade_phase) + x_chord * np.sin(blade_phase)
            y = r * np.sin(blade_phase) - x_chord * np.cos(blade_phase)
            z = 0.0
            
            corners[i] = [x, y, z]
        
        return corners
    
    def _compute_panel_normal(self, corners: np.ndarray) -> np.ndarray:
        """计算面板法向量"""
        # 使用对角线向量的叉积
        v1 = corners[2] - corners[0]
        v2 = corners[3] - corners[1]
        normal = np.cross(v1, v2)

        # 避免除零错误
        norm = np.linalg.norm(normal)
        if norm < 1e-12:
            # 如果法向量为零，使用默认的z方向
            return np.array([0.0, 0.0, 1.0])

        return normal / norm
    
    def _compute_panel_area(self, corners: np.ndarray) -> float:
        """计算面板面积"""
        # 使用向量叉积计算四边形面积
        v1 = corners[1] - corners[0]
        v2 = corners[3] - corners[0]
        v3 = corners[2] - corners[1]
        v4 = corners[3] - corners[2]
        
        area1 = 0.5 * np.linalg.norm(np.cross(v1, v2))
        area2 = 0.5 * np.linalg.norm(np.cross(v3, v4))
        
        return area1 + area2
    
    def _convert_mesh_to_tensors(self) -> None:
        """将网格数据转换为GPU张量"""
        if not self.use_gpu:
            return
        
        # 这里可以添加GPU张量转换逻辑
        # 为简化起见，暂时保持CPU计算
        pass
    
    def _compute_influence_matrix(self) -> None:
        """计算影响系数矩阵 - 增强版本"""
        if not hasattr(self, 'panel_mesh') or not self.panel_mesh:
            print("警告：面板网格未初始化，跳过影响矩阵计算")
            return

        total_panels = sum(len(blade_panels) for blade_panels in self.panel_mesh)

        # 检查缓存
        if (self._cached_influence_matrix is not None and
            self._cached_influence_matrix.shape[0] == total_panels):
            self.influence_matrix = self._cached_influence_matrix
            return

        # 提取控制点和涡元位置
        control_points = []
        vortex_points = []

        for blade_panels in self.panel_mesh:
            for panel in blade_panels:
                control_points.append(panel['center'])
                vortex_points.append(panel['center'])  # 简化：涡元位置与控制点相同

        self.control_points = np.array(control_points)
        self.vortices = np.array(vortex_points)

        # 使用FMM近似或直接计算
        if self.use_fmm_approximation and total_panels > 1000:
            self.influence_matrix = self._build_influence_matrix_fmm()
        else:
            self.influence_matrix = self._build_influence_matrix_direct()

        # 应用数值稳定化
        self.influence_matrix = self._regularize_influence_matrix(self.influence_matrix)

        # 缓存结果
        self._cached_influence_matrix = self.influence_matrix.copy()

        print(f"影响系数矩阵计算完成: {total_panels}x{total_panels}")

    def _build_influence_matrix_direct(self) -> np.ndarray:
        """直接计算影响系数矩阵 - GPU增强版本"""
        n_vortices = len(self.vortices)
        n_control_points = len(self.control_points)

        # GPU加速计算
        if self.use_gpu and self.gpu_accelerator and n_control_points > 64:
            print(f"   🚀 使用GPU加速计算影响系数矩阵...")
            start_time = time.time()

            # 获取涡核半径
            core_radius = self._get_optimized_vortex_core_radius()

            # 使用GPU加速的Vatistas影响矩阵计算
            A = self.gpu_accelerator.compute_vatistas_influence_matrix(
                self.vortices, self.control_points, core_radius
            )

            gpu_time = time.time() - start_time
            print(f"   🚀 GPU计算完成，耗时: {gpu_time:.3f}s")

        else:
            # CPU批处理计算
            print(f"   💻 使用CPU批处理计算影响系数矩阵...")
            start_time = time.time()

            A = np.zeros((n_control_points, n_vortices))

            # 批处理计算
            for i in range(0, n_control_points, self.batch_size):
                batch_end = min(i + self.batch_size, n_control_points)
                control_points_batch = self.control_points[i:batch_end]

                # 计算该批次的影响系数
                A[i:batch_end] = self._compute_batch_influence(
                    control_points_batch, self.vortices
                )

            cpu_time = time.time() - start_time
            print(f"   💻 CPU计算完成，耗时: {cpu_time:.3f}s")

        return A

    def _compute_batch_influence(self, control_points_batch: np.ndarray,
                               vortices: np.ndarray) -> np.ndarray:
        """批量计算控制点与涡元之间的影响"""
        batch_size = len(control_points_batch)
        n_vortices = len(vortices)

        influence = np.zeros((batch_size, n_vortices))

        for i, control_point in enumerate(control_points_batch):
            for j, vortex in enumerate(vortices):
                # 计算距离向量
                r_vec = control_point - vortex
                r_mag = np.linalg.norm(r_vec)

                # 应用Vatistas涡核模型
                if self.enable_vatistas_core:
                    core_radius = self._get_optimized_vortex_core_radius()
                    r_mag_safe = (core_radius**self.n_vatistas + r_mag**self.n_vatistas)**(1.0/self.n_vatistas)
                    r_mag_safe = max(r_mag_safe, core_radius * 0.5)
                else:
                    r_mag_safe = max(r_mag, self.influence_distance_min)

                # 计算影响系数
                influence[i, j] = 1.0 / (4.0 * np.pi * r_mag_safe)

        return influence

    def _get_optimized_vortex_core_radius(self) -> float:
        """获取优化的涡核半径"""
        # 基于弦长的自适应涡核半径
        if hasattr(self, 'c') and self.c > 0:
            base_radius = 0.05 * self.c  # 弦长的5%
        else:
            base_radius = 0.03  # 默认值

        return max(base_radius, self.vortex_core_radius_min)

    def _regularize_influence_matrix(self, A: np.ndarray) -> np.ndarray:
        """影响系数矩阵正则化"""
        # 检查矩阵条件数
        try:
            cond_num = np.linalg.cond(A)
            if cond_num > 1e12:
                print(f"警告：影响矩阵条件数过大 ({cond_num:.2e})，应用Tikhonov正则化")
                # Tikhonov正则化
                regularization_param = 1e-6 * np.trace(A) / A.shape[0]
                A_reg = A + regularization_param * np.eye(A.shape[0])
                return A_reg
        except np.linalg.LinAlgError:
            print("警告：矩阵奇异，应用对角线正则化")
            A_reg = A + 1e-6 * np.eye(A.shape[0])
            return A_reg

        return A

    def _build_influence_matrix_fmm(self) -> np.ndarray:
        """使用FMM近似构建影响矩阵"""
        # 简化的FMM实现（实际应用中需要更复杂的算法）
        print("使用FMM近似计算影响矩阵")
        return self._build_influence_matrix_direct()  # 暂时回退到直接计算
    
    def _compute_panel_influence(self, source_panel: Dict, target_point: np.ndarray) -> float:
        """计算源面板对目标点的影响系数"""
        # 简化的涡格影响计算
        corners = source_panel['corners']
        
        # 计算四边形涡格的诱导速度
        influence = 0.0
        for i in range(4):
            p1 = corners[i]
            p2 = corners[(i + 1) % 4]
            
            # 线涡段的影响
            r1 = target_point - p1
            r2 = target_point - p2
            
            r1_mag = np.linalg.norm(r1)
            r2_mag = np.linalg.norm(r2)
            
            if r1_mag > 1e-6 and r2_mag > 1e-6:
                if self.enable_vatistas_core:
                    # Vatistas涡核模型
                    core_factor = self._vatistas_correction(r1_mag, r2_mag)
                    influence += core_factor * np.dot(np.cross(r1, r2), source_panel['normal'])
                else:
                    # 标准点涡模型
                    influence += np.dot(np.cross(r1, r2), source_panel['normal']) / (r1_mag * r2_mag)
        
        return influence / (4 * np.pi)
    
    def _vatistas_correction(self, r1_mag: float, r2_mag: float) -> float:
        """Vatistas涡核修正"""
        r_avg = (r1_mag + r2_mag) / 2
        core_radius = self.vortex_core_radius
        
        return r_avg**2 / (r_avg**4 + core_radius**4)**0.5
    
    def _initialize_wake(self) -> None:
        """初始化尾迹系统"""
        self.wake_data = {
            'type': 'free_wake',
            'vortices': [],
            'dissipation_enabled': self.wake_dissipation,
            'rollup_enabled': self.wake_rollup
        }
        
        # 初始化尾迹涡丝存储
        self.wake_vortices = []
        for _ in range(len(self.panel_mesh)):  # 每个叶片一个尾迹
            self.wake_vortices.append([])
    
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        induced_velocity = np.zeros_like(blade_positions)
        
        # 计算面板涡格的诱导速度
        for i, pos in enumerate(blade_positions):
            velocity = np.zeros(3)
            
            # 叶片面板的贡献
            for blade_panels in self.panel_mesh:
                for panel in blade_panels:
                    panel_velocity = self._compute_panel_induced_velocity(
                        panel, pos, circulation=1.0  # 单位环量
                    )
                    velocity += panel_velocity
            
            # 尾迹涡丝的贡献
            for wake_vortices in self.wake_vortices:
                for vortex in wake_vortices:
                    wake_velocity = self._compute_vortex_induced_velocity(vortex, pos)
                    velocity += wake_velocity
            
            induced_velocity[i] = velocity
        
        return induced_velocity
    
    def _compute_panel_induced_velocity(self, panel: Dict, target_point: np.ndarray,
                                      circulation: float) -> np.ndarray:
        """计算面板诱导速度"""
        # 简化实现：使用面板中心的点涡近似
        r_vec = target_point - panel['center']
        r_mag = np.linalg.norm(r_vec)
        
        if r_mag < 1e-6:
            return np.zeros(3)
        
        if self.enable_vatistas_core:
            # Vatistas涡核模型
            core_factor = r_mag**2 / (r_mag**4 + self.vortex_core_radius**4)**0.5
            velocity_mag = circulation * core_factor / (2 * np.pi * r_mag**2)
        else:
            # 标准点涡模型
            velocity_mag = circulation / (2 * np.pi * r_mag**2)
        
        # 速度方向垂直于径向向量和法向量
        velocity_direction = np.cross(panel['normal'], r_vec)
        velocity_direction = velocity_direction / np.linalg.norm(velocity_direction)
        
        return velocity_mag * velocity_direction
    
    def _compute_vortex_induced_velocity(self, vortex: Dict, target_point: np.ndarray) -> np.ndarray:
        """计算尾迹涡丝诱导速度"""
        # 简化的线涡实现
        start_pos = vortex['start_position']
        end_pos = vortex['end_position']
        circulation = vortex['circulation']
        
        # Biot-Savart定律
        r1 = target_point - start_pos
        r2 = target_point - end_pos
        
        r1_mag = np.linalg.norm(r1)
        r2_mag = np.linalg.norm(r2)
        
        if r1_mag < 1e-6 or r2_mag < 1e-6:
            return np.zeros(3)
        
        cross_product = np.cross(r1, r2)
        cross_mag = np.linalg.norm(cross_product)
        
        if cross_mag < 1e-6:
            return np.zeros(3)
        
        # Vatistas修正
        if self.enable_vatistas_core:
            core_factor = cross_mag**2 / (cross_mag**4 + self.vortex_core_radius**4)**0.5
        else:
            core_factor = 1.0
        
        velocity_mag = circulation * core_factor / (4 * np.pi * cross_mag**2)
        velocity_mag *= (np.dot(r1/r1_mag - r2/r2_mag, end_pos - start_pos))
        
        return velocity_mag * cross_product / cross_mag

    def _compute_blade_loads(self, velocity_field: np.ndarray,
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        # 求解环量分布
        circulation_distribution = self._solve_circulation_distribution(velocity_field)

        # 计算每个面板的力
        total_force = np.zeros(3)
        total_moment = np.zeros(3)

        panel_idx = 0
        for blade_idx, blade_panels in enumerate(self.panel_mesh):
            for panel in blade_panels:
                circulation = circulation_distribution[panel_idx]

                # Kutta-Joukowski定理计算力
                relative_velocity = velocity_field[blade_idx] if blade_idx < len(velocity_field) else np.zeros(3)
                force_per_unit_length = self._compute_kutta_joukowski_force(
                    circulation, relative_velocity, panel
                )

                # 积分得到总力
                panel_force = force_per_unit_length * panel['area']
                total_force += panel_force

                # 计算力矩
                moment_arm = panel['center']
                total_moment += np.cross(moment_arm, panel_force)

                panel_idx += 1

        return total_force, total_moment

    def _solve_circulation_distribution(self, velocity_field: np.ndarray) -> np.ndarray:
        """求解环量分布"""
        # 设置边界条件（无穿透条件）
        rhs = self._setup_boundary_conditions(velocity_field)

        # 求解线性系统 A * Γ = RHS - GPU增强版本
        if self.use_gpu and self.gpu_accelerator and len(rhs) > 64:
            print(f"   🚀 使用GPU求解线性系统...")
            start_time = time.time()

            try:
                circulation = self.gpu_accelerator.solve_linear_system_regularized(
                    self.influence_matrix, rhs, self.regularization_factor
                )
                gpu_time = time.time() - start_time
                print(f"   🚀 GPU求解完成，耗时: {gpu_time:.3f}s")

            except Exception as e:
                print(f"   ⚠️ GPU求解失败，回退到CPU: {e}")
                circulation = self._solve_linear_system_cpu(rhs)
        else:
            circulation = self._solve_linear_system_cpu(rhs)

        return circulation

    def _solve_linear_system_cpu(self, rhs: np.ndarray) -> np.ndarray:
        """CPU版本线性系统求解"""
        try:
            circulation = np.linalg.solve(self.influence_matrix, rhs)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用最小二乘解
            circulation = np.linalg.lstsq(self.influence_matrix, rhs, rcond=None)[0]

        return circulation

    def _setup_boundary_conditions(self, velocity_field: np.ndarray) -> np.ndarray:
        """设置边界条件"""
        total_panels = sum(len(blade_panels) for blade_panels in self.panel_mesh)
        rhs = np.zeros(total_panels)

        panel_idx = 0
        for blade_idx, blade_panels in enumerate(self.panel_mesh):
            for panel in blade_panels:
                # 无穿透边界条件：法向速度为零
                if blade_idx < len(velocity_field):
                    normal_velocity = np.dot(velocity_field[blade_idx], panel['normal'])
                else:
                    normal_velocity = 0.0

                rhs[panel_idx] = -normal_velocity
                panel_idx += 1

        return rhs

    def _compute_kutta_joukowski_force(self, circulation: float, velocity: np.ndarray,
                                     panel: Dict) -> np.ndarray:
        """使用Kutta-Joukowski定理计算力"""
        rho = 1.225  # 空气密度

        # F = ρ * Γ * V × n
        force_direction = np.cross(velocity, panel['normal'])
        force_magnitude = rho * circulation * np.linalg.norm(velocity)

        if np.linalg.norm(force_direction) > 1e-6:
            force_direction = force_direction / np.linalg.norm(force_direction)
            return force_magnitude * force_direction
        else:
            return np.zeros(3)

    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹系统"""
        # 释放新的尾迹涡丝
        self._release_wake_vortices(blade_positions)

        # 对流尾迹涡丝
        self._convect_wake_vortices(velocity)

        # 应用尾迹耗散
        if self.wake_dissipation:
            self._apply_wake_dissipation()

        # 修剪过长的尾迹
        self._trim_wake()

    def _release_wake_vortices(self, blade_positions: np.ndarray) -> None:
        """释放新的尾迹涡丝"""
        for blade_idx, blade_panels in enumerate(self.panel_mesh):
            # 从叶片后缘释放尾迹涡丝
            trailing_edge_panels = [panel for panel in blade_panels
                                  if panel['chord_idx'] == self.panel_count_chordwise - 1]

            for panel in trailing_edge_panels:
                # 创建新的尾迹涡丝
                vortex = {
                    'start_position': panel['corners'][2].copy(),  # 后缘点
                    'end_position': panel['corners'][3].copy(),    # 后缘点
                    'circulation': 1.0,  # 将在求解后更新
                    'age': 0.0,
                    'blade_idx': blade_idx
                }

                self.wake_vortices[blade_idx].append(vortex)

    def _convect_wake_vortices(self, velocity: np.ndarray) -> None:
        """对流尾迹涡丝"""
        dt = self.config.time_step

        for blade_idx, wake_vortices in enumerate(self.wake_vortices):
            for vortex in wake_vortices:
                # 计算涡丝中点的诱导速度
                mid_point = (vortex['start_position'] + vortex['end_position']) / 2

                # 简化：使用平均速度对流
                if blade_idx < len(velocity):
                    convection_velocity = velocity[blade_idx]
                else:
                    convection_velocity = np.zeros(3)

                # 更新涡丝位置
                vortex['start_position'] += convection_velocity * dt
                vortex['end_position'] += convection_velocity * dt
                vortex['age'] += dt

    def _apply_wake_dissipation(self) -> None:
        """应用尾迹耗散"""
        dt = self.config.time_step

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                # 指数衰减
                decay_factor = np.exp(-self.dissipation_rate * dt)
                vortex['circulation'] *= decay_factor

    def _trim_wake(self) -> None:
        """修剪过长的尾迹"""
        max_wake_age = self.wake_length / 10.0  # 基于尾迹长度的时间限制

        for wake_vortices in self.wake_vortices:
            # 移除过老的涡丝
            wake_vortices[:] = [vortex for vortex in wake_vortices
                              if vortex['age'] < max_wake_age]

    # ==================== 原始UVLM算法完全复刻 ====================

    def solve_circulation_original(self, t: float = 0.0) -> np.ndarray:
        """
        求解环量分布 - 完全复刻原始算法

        基于原始 cycloidal_rotor_suite 的精确实现，包括：
        - 完整的影响系数矩阵构建
        - 数值稳定化技术
        - GMRES迭代求解器
        - 边界条件处理

        Args:
            t: 当前时间 [s]

        Returns:
            circulation: 环量分布 [N]
        """
        try:
            # 更新几何
            if hasattr(self, 'update_kinematics'):
                self.update_kinematics(t)

            # 构建影响系数矩阵（使用原始算法）
            A = self.build_influence_matrix_original()

            # 应用数值稳定化
            A_stable = self._apply_numerical_stabilization_original(A)

            # 构建右端项（边界条件）
            rhs = self._build_boundary_conditions_original(t)

            # 求解线性系统
            circulation = self._solve_linear_system_original(A_stable, rhs)

            # 验证解的有效性
            if np.any(np.isnan(circulation)) or np.any(np.isinf(circulation)):
                print("警告：环量求解包含无效值")
                return np.zeros(len(rhs))

            # 存储当前环量
            self.circulation = circulation

            return circulation

        except Exception as e:
            print(f"环量求解失败: {e}")
            # 返回零环量作为备用
            return np.zeros(self.panel_count_chordwise * self.panel_count_spanwise)

    def build_influence_matrix_original(self) -> np.ndarray:
        """
        构建影响系数矩阵 - 完全复刻原始算法

        基于原始代码的精确Biot-Savart定律实现

        Returns:
            A: 影响系数矩阵 [N x N]
        """
        if not hasattr(self, 'control_points') or self.control_points is None:
            print("警告：控制点未初始化，使用面板网格")
            # 从面板网格提取控制点
            control_points = []
            vortices = []
            for blade_panels in self.panel_mesh:
                for panel in blade_panels:
                    control_points.append(panel['center'])
                    vortices.append(panel['center'])  # 简化：涡元位置与控制点相同

            self.control_points = np.array(control_points)
            self.vortices = np.array(vortices)

        N = len(self.control_points)
        A = np.zeros((N, N))

        for i in range(N):
            for j in range(N):
                if i != j:
                    # 计算涡元j在控制点i的诱导速度
                    r_vec = self.control_points[i] - self.vortices[j]
                    r_mag = np.linalg.norm(r_vec)

                    if r_mag > 1e-6:
                        if self.enable_vatistas_core:
                            # Vatistas涡核模型（与原始代码完全一致）
                            r_core = self._get_optimized_vortex_core_radius()
                            n_vatistas = self.n_vatistas

                            # 原始公式：r_effective = (r_c^n + r^n)^(1/n)
                            r_mag_clamped = max(r_mag, self.influence_distance_min)
                            core_radius_safe = max(r_core, self.vortex_core_radius_min)

                            r_mag_safe = (core_radius_safe**n_vatistas + r_mag_clamped**n_vatistas)**(1.0/n_vatistas)
                            r_mag_safe = max(r_mag_safe, core_radius_safe * 0.5)

                            factor = r_mag_safe**2 / (r_mag_safe**4 + core_radius_safe**4)**0.5
                        else:
                            # 点涡模型
                            factor = 1.0 / r_mag**2

                        # Biot-Savart定律（与原始代码完全一致）
                        # 假设涡轴沿z方向，计算在xy平面的诱导速度
                        v_induced_x = -factor * r_vec[1] / (2 * np.pi * r_mag)
                        v_induced_y = factor * r_vec[0] / (2 * np.pi * r_mag)

                        # 计算法向分量（简化为y方向）
                        A[i, j] = v_induced_y

        return A

    def _build_boundary_conditions_original(self, t: float) -> np.ndarray:
        """
        构建边界条件右端项 - 完全复刻原始算法

        基于原始代码的完整边界条件处理，包括桨叶运动

        Args:
            t: 当前时间 [s]

        Returns:
            rhs: 右端项向量 [N]
        """
        if not hasattr(self, 'control_points') or self.control_points is None:
            n_panels = self.panel_count_chordwise * self.panel_count_spanwise
            return np.zeros(n_panels)

        n_points = len(self.control_points)
        rhs = np.zeros(n_points)

        # 计算每个控制点的法向速度
        for i in range(n_points):
            # 获取控制点位置
            control_point = self.control_points[i]

            # 计算局部法向量（简化为z方向）
            normal = np.array([0.0, 0.0, 1.0])

            # 计算来流速度在法向的分量
            inflow_velocity = np.array([0.0, 0.0, 0.0])  # 简化的来流速度

            # 循环翼转子特殊处理：考虑桨叶运动（与原始代码一致）
            if hasattr(self, 'rotor_type') and self.rotor_type == 'cycloidal':
                # 计算桨叶运动速度
                blade_idx = i // self.panel_count_spanwise
                azimuth = self._calculate_blade_azimuth(blade_idx, t)

                # 桨叶运动速度（简化模型）
                blade_velocity = np.zeros(3)
                if hasattr(self, 'omega_rotor') and hasattr(self, 'R_rotor'):
                    blade_velocity[0] = -self.omega_rotor * self.R_rotor * np.sin(azimuth)
                    blade_velocity[1] = self.omega_rotor * self.R_rotor * np.cos(azimuth)

                # 合成总速度
                inflow_velocity = inflow_velocity + blade_velocity

            # 计算法向速度分量
            normal_velocity = np.dot(inflow_velocity, normal)

            # 边界条件：法向速度为零
            rhs[i] = -normal_velocity

        return rhs

    def _apply_numerical_stabilization_original(self, A: np.ndarray) -> np.ndarray:
        """
        应用数值稳定化技术 - 完全复刻原始算法

        基于原始代码的数值稳定化方法

        Args:
            A: 原始影响系数矩阵

        Returns:
            A_stable: 稳定化后的矩阵
        """
        # 检查矩阵条件数
        try:
            cond_num = np.linalg.cond(A)
            print(f"影响矩阵条件数: {cond_num:.2e}")

            if cond_num > 1e12:
                print(f"警告：影响矩阵条件数过大 ({cond_num:.2e})，应用数值稳定化")

                # Tikhonov正则化（与原始代码一致）
                regularization_param = 1e-6 * np.trace(A) / A.shape[0]
                A_stable = A + regularization_param * np.eye(A.shape[0])

                # 验证稳定化效果
                new_cond_num = np.linalg.cond(A_stable)
                print(f"稳定化后条件数: {new_cond_num:.2e}")

                return A_stable
            else:
                return A

        except np.linalg.LinAlgError:
            print("警告：矩阵奇异，应用对角线正则化")
            A_stable = A + 1e-6 * np.eye(A.shape[0])
            return A_stable

    def _solve_linear_system_original(self, A: np.ndarray, rhs: np.ndarray) -> np.ndarray:
        """
        求解线性系统 - 完全复刻原始算法

        基于原始代码的求解方法，包括GMRES和备用方法

        Args:
            A: 系数矩阵
            rhs: 右端项

        Returns:
            solution: 解向量
        """
        try:
            # 首先尝试直接求解
            solution = np.linalg.solve(A, rhs)
            return solution

        except np.linalg.LinAlgError:
            print("警告：直接求解失败，使用最小二乘法")
            try:
                # 使用最小二乘法
                solution = np.linalg.lstsq(A, rhs, rcond=None)[0]
                return solution

            except Exception as e:
                print(f"最小二乘法也失败: {e}")
                # 返回零解
                return np.zeros(len(rhs))

    def _calculate_blade_azimuth(self, blade_idx: int, t: float) -> float:
        """
        计算桨叶方位角 - 与原始代码一致

        Args:
            blade_idx: 桨叶索引
            t: 时间

        Returns:
            azimuth: 方位角 [rad]
        """
        if hasattr(self, 'omega_rotor'):
            base_azimuth = blade_idx * 2 * np.pi / self.blade_count
            return self.omega_rotor * t + base_azimuth
        else:
            return blade_idx * 2 * np.pi / self.blade_count

    def update_kinematics(self, t: float) -> None:
        """
        更新运动学 - 与原始代码一致

        Args:
            t: 当前时间
        """
        # 更新桨叶位置（简化实现）
        if hasattr(self, 'panel_mesh'):
            for blade_idx, blade_panels in enumerate(self.panel_mesh):
                azimuth = self._calculate_blade_azimuth(blade_idx, t)

                # 更新面板位置（简化：只更新方位角）
                for panel in blade_panels:
                    # 这里可以添加更复杂的运动学更新
                    pass

    # ==================== 自由尾迹演化方法 ====================

    def evolve_free_wake(self, dt: float):
        """
        自由尾迹演化 - 增强版本（基于adevice_complement4.md规范）

        实现完整的自由尾迹演化算法，包括：
        - 预测-修正时间积分
        - Vatistas涡核模型集成
        - 尾迹拉伸、扩散和不稳定性效应
        - 数值稳定化技术

        Args:
            dt: 时间步长 [s]
        """
        if not self.wake_rollup:
            return

        try:
            # 预处理：检查尾迹几何有效性
            self._validate_wake_geometry()

            # 使用预测-修正方法进行尾迹演化
            if self.wake_convection_scheme == "predictor_corrector":
                self._evolve_wake_predictor_corrector_enhanced(dt)
            else:
                self._evolve_wake_euler_enhanced(dt)

            # 应用物理效应（按重要性顺序）
            if self.wake_stretching:
                self._apply_wake_stretching_enhanced(dt)

            if self.wake_diffusion:
                self._apply_wake_diffusion_enhanced(dt)

            # Kelvin-Helmholtz不稳定性（高级功能）
            if self.wake_instability:
                self._apply_kelvin_helmholtz_instability(dt)

            # 数值稳定化处理
            self._apply_numerical_stabilization()

            # 尾迹几何质量控制
            self._apply_wake_geometry_control()

        except Exception as e:
            print(f"   ⚠️ 自由尾迹演化失败: {e}")
            # 回退到简化演化
            self._evolve_wake_euler(dt)

    def _evolve_wake_predictor_corrector_enhanced(self, dt: float):
        """
        增强的预测-修正方法演化尾迹（基于adevice_complement4.md规范）

        实现高精度的预测-修正时间积分，包括：
        - 自适应时间步长控制
        - Vatistas涡核模型集成
        - 数值稳定性检查
        """
        if not hasattr(self, 'wake_vortices') or not self.wake_vortices:
            return

        # 自适应时间步长参数
        max_displacement_ratio = 0.1  # 最大位移与涡核半径比

        for blade_idx, wake_vortices in enumerate(self.wake_vortices):
            for vortex in wake_vortices:
                try:
                    # 获取涡丝几何信息
                    start_pos = vortex['start_position']
                    end_pos = vortex['end_position']
                    mid_pos = (start_pos + end_pos) / 2
                    vortex_length = np.linalg.norm(end_pos - start_pos)

                    # 动态涡核半径（基于年龄和拉伸）
                    age = vortex.get('age', 0.0)
                    core_radius = self._compute_dynamic_core_radius(vortex, age)

                    # 预测步：计算当前位置的诱导速度
                    velocity_predictor = self._compute_total_induced_velocity_at_point_vatistas(
                        mid_pos, core_radius
                    )

                    # 自适应时间步长检查
                    velocity_magnitude = np.linalg.norm(velocity_predictor)
                    if velocity_magnitude > 1e-12:
                        adaptive_dt = min(dt, max_displacement_ratio * core_radius / velocity_magnitude)
                    else:
                        adaptive_dt = dt

                    # 预测新位置
                    predicted_displacement = velocity_predictor * adaptive_dt
                    predicted_start = start_pos + predicted_displacement
                    predicted_end = end_pos + predicted_displacement
                    predicted_mid = (predicted_start + predicted_end) / 2

                    # 修正步：计算预测位置的诱导速度
                    velocity_corrector = self._compute_total_induced_velocity_at_point_vatistas(
                        predicted_mid, core_radius
                    )

                    # Adams-Bashforth-Moulton修正（二阶精度）
                    velocity_final = 0.5 * (velocity_predictor + velocity_corrector)

                    # 数值稳定性检查
                    velocity_change = np.linalg.norm(velocity_corrector - velocity_predictor)
                    if velocity_change > 0.5 * velocity_magnitude:
                        # 速度变化过大，使用更保守的更新
                        velocity_final = 0.8 * velocity_predictor + 0.2 * velocity_corrector

                    # 更新涡丝位置
                    final_displacement = velocity_final * adaptive_dt
                    vortex['start_position'] += final_displacement
                    vortex['end_position'] += final_displacement
                    vortex['age'] += adaptive_dt

                    # 更新涡丝强度（考虑拉伸效应）
                    self._update_vortex_circulation_stretching(vortex, velocity_final, adaptive_dt)

                except Exception as e:
                    print(f"   ⚠️ 涡丝 {blade_idx}-{len(wake_vortices)} 演化失败: {e}")
                    # 回退到简单欧拉方法
                    self._evolve_single_vortex_euler(vortex, dt)

    def _compute_dynamic_core_radius(self, vortex: Dict, age: float) -> float:
        """计算动态涡核半径"""
        base_radius = self.vortex_core_radius

        # 年龄相关的扩散
        age_factor = np.sqrt(1.0 + 0.01 * age)

        # 拉伸相关的修正
        circulation = abs(vortex.get('circulation', 1.0))
        stretch_factor = max(0.5, min(2.0, circulation))

        return base_radius * age_factor * stretch_factor

    def _compute_total_induced_velocity_at_point_vatistas(self, point: np.ndarray,
                                                        core_radius: float) -> np.ndarray:
        """使用Vatistas涡核模型计算点处的总诱导速度"""
        total_velocity = np.zeros(3)

        try:
            # GPU加速计算（如果可用）
            if self.use_gpu and self.gpu_accelerator:
                # 收集所有涡丝位置和强度
                vortex_positions = []
                vortex_strengths = []

                if hasattr(self, 'wake_vortices'):
                    for wake_vortices in self.wake_vortices:
                        for vortex in wake_vortices:
                            start_pos = vortex['start_position']
                            end_pos = vortex['end_position']
                            circulation = vortex.get('circulation', 1.0)

                            vortex_positions.append([start_pos, end_pos])
                            vortex_strengths.append(circulation)

                if vortex_positions:
                    vortex_positions = np.array(vortex_positions)
                    vortex_strengths = np.array(vortex_strengths)
                    target_points = point.reshape(1, -1)

                    # GPU加速的Biot-Savart计算
                    induced_velocities = self.gpu_accelerator.compute_biot_savart_batch(
                        vortex_positions, vortex_strengths, target_points, core_radius
                    )

                    total_velocity = induced_velocities[0]
                else:
                    total_velocity = self._compute_total_induced_velocity_at_point(point)
            else:
                # CPU计算
                total_velocity = self._compute_total_induced_velocity_at_point(point)

        except Exception as e:
            print(f"   ⚠️ Vatistas速度计算失败: {e}")
            # 回退到基础方法
            total_velocity = self._compute_total_induced_velocity_at_point(point)

        return total_velocity

    def _evolve_wake_euler(self, dt: float):
        """使用欧拉方法演化尾迹"""
        if not hasattr(self, 'wake_vortices') or not self.wake_vortices:
            return

        for blade_idx, wake_vortices in enumerate(self.wake_vortices):
            for vortex in wake_vortices:
                # 计算涡丝中点的诱导速度
                mid_point = (vortex['start_position'] + vortex['end_position']) / 2
                induced_velocity = self._compute_total_induced_velocity_at_point(mid_point)

                # 更新涡丝位置
                displacement = induced_velocity * dt
                vortex['start_position'] += displacement
                vortex['end_position'] += displacement
                vortex['age'] += dt

    def _compute_total_induced_velocity_at_point(self, point: np.ndarray) -> np.ndarray:
        """计算点处的总诱导速度"""
        total_velocity = np.zeros(3)

        # 面板贡献
        if hasattr(self, 'panel_mesh') and self.panel_mesh:
            for blade_panels in self.panel_mesh:
                for panel in blade_panels:
                    velocity = self._compute_panel_induced_velocity(
                        panel, point, circulation=1.0
                    )
                    total_velocity += velocity

        # 尾迹贡献
        if hasattr(self, 'wake_vortices'):
            for wake_vortices in self.wake_vortices:
                for vortex in wake_vortices:
                    velocity = self._compute_vortex_induced_velocity(vortex, point)
                    total_velocity += velocity

        return total_velocity

    def _apply_wake_stretching(self, dt: float):
        """应用尾迹拉伸效应"""
        if not hasattr(self, 'wake_vortices'):
            return

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                # 计算涡丝两端的速度梯度
                start_pos = vortex['start_position']
                end_pos = vortex['end_position']

                # 计算速度梯度张量（简化）
                velocity_gradient = self._compute_velocity_gradient(start_pos, end_pos)

                # 应用拉伸效应到环量
                stretching_factor = 1.0 + dt * np.trace(velocity_gradient) / 3.0
                vortex['circulation'] *= max(0.1, stretching_factor)  # 防止过度拉伸

    def _apply_wake_diffusion(self, dt: float):
        """应用尾迹扩散效应"""
        if not hasattr(self, 'wake_vortices'):
            return

        # 涡核扩散
        diffusion_rate = 0.01  # 扩散率

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                # 涡核半径增长
                age = vortex['age']
                core_growth = np.sqrt(1.0 + diffusion_rate * age)

                # 环量衰减（由于扩散）
                decay_factor = 1.0 / core_growth
                vortex['circulation'] *= decay_factor

    def _apply_wake_instability(self, dt: float):
        """应用尾迹不稳定性效应"""
        if not hasattr(self, 'wake_vortices'):
            return

        # 简化的不稳定性模型
        instability_amplitude = 0.01  # 不稳定性幅度

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                age = vortex['age']

                # 随机扰动（模拟不稳定性）
                if age > 0.1:  # 只对较老的尾迹应用
                    perturbation = instability_amplitude * np.random.randn(3)
                    vortex['start_position'] += perturbation
                    vortex['end_position'] += perturbation

    def _compute_velocity_gradient(self, start_pos: np.ndarray, end_pos: np.ndarray) -> np.ndarray:
        """计算速度梯度张量（简化）"""
        # 简化的速度梯度计算
        delta_pos = end_pos - start_pos
        length = np.linalg.norm(delta_pos)

        if length < 1e-12:
            return np.zeros((3, 3))

        # 简化的梯度张量
        gradient = np.outer(delta_pos, delta_pos) / (length**2)

        return gradient

    def _update_vortex_circulation_stretching(self, vortex: Dict, velocity: np.ndarray, dt: float):
        """更新涡丝环量（考虑拉伸效应）"""
        try:
            # 计算涡丝方向
            vortex_vector = vortex['end_position'] - vortex['start_position']
            vortex_length = np.linalg.norm(vortex_vector)

            if vortex_length < 1e-12:
                return

            vortex_direction = vortex_vector / vortex_length

            # 计算拉伸率（速度梯度在涡丝方向的分量）
            velocity_gradient = self._compute_velocity_gradient_enhanced(vortex)
            stretching_rate = np.dot(vortex_direction, np.dot(velocity_gradient, vortex_direction))

            # 更新环量（Kelvin定理）
            circulation_old = vortex.get('circulation', 1.0)
            circulation_new = circulation_old * (1.0 + stretching_rate * dt)

            # 限制环量变化（数值稳定性）
            max_change_ratio = 0.1  # 每步最大10%变化
            change_ratio = abs(circulation_new - circulation_old) / abs(circulation_old)

            if change_ratio > max_change_ratio:
                sign = np.sign(circulation_new - circulation_old)
                circulation_new = circulation_old * (1.0 + sign * max_change_ratio)

            vortex['circulation'] = circulation_new

        except Exception as e:
            print(f"   ⚠️ 环量更新失败: {e}")

    def _compute_velocity_gradient_enhanced(self, vortex: Dict) -> np.ndarray:
        """计算增强的速度梯度张量"""
        try:
            start_pos = vortex['start_position']
            end_pos = vortex['end_position']
            mid_pos = (start_pos + end_pos) / 2

            # 使用有限差分计算梯度
            epsilon = 1e-4  # 差分步长
            gradient = np.zeros((3, 3))

            for i in range(3):
                # 正向扰动
                pos_plus = mid_pos.copy()
                pos_plus[i] += epsilon
                vel_plus = self._compute_total_induced_velocity_at_point(pos_plus)

                # 负向扰动
                pos_minus = mid_pos.copy()
                pos_minus[i] -= epsilon
                vel_minus = self._compute_total_induced_velocity_at_point(pos_minus)

                # 中心差分
                gradient[:, i] = (vel_plus - vel_minus) / (2 * epsilon)

            return gradient

        except Exception as e:
            print(f"   ⚠️ 速度梯度计算失败: {e}")
            return np.zeros((3, 3))

    def _apply_kelvin_helmholtz_instability(self, dt: float):
        """
        应用Kelvin-Helmholtz不稳定性效应（高级功能）

        基于线性稳定性理论的不稳定性模型
        """
        if not hasattr(self, 'wake_vortices'):
            return

        # K-H不稳定性参数
        growth_rate = 0.1  # 增长率
        wavelength = 0.1   # 特征波长

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                age = vortex.get('age', 0.0)
                circulation = abs(vortex.get('circulation', 1.0))

                # 只对足够强和足够老的涡丝应用
                if age > 0.05 and circulation > 0.1:
                    # 计算不稳定性增长
                    growth_factor = growth_rate * circulation * dt

                    # 生成周期性扰动
                    vortex_vector = vortex['end_position'] - vortex['start_position']
                    vortex_length = np.linalg.norm(vortex_vector)

                    if vortex_length > 1e-12:
                        # 垂直于涡丝的扰动
                        tangent = vortex_vector / vortex_length

                        # 生成两个垂直方向
                        if abs(tangent[2]) < 0.9:
                            normal1 = np.cross(tangent, np.array([0, 0, 1]))
                        else:
                            normal1 = np.cross(tangent, np.array([1, 0, 0]))
                        normal1 = normal1 / np.linalg.norm(normal1)
                        normal2 = np.cross(tangent, normal1)

                        # 周期性扰动
                        phase = 2 * np.pi * vortex_length / wavelength
                        amplitude = growth_factor * np.exp(-age * 0.5)  # 随时间衰减

                        perturbation = amplitude * (
                            normal1 * np.sin(phase + age * 10) +
                            normal2 * np.cos(phase + age * 15)
                        )

                        # 应用扰动
                        vortex['start_position'] += perturbation
                        vortex['end_position'] += perturbation

    def _apply_numerical_stabilization(self):
        """应用数值稳定化技术"""
        if not hasattr(self, 'wake_vortices'):
            return

        min_vortex_length = 1e-6
        max_circulation = 100.0
        min_circulation = 1e-6

        for wake_vortices in self.wake_vortices:
            vortices_to_remove = []

            for i, vortex in enumerate(wake_vortices):
                # 检查涡丝长度
                vortex_vector = vortex['end_position'] - vortex['start_position']
                vortex_length = np.linalg.norm(vortex_vector)

                # 检查环量
                circulation = abs(vortex.get('circulation', 1.0))

                # 标记需要移除的涡丝
                if (vortex_length < min_vortex_length or
                    circulation < min_circulation or
                    circulation > max_circulation or
                    vortex.get('age', 0.0) > 10.0):  # 年龄限制
                    vortices_to_remove.append(i)

            # 移除无效涡丝（从后往前移除）
            for i in reversed(vortices_to_remove):
                wake_vortices.pop(i)

    def _apply_wake_geometry_control(self):
        """应用尾迹几何质量控制"""
        if not hasattr(self, 'wake_vortices'):
            return

        max_aspect_ratio = 100.0  # 最大长宽比

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                vortex_vector = vortex['end_position'] - vortex['start_position']
                vortex_length = np.linalg.norm(vortex_vector)

                # 估计涡丝"宽度"（基于涡核半径）
                core_radius = self._compute_dynamic_core_radius(vortex, vortex.get('age', 0.0))
                aspect_ratio = vortex_length / (2 * core_radius)

                # 如果长宽比过大，分割涡丝
                if aspect_ratio > max_aspect_ratio and vortex_length > 2 * core_radius:
                    self._split_vortex(vortex, wake_vortices)

    def _split_vortex(self, vortex: Dict, wake_vortices: List):
        """分割过长的涡丝"""
        try:
            start_pos = vortex['start_position']
            end_pos = vortex['end_position']
            mid_pos = (start_pos + end_pos) / 2

            # 创建两个新涡丝
            vortex1 = vortex.copy()
            vortex1['end_position'] = mid_pos
            vortex1['circulation'] *= 0.5

            vortex2 = vortex.copy()
            vortex2['start_position'] = mid_pos
            vortex2['circulation'] *= 0.5

            # 替换原涡丝
            idx = wake_vortices.index(vortex)
            wake_vortices[idx] = vortex1
            wake_vortices.insert(idx + 1, vortex2)

        except Exception as e:
            print(f"   ⚠️ 涡丝分割失败: {e}")

    def _validate_wake_geometry(self):
        """验证尾迹几何有效性"""
        if not hasattr(self, 'wake_vortices'):
            return

        total_vortices = 0
        invalid_vortices = 0

        for wake_vortices in self.wake_vortices:
            for vortex in wake_vortices:
                total_vortices += 1

                # 检查位置有效性
                start_pos = vortex['start_position']
                end_pos = vortex['end_position']

                if (np.any(np.isnan(start_pos)) or np.any(np.isnan(end_pos)) or
                    np.any(np.isinf(start_pos)) or np.any(np.isinf(end_pos))):
                    invalid_vortices += 1

        if invalid_vortices > 0:
            print(f"   ⚠️ 发现 {invalid_vortices}/{total_vortices} 个无效涡丝")

    def _evolve_single_vortex_euler(self, vortex: Dict, dt: float):
        """单个涡丝的简化欧拉演化（回退方法）"""
        try:
            mid_point = (vortex['start_position'] + vortex['end_position']) / 2
            induced_velocity = self._compute_total_induced_velocity_at_point(mid_point)

            displacement = induced_velocity * dt
            vortex['start_position'] += displacement
            vortex['end_position'] += displacement
            vortex['age'] += dt

        except Exception as e:
            print(f"   ⚠️ 简化涡丝演化失败: {e}")

    def _evolve_wake_euler_enhanced(self, dt: float):
        """增强的欧拉方法演化尾迹"""
        if not hasattr(self, 'wake_vortices') or not self.wake_vortices:
            return

        for blade_idx, wake_vortices in enumerate(self.wake_vortices):
            for vortex in wake_vortices:
                try:
                    # 使用增强的速度计算
                    mid_point = (vortex['start_position'] + vortex['end_position']) / 2
                    age = vortex.get('age', 0.0)
                    core_radius = self._compute_dynamic_core_radius(vortex, age)

                    induced_velocity = self._compute_total_induced_velocity_at_point_vatistas(
                        mid_point, core_radius
                    )

                    # 更新位置
                    displacement = induced_velocity * dt
                    vortex['start_position'] += displacement
                    vortex['end_position'] += displacement
                    vortex['age'] += dt

                except Exception as e:
                    print(f"   ⚠️ 增强欧拉演化失败: {e}")
                    self._evolve_single_vortex_euler(vortex, dt)
