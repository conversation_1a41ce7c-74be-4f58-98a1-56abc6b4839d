# 循环翼转子仿真套件重构版文档
## Cycloidal Rotor Simulation Suite Refactored Documentation

**版本**: v2.0  
**最后更新**: 2025-01-08  
**状态**: ✅ 重构完成

---

## 📚 **文档导航**

### **🚀 快速开始**
- [项目概述](README.md) - 本文档
- [安装指南](user_guide/installation_guide.md) - 详细安装步骤
- [快速入门教程](user_guide/quick_start.md) - 30分钟入门指南

### **👥 用户指南**
- [`user_guide/`](user_guide/) - 用户使用指南
  - 安装和配置
  - 基本使用教程
  - 高级功能指南
  - 故障排除

### **🔬 方法论和理论**
- [`methodology/`](methodology/) - 理论基础和方法论
  - [核心方法论](methodology/core_methodology.md) - 完整理论框架
  - [BEMT理论](methodology/aerodynamics/bemt_theory.md) - 叶素动量理论
  - [UVLM理论](methodology/aerodynamics/uvlm_theory.md) - 非定常涡格法
  - [FW-H声学理论](methodology/acoustics/fwh_theory.md) - 声学类比方程
  - [循环翼运动学](methodology/geometry/cycloidal_kinematics.md) - 运动学理论
  - [验证方法论](methodology/validation_methodology.md) - V&V框架

### **🛠️ 开发文档**
- [`development/`](development/) - 开发者文档
  - [实施进度报告](development/implementation_progress_report.md)
  - [实施路线图](development/implementation_roadmap.md)
  - [重构文档](development/refactored.md)
  - 架构设计
  - 贡献指南

### **📖 API参考**
- [`api/`](api/) - API文档
  - [核心API](api/core_api.md) - 主要编程接口
  - 求解器接口
  - 后处理API
  - 验证框架API

### **✅ 验证和测试**
- [`validation/`](validation/) - 验证相关文档
  - 验证案例
  - 基准测试
  - 精度分析
- [`testing/`](testing/) - 测试文档
  - [测试验证报告](testing/testing_validation_report.md)
  - 测试框架
  - 测试用例

### **📊 报告和分析**
- [`reports/`](reports/) - 技术报告和分析
  - [功能比较分析](reports/comprehensive_functionality_comparison.md)
  - [最终分析总结](reports/final_comparison_analysis_summary.md)
  - 性能分析报告
  - 深度重构报告

---

## 🏗️ **项目架构概览**

### **核心模块结构**
```
cycloidal_rotor_suite_refactored/
├── core/                    # 核心计算模块
│   ├── aerodynamics/        # 空气动力学求解器
│   ├── acoustics/           # 声学求解器
│   ├── geometry/            # 几何和运动学
│   ├── physics/             # 物理模型
│   └── postprocessing/      # 后处理系统
├── scripts/                 # 脚本和工具
│   ├── cli/                 # 命令行界面
│   ├── validation/          # 验证框架
│   └── test/                # 测试套件
├── config/                  # 配置文件
├── examples/                # 示例代码
└── docs/                    # 文档（本目录）
```

### **主要功能模块**
- **BEMT求解器**: 叶素动量理论求解器，支持动态失速
- **UVLM求解器**: 非定常涡格法求解器，支持自由尾迹
- **FW-H声学求解器**: Ffowcs Williams-Hawkings声学求解器
- **验证框架**: 完整的验证和基准测试系统
- **后处理系统**: 数据分析、可视化和报告生成

---

## 📋 **文档类型说明**

### **按文档类型分类**

#### **📘 教程类 (Tutorials)**
- 面向初学者的逐步指导
- 实际操作示例
- 常见用例演示

#### **📗 操作指南 (How-to Guides)**
- 解决特定问题的指南
- 最佳实践建议
- 配置和优化技巧

#### **📙 解释性文档 (Explanation)**
- 理论背景和概念解释
- 设计决策说明
- 深度技术分析

#### **📕 参考文档 (Reference)**
- API文档
- 配置参数说明
- 技术规范

---

## 🔍 **快速查找**

### **常用文档快速链接**
- **新用户**: 从 [`user_guide/quick_start.md`](user_guide/quick_start.md) 开始
- **开发者**: 查看 [`development/`](development/) 目录
- **研究人员**: 参考 [`methodology/`](methodology/) 和 [`validation/`](validation/)
- **API使用**: 查看 [`api/`](api/) 目录
- **问题排查**: 查看 [`user_guide/troubleshooting.md`](user_guide/troubleshooting.md)

### **按功能模块查找**
- **空气动力学**: `methodology/aerodynamics/` + `api/aerodynamics/`
- **声学分析**: `methodology/acoustics/` + `api/acoustics/`
- **几何建模**: `methodology/geometry/` + `api/geometry/`
- **后处理**: `user_guide/postprocessing/` + `api/postprocessing/`
- **验证测试**: `validation/` + `testing/`

---

## 📈 **文档状态**

### **完成状态**
- ✅ **核心文档**: 100% 完成
- ✅ **API文档**: 90% 完成
- ✅ **用户指南**: 85% 完成
- ✅ **开发文档**: 95% 完成
- ✅ **验证文档**: 90% 完成

### **最近更新**
- **2025-01-08**: 完成项目重构，更新所有文档结构
- **2025-01-08**: 添加深度验证测试文档
- **2025-01-08**: 重组文档目录结构

---

## 🤝 **贡献指南**

### **文档贡献**
- 遵循现有的文档结构和格式
- 使用清晰的标题和章节组织
- 包含代码示例和实际用例
- 保持中英文对照的专业术语

### **文档标准**
- **格式**: Markdown (.md)
- **编码**: UTF-8
- **图片**: PNG/SVG格式，存放在对应目录的`images/`子目录
- **代码**: 使用语法高亮的代码块

---

## 📞 **获取帮助**

### **文档问题**
- 查看 [`user_guide/troubleshooting.md`](user_guide/troubleshooting.md)
- 搜索现有的问题和解决方案
- 查看相关的技术报告

### **技术支持**
- 查看 [`validation/`](validation/) 目录中的验证案例
- 参考 [`examples/`](../examples/) 目录中的示例代码
- 查看 [`reports/`](reports/) 目录中的技术分析

---

**循环翼转子仿真套件重构版** - 高精度、模块化的循环翼转子仿真平台  
**Cycloidal Rotor Simulation Suite Refactored** - High-fidelity, modular simulation platform for cycloidal rotors
