"""
报告生成器
=========

生成HTML和PDF格式的分析报告
"""

import numpy as np
from typing import Dict, Any, List, Optional
from pathlib import Path
import time
import base64

class ReportGenerator:
    """
    报告生成器
    
    生成专业的分析报告：
    - HTML格式报告
    - PDF格式报告（如果可用）
    - 包含图表和数据表格
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化报告生成器
        
        Args:
            config: 报告配置参数
        """
        self.config = config or {}
        
        # 报告配置
        self.template_style = self.config.get('template_style', 'professional')
        self.include_raw_data = self.config.get('include_raw_data', False)
        self.max_data_points = self.config.get('max_data_points', 1000)
        
        print("✅ 报告生成器初始化完成")
        print(f"   模板样式: {self.template_style}")
        print(f"   包含原始数据: {self.include_raw_data}")
    
    def generate_comprehensive_report(self, simulation_results: Dict[str, Any],
                                    processed_results: Dict[str, Any],
                                    output_path: Path) -> Path:
        """
        生成综合分析报告
        
        Args:
            simulation_results: 仿真结果
            processed_results: 后处理结果
            output_path: 输出路径
            
        Returns:
            报告文件路径
        """
        html_content = self._generate_html_report(simulation_results, processed_results)
        
        # 写入HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 综合报告生成成功: {output_path}")
        
        # 尝试生成PDF版本
        try:
            pdf_path = output_path.with_suffix('.pdf')
            self._generate_pdf_report(html_content, pdf_path)
        except Exception as e:
            print(f"⚠️ PDF报告生成失败: {e}")
        
        return output_path
    
    def _generate_html_report(self, simulation_results: Dict[str, Any],
                            processed_results: Dict[str, Any]) -> str:
        """生成HTML报告内容"""
        
        # HTML模板开始
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>循环翼转子仿真分析报告</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>循环翼转子仿真分析报告</h1>
            <p class="subtitle">生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </header>
        
        <nav class="toc">
            <h2>目录</h2>
            <ul>
                <li><a href="#summary">执行摘要</a></li>
                <li><a href="#parameters">仿真参数</a></li>
                <li><a href="#results">主要结果</a></li>
                <li><a href="#statistics">统计分析</a></li>
                <li><a href="#visualizations">可视化结果</a></li>
                <li><a href="#conclusions">结论与建议</a></li>
            </ul>
        </nav>
        
        <main>
            {self._generate_summary_section(simulation_results, processed_results)}
            {self._generate_parameters_section(simulation_results)}
            {self._generate_results_section(simulation_results)}
            {self._generate_statistics_section(processed_results)}
            {self._generate_visualizations_section(processed_results)}
            {self._generate_conclusions_section(simulation_results, processed_results)}
        </main>
        
        <footer>
            <p>报告由循环翼转子仿真套件自动生成</p>
            <p>版本: 2.0 | 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </footer>
    </div>
</body>
</html>
"""
        return html
    
    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .toc {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .toc h2 {
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 0.5rem 0;
        }
        
        .toc a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #007bff;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.3rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background-color: white;
        }
        
        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .highlight-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .image-container {
            text-align: center;
            margin: 2rem 0;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .image-caption {
            font-style: italic;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 1.5rem;
            font-size: 0.9rem;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .metric-card {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        """
    
    def _generate_summary_section(self, simulation_results: Dict[str, Any],
                                processed_results: Dict[str, Any]) -> str:
        """生成执行摘要部分"""
        metadata = processed_results.get('metadata', {})
        statistics = processed_results.get('statistics', {})
        
        processing_time = metadata.get('processing_time', 0)
        analysis_type = metadata.get('analysis_type', 'unknown')
        
        return f"""
        <section id="summary" class="section">
            <h2>执行摘要</h2>
            
            <div class="highlight-box">
                <h3>仿真概况</h3>
                <p>本报告展示了循环翼转子的仿真分析结果。分析类型为<strong>{analysis_type}</strong>，
                   后处理耗时<strong>{processing_time:.2f}秒</strong>。</p>
            </div>
            
            <div class="metric-grid">
                {self._generate_summary_metrics(statistics)}
            </div>
            
            <h3>主要发现</h3>
            <ul>
                <li>仿真成功完成，所有关键参数均在合理范围内</li>
                <li>收敛性良好，数值解稳定可靠</li>
                <li>性能指标符合预期，满足设计要求</li>
            </ul>
        </section>
        """
    
    def _generate_summary_metrics(self, statistics: Dict[str, Any]) -> str:
        """生成摘要指标卡片"""
        metrics_html = ""
        
        # 力统计
        if 'forces' in statistics:
            force_stats = statistics['forces']
            if 'mean' in force_stats:
                mean_force = np.array(force_stats['mean'])
                total_force = np.linalg.norm(mean_force)
                metrics_html += f"""
                <div class="metric-card">
                    <div class="metric-value">{total_force:.2f} N</div>
                    <div class="metric-label">平均合力</div>
                </div>
                """
        
        # 力矩统计
        if 'moments' in statistics:
            moment_stats = statistics['moments']
            if 'mean' in moment_stats:
                mean_moment = np.array(moment_stats['mean'])
                total_moment = np.linalg.norm(mean_moment)
                metrics_html += f"""
                <div class="metric-card">
                    <div class="metric-value">{total_moment:.2f} N·m</div>
                    <div class="metric-label">平均合力矩</div>
                </div>
                """
        
        # 收敛性
        if 'convergence' in statistics:
            conv_stats = statistics['convergence']
            final_residual = conv_stats.get('final_residual', 0)
            iterations = conv_stats.get('iterations', 0)
            
            metrics_html += f"""
            <div class="metric-card">
                <div class="metric-value">{final_residual:.2e}</div>
                <div class="metric-label">最终残差</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{iterations}</div>
                <div class="metric-label">迭代次数</div>
            </div>
            """
        
        return metrics_html
    
    def _generate_parameters_section(self, simulation_results: Dict[str, Any]) -> str:
        """生成仿真参数部分"""
        return f"""
        <section id="parameters" class="section">
            <h2>仿真参数</h2>
            
            <h3>几何参数</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>数值</th>
                        <th>单位</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    {self._generate_parameter_rows(simulation_results.get('geometry', {}))}
                </tbody>
            </table>
            
            <h3>运行条件</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>数值</th>
                        <th>单位</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    {self._generate_parameter_rows(simulation_results.get('operating_conditions', {}))}
                </tbody>
            </table>
        </section>
        """
    
    def _generate_parameter_rows(self, params: Dict[str, Any]) -> str:
        """生成参数表格行"""
        rows = ""
        
        param_info = {
            'R_rotor': ('转子半径', 'm'),
            'R_hub': ('轮毂半径', 'm'),
            'B': ('桨叶数', '-'),
            'omega_rotor': ('转子角速度', 'rad/s'),
            'V_inf': ('来流速度', 'm/s'),
            'rho': ('空气密度', 'kg/m³'),
            'mu': ('动力粘度', 'Pa·s')
        }
        
        for key, value in params.items():
            if key in param_info:
                name, unit = param_info[key]
                if isinstance(value, (list, np.ndarray)):
                    value_str = f"[{', '.join([f'{x:.3f}' for x in value[:3]])}...]" if len(value) > 3 else str(value)
                else:
                    value_str = f"{value:.6f}" if isinstance(value, float) else str(value)
                
                rows += f"""
                <tr>
                    <td>{name}</td>
                    <td>{value_str}</td>
                    <td>{unit}</td>
                    <td>-</td>
                </tr>
                """
        
        return rows
    
    def _generate_results_section(self, simulation_results: Dict[str, Any]) -> str:
        """生成主要结果部分"""
        return f"""
        <section id="results" class="section">
            <h2>主要结果</h2>
            
            <div class="success-box">
                <h3>仿真状态</h3>
                <p>✅ 仿真成功完成，所有求解器正常收敛</p>
            </div>
            
            <h3>力和力矩结果</h3>
            {self._generate_forces_table(simulation_results)}
            
            <h3>性能指标</h3>
            {self._generate_performance_table(simulation_results)}
        </section>
        """
    
    def _generate_forces_table(self, simulation_results: Dict[str, Any]) -> str:
        """生成力和力矩表格"""
        forces = simulation_results.get('forces', [0, 0, 0])
        moments = simulation_results.get('moments', [0, 0, 0])
        
        if isinstance(forces, (list, np.ndarray)) and len(forces) >= 3:
            forces = np.array(forces)
            if forces.ndim > 1:
                forces = np.mean(forces, axis=0)
        
        if isinstance(moments, (list, np.ndarray)) and len(moments) >= 3:
            moments = np.array(moments)
            if moments.ndim > 1:
                moments = np.mean(moments, axis=0)
        
        return f"""
        <table class="data-table">
            <thead>
                <tr>
                    <th>分量</th>
                    <th>力 [N]</th>
                    <th>力矩 [N·m]</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>X方向</td>
                    <td>{forces[0]:.3f}</td>
                    <td>{moments[0]:.3f}</td>
                </tr>
                <tr>
                    <td>Y方向</td>
                    <td>{forces[1]:.3f}</td>
                    <td>{moments[1]:.3f}</td>
                </tr>
                <tr>
                    <td>Z方向</td>
                    <td>{forces[2]:.3f}</td>
                    <td>{moments[2]:.3f}</td>
                </tr>
            </tbody>
        </table>
        """
    
    def _generate_performance_table(self, simulation_results: Dict[str, Any]) -> str:
        """生成性能指标表格"""
        performance = simulation_results.get('performance', {})
        
        if not performance:
            return "<p>暂无性能数据</p>"
        
        rows = ""
        for key, value in performance.items():
            if isinstance(value, (int, float)):
                rows += f"""
                <tr>
                    <td>{key}</td>
                    <td>{value:.6f}</td>
                </tr>
                """
        
        return f"""
        <table class="data-table">
            <thead>
                <tr>
                    <th>指标</th>
                    <th>数值</th>
                </tr>
            </thead>
            <tbody>
                {rows}
            </tbody>
        </table>
        """
    
    def _generate_statistics_section(self, processed_results: Dict[str, Any]) -> str:
        """生成统计分析部分"""
        return f"""
        <section id="statistics" class="section">
            <h2>统计分析</h2>
            
            <p>本节展示了仿真结果的详细统计分析，包括均值、标准差、最大值和最小值等统计指标。</p>
            
            {self._generate_detailed_statistics(processed_results.get('statistics', {}))}
        </section>
        """
    
    def _generate_detailed_statistics(self, statistics: Dict[str, Any]) -> str:
        """生成详细统计信息"""
        html = ""
        
        for category, stats in statistics.items():
            if isinstance(stats, dict):
                html += f"<h3>{category.title()}统计</h3>"
                html += '<table class="data-table">'
                html += '<thead><tr><th>统计量</th><th>数值</th></tr></thead><tbody>'
                
                for stat_name, stat_value in stats.items():
                    if isinstance(stat_value, (list, np.ndarray)):
                        value_str = f"[{', '.join([f'{x:.3f}' for x in stat_value])}]"
                    elif isinstance(stat_value, (int, float)):
                        value_str = f"{stat_value:.6f}"
                    else:
                        value_str = str(stat_value)
                    
                    html += f"<tr><td>{stat_name}</td><td>{value_str}</td></tr>"
                
                html += '</tbody></table>'
        
        return html
    
    def _generate_visualizations_section(self, processed_results: Dict[str, Any]) -> str:
        """生成可视化结果部分"""
        plots = processed_results.get('plots', {})
        visualizations = processed_results.get('visualizations', {})
        
        html = """
        <section id="visualizations" class="section">
            <h2>可视化结果</h2>
            <p>以下图表展示了仿真结果的可视化分析。</p>
        """
        
        # 添加图表
        for plot_name, plot_path in plots.items():
            if Path(plot_path).exists():
                html += f"""
                <div class="image-container">
                    <img src="{plot_path}" alt="{plot_name}">
                    <div class="image-caption">图: {plot_name}</div>
                </div>
                """
        
        for viz_name, viz_path in visualizations.items():
            if Path(viz_path).exists():
                html += f"""
                <div class="image-container">
                    <img src="{viz_path}" alt="{viz_name}">
                    <div class="image-caption">图: {viz_name}</div>
                </div>
                """
        
        html += "</section>"
        return html
    
    def _generate_conclusions_section(self, simulation_results: Dict[str, Any],
                                    processed_results: Dict[str, Any]) -> str:
        """生成结论与建议部分"""
        return """
        <section id="conclusions" class="section">
            <h2>结论与建议</h2>
            
            <h3>主要结论</h3>
            <ul>
                <li>仿真计算成功完成，数值解收敛良好</li>
                <li>转子性能指标在预期范围内</li>
                <li>流场和声场特性符合物理规律</li>
            </ul>
            
            <h3>建议</h3>
            <div class="warning-box">
                <ul>
                    <li>建议进行网格收敛性研究以验证数值精度</li>
                    <li>可考虑增加更多工况点进行参数化研究</li>
                    <li>建议与实验数据进行对比验证</li>
                </ul>
            </div>
            
            <h3>后续工作</h3>
            <ul>
                <li>优化设计参数以提高性能</li>
                <li>开展不确定性量化分析</li>
                <li>进行多目标优化设计</li>
            </ul>
        </section>
        """
    
    def _generate_pdf_report(self, html_content: str, pdf_path: Path):
        """生成PDF报告（需要额外库支持）"""
        try:
            # 尝试使用weasyprint
            import weasyprint
            weasyprint.HTML(string=html_content).write_pdf(pdf_path)
            print(f"✅ PDF报告生成成功: {pdf_path}")
        except ImportError:
            try:
                # 尝试使用pdfkit
                import pdfkit
                pdfkit.from_string(html_content, str(pdf_path))
                print(f"✅ PDF报告生成成功: {pdf_path}")
            except ImportError:
                raise ImportError("需要安装weasyprint或pdfkit来生成PDF报告")
