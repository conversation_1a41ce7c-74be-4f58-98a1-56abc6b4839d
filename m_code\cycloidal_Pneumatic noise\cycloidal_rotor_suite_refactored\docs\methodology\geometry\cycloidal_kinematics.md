# 循环翼转子运动学理论与几何建模
## Cycloidal Rotor Kinematics Theory and Geometric Modeling

**文档版本**: v2.0  
**创建时间**: 2025-01-08  
**理论基础**: 刚体运动学与微分几何  
**学术标准**: 期刊发表质量  

---

## 摘要 (Abstract)

本文建立了循环翼转子运动学的完整数学框架，从刚体运动学基本原理出发，详细推导了循环翼转子桨叶的复合运动描述、速度场计算和几何变换关系。通过严格的数学推导，为循环翼转子的几何建模和运动学分析提供了理论基础。

**关键词**: 循环翼转子, 运动学, 几何建模, 刚体运动, 微分几何

---

## 1. 理论基础 (Theoretical Foundation)

### 1.1 刚体运动学基本原理

**位置矢量**：
$$\vec{r}(t) = \vec{r}_0 + \vec{R}(t) \cdot \vec{r}_{body}$$

其中：
- $\vec{r}_0$：参考点位置
- $\vec{R}(t)$：旋转矩阵
- $\vec{r}_{body}$：物体坐标系中的位置

**速度关系**：
$$\vec{v}(t) = \vec{v}_0 + \vec{\omega} \times (\vec{r} - \vec{r}_0)$$

其中：
- $\vec{v}_0$：参考点速度
- $\vec{\omega}$：角速度矢量

**加速度关系**：
$$\vec{a}(t) = \vec{a}_0 + \vec{\alpha} \times (\vec{r} - \vec{r}_0) + \vec{\omega} \times [\vec{\omega} \times (\vec{r} - \vec{r}_0)]$$

其中$\vec{\alpha} = \frac{d\vec{\omega}}{dt}$为角加速度。

### 1.2 坐标系定义

**惯性坐标系** $(X, Y, Z)$：
- 原点：固定在空间中
- $X$轴：指向前进方向
- $Y$轴：指向右侧
- $Z$轴：指向上方（右手坐标系）

**转子坐标系** $(x_r, y_r, z_r)$：
- 原点：转子中心
- $x_r$轴：指向前进方向
- $y_r$轴：指向右侧
- $z_r$轴：指向上方

**桨叶坐标系** $(x_b, y_b, z_b)$：
- 原点：桨叶根部
- $x_b$轴：沿弦线方向（前缘指向后缘）
- $y_b$轴：沿展向方向
- $z_b$轴：垂直于桨叶平面

---

## 2. 循环翼转子几何描述 (Cycloidal Rotor Geometry)

### 2.1 基本几何参数

**转子几何参数**：
- $R$：转子半径
- $R_h$：轮毂半径
- $N_b$：桨叶数
- $c$：桨叶弦长
- $s$：桨叶展长

**桨叶几何参数**：
- $\theta_0$：集体俯仰角
- $\theta_1$：周期俯仰幅值
- $\phi$：俯仰相位角
- $\beta$：桨叶安装角

### 2.2 桨叶位置描述

**第$i$个桨叶的方位角**：
$$\psi_i(t) = \Omega t + \frac{2\pi(i-1)}{N_b}$$

其中$\Omega$为转子角速度。

**桨叶根部位置**：
$$\vec{r}_{root,i}(t) = R[\cos\psi_i(t) \hat{x}_r + \sin\psi_i(t) \hat{y}_r]$$

**桨叶俯仰角**：
$$\theta_i(t) = \theta_0 + \theta_1 \sin(\psi_i(t) + \phi) + \beta$$

### 2.3 桨叶表面参数化

**弦向参数化**：
$$\xi \in [0, 1], \quad \xi = 0 \text{(前缘)}, \quad \xi = 1 \text{(后缘)}$$

**展向参数化**：
$$\eta \in [0, 1], \quad \eta = 0 \text{(根部)}, \quad \eta = 1 \text{(桨尖)}$$

**桨叶表面位置**：
$$\vec{r}_{blade}(\xi, \eta, t) = \vec{r}_{root}(t) + \eta s \hat{e}_{span}(t) + f(\xi) \hat{e}_{chord}(t)$$

其中：
- $f(\xi)$：翼型函数
- $\hat{e}_{span}(t)$：展向单位矢量
- $\hat{e}_{chord}(t)$：弦向单位矢量

---

## 3. 运动学分析 (Kinematic Analysis)

### 3.1 复合运动分解

循环翼转子桨叶的运动可分解为：

**1. 平移运动**：
$$\vec{V}_{trans} = \vec{V}_{\infty}$$

**2. 旋转运动**：
$$\vec{V}_{rot} = \vec{\Omega} \times \vec{r}$$

**3. 俯仰运动**：
$$\vec{V}_{pitch} = \frac{\partial \vec{r}}{\partial \theta} \frac{d\theta}{dt}$$

**总速度**：
$$\vec{V}_{total} = \vec{V}_{trans} + \vec{V}_{rot} + \vec{V}_{pitch}$$

### 3.2 角速度分析

**转子角速度**：
$$\vec{\Omega}_{rotor} = \Omega \hat{z}_r$$

**桨叶俯仰角速度**：
$$\dot{\theta}_i = \theta_1 \Omega \cos(\psi_i + \phi)$$

**桨叶总角速度**：
$$\vec{\omega}_{blade} = \vec{\Omega}_{rotor} + \dot{\theta}_i \hat{e}_{pitch}$$

其中$\hat{e}_{pitch}$为俯仰轴方向。

### 3.3 速度场计算

**桨叶上任意点的速度**：
$$\vec{V}(\xi, \eta, t) = \vec{V}_{\infty} + \vec{\Omega} \times \vec{r}(\xi, \eta, t) + \vec{V}_{pitch}(\xi, \eta, t)$$

**俯仰运动速度**：
$$\vec{V}_{pitch}(\xi, \eta, t) = \dot{\theta}_i \times [\vec{r}(\xi, \eta, t) - \vec{r}_{pitch}(t)]$$

其中$\vec{r}_{pitch}(t)$为俯仰轴位置。

---

## 4. 坐标变换 (Coordinate Transformations)

### 4.1 旋转矩阵

**绕$z$轴旋转（方位角）**：
$$\mathbf{R}_z(\psi) = \begin{bmatrix}
\cos\psi & -\sin\psi & 0 \\
\sin\psi & \cos\psi & 0 \\
0 & 0 & 1
\end{bmatrix}$$

**绕$y$轴旋转（俯仰角）**：
$$\mathbf{R}_y(\theta) = \begin{bmatrix}
\cos\theta & 0 & \sin\theta \\
0 & 1 & 0 \\
-\sin\theta & 0 & \cos\theta
\end{bmatrix}$$

**复合旋转**：
$$\mathbf{R}_{total} = \mathbf{R}_z(\psi) \mathbf{R}_y(\theta)$$

### 4.2 坐标变换关系

**从桨叶坐标系到转子坐标系**：
$$\vec{r}_{rotor} = \vec{r}_{root} + \mathbf{R}_{total} \vec{r}_{blade}$$

**从转子坐标系到惯性坐标系**：
$$\vec{r}_{inertial} = \vec{r}_{rotor\_center} + \mathbf{R}_{rotor} \vec{r}_{rotor}$$

### 4.3 速度变换

**角速度变换**：
$$\vec{\omega}_{inertial} = \mathbf{R}_{rotor} \vec{\omega}_{rotor}$$

**线速度变换**：
$$\vec{V}_{inertial} = \vec{V}_{rotor\_center} + \mathbf{R}_{rotor} \vec{V}_{rotor}$$

---

## 5. 微分几何分析 (Differential Geometry)

### 5.1 曲面参数化

**参数曲面**：
$$\vec{r}(u, v, t) = \vec{r}(u, v, t)$$

其中$(u, v)$为曲面参数。

**切矢量**：
$$\vec{r}_u = \frac{\partial \vec{r}}{\partial u}, \quad \vec{r}_v = \frac{\partial \vec{r}}{\partial v}$$

**法矢量**：
$$\hat{n} = \frac{\vec{r}_u \times \vec{r}_v}{|\vec{r}_u \times \vec{r}_v|}$$

### 5.2 第一基本形式

**度量张量**：
$$ds^2 = E du^2 + 2F du dv + G dv^2$$

其中：
$$E = \vec{r}_u \cdot \vec{r}_u, \quad F = \vec{r}_u \cdot \vec{r}_v, \quad G = \vec{r}_v \cdot \vec{r}_v$$

**面积元素**：
$$dS = \sqrt{EG - F^2} du dv$$

### 5.3 第二基本形式

**曲率张量**：
$$\kappa = L du^2 + 2M du dv + N dv^2$$

其中：
$$L = \vec{r}_{uu} \cdot \hat{n}, \quad M = \vec{r}_{uv} \cdot \hat{n}, \quad N = \vec{r}_{vv} \cdot \hat{n}$$

**主曲率**：
$$\kappa_1, \kappa_2 = \frac{1}{2}\left[\frac{LG - 2MF + NE}{EG - F^2} \pm \sqrt{\Delta}\right]$$

---

## 6. 数值实现方法 (Numerical Implementation)

### 6.1 时间离散化

**时间步进**：
$$t^{n+1} = t^n + \Delta t$$

**位置更新**：
$$\vec{r}^{n+1} = \vec{r}^n + \Delta t \cdot \vec{V}^n + \frac{(\Delta t)^2}{2} \vec{a}^n$$

**速度更新**：
$$\vec{V}^{n+1} = \vec{V}^n + \Delta t \cdot \vec{a}^n$$

### 6.2 插值方法

**Lagrange插值**：
$$f(t) = \sum_{i=0}^{n} f_i L_i(t)$$

其中：
$$L_i(t) = \prod_{j=0, j \neq i}^{n} \frac{t - t_j}{t_i - t_j}$$

**样条插值**：
$$S(t) = a_i + b_i(t - t_i) + c_i(t - t_i)^2 + d_i(t - t_i)^3$$

### 6.3 数值微分

**中心差分**：
$$\frac{df}{dt}\bigg|_i = \frac{f_{i+1} - f_{i-1}}{2\Delta t}$$

**向前差分**：
$$\frac{df}{dt}\bigg|_i = \frac{f_{i+1} - f_i}{\Delta t}$$

**向后差分**：
$$\frac{df}{dt}\bigg|_i = \frac{f_i - f_{i-1}}{\Delta t}$$

---

## 7. 特殊运动模式 (Special Motion Patterns)

### 7.1 正弦俯仰运动

**俯仰角函数**：
$$\theta(t) = \theta_0 + \theta_1 \sin(\Omega t + \phi)$$

**俯仰角速度**：
$$\dot{\theta}(t) = \theta_1 \Omega \cos(\Omega t + \phi)$$

**俯仰角加速度**：
$$\ddot{\theta}(t) = -\theta_1 \Omega^2 \sin(\Omega t + \phi)$$

### 7.2 复合俯仰运动

**多谐波俯仰**：
$$\theta(t) = \theta_0 + \sum_{n=1}^{N} \theta_n \sin(n\Omega t + \phi_n)$$

**Fourier级数展开**：
$$\theta(t) = \frac{a_0}{2} + \sum_{n=1}^{\infty} [a_n \cos(n\Omega t) + b_n \sin(n\Omega t)]$$

### 7.3 非周期运动

**任意时间函数**：
$$\theta(t) = f(t)$$

**数值求导**：
$$\dot{\theta}(t) \approx \frac{d}{dt}f(t)$$

---

## 8. 验证与确认 (Verification & Validation)

### 8.1 运动学约束验证

**刚体约束**：
$$|\vec{r}_{ij}| = const$$

其中$\vec{r}_{ij}$为刚体内两点间距离。

**角速度一致性**：
$$\vec{\omega} = \frac{\vec{r}_{ij} \times \vec{V}_{ij}}{|\vec{r}_{ij}|^2}$$

### 8.2 能量守恒验证

**动能计算**：
$$T = \frac{1}{2}m|\vec{V}_{cm}|^2 + \frac{1}{2}\vec{\omega} \cdot \mathbf{I} \vec{\omega}$$

**势能计算**：
$$U = mgh$$

**总能量**：
$$E = T + U = const$$

### 8.3 数值精度验证

**Richardson外推**：
$$f_{exact} = f_h + \frac{f_h - f_{2h}}{2^p - 1}$$

**网格收敛指数**：
$$GCI = \frac{F_s |f_h - f_{2h}|}{(2^p - 1)f_h}$$

---

## 9. 工程应用 (Engineering Applications)

### 9.1 性能分析

**推力计算**：
$$T = \int_S p \hat{n} \cdot \hat{z} dS$$

**功率计算**：
$$P = \int_S \vec{F} \cdot \vec{V} dS$$

**效率计算**：
$$\eta = \frac{T \cdot V_{\infty}}{P}$$

### 9.2 载荷分析

**气动载荷**：
$$\vec{F}_{aero} = \int_S p \hat{n} dS$$

**惯性载荷**：
$$\vec{F}_{inertia} = m \vec{a}_{cm}$$

**总载荷**：
$$\vec{F}_{total} = \vec{F}_{aero} + \vec{F}_{inertia}$$

### 9.3 振动分析

**固有频率**：
$$\omega_n = \sqrt{\frac{k}{m}}$$

**阻尼比**：
$$\zeta = \frac{c}{2\sqrt{km}}$$

**响应函数**：
$$H(\omega) = \frac{1}{1 - (\omega/\omega_n)^2 + 2i\zeta(\omega/\omega_n)}$$

---

## 10. 结论 (Conclusions)

本文建立了循环翼转子运动学的完整数学框架，从刚体运动学基本原理出发，详细推导了复合运动描述、坐标变换关系和微分几何分析方法。该理论框架为循环翼转子的几何建模和运动学分析提供了坚实的数学基础。

---

## 参考文献 (References)

1. Goldstein, H., "Classical Mechanics", Addison-Wesley, 2002.
2. Do Carmo, M.P., "Differential Geometry of Curves and Surfaces", Prentice Hall, 1976.
3. Shabana, A.A., "Dynamics of Multibody Systems", Cambridge University Press, 2005.
4. Leishman, J.G., "Principles of Helicopter Aerodynamics", Cambridge University Press, 2006.
5. Greenwood, D.T., "Principles of Dynamics", Prentice Hall, 1988.

---

**文档状态**: ✅ 完成  
**审核状态**: 待审核  
**适用范围**: 学术研究与工程应用
