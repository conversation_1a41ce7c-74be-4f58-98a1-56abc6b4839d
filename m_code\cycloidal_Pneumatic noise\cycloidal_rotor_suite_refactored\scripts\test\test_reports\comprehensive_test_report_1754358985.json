{"summary": {"total_tests": 9, "passed_tests": 4, "failed_tests": 5, "pass_rate": 44.44444444444444, "total_execution_time": 2.5259482860565186, "timestamp": "2025-08-05 09:56:25"}, "detailed_results": {"1. 功能完整性验证": {"success": false, "execution_time": 2.2132434844970703, "timestamp": "2025-08-05 09:56:25"}, "2. 性能基准测试": {"success": true, "execution_time": 0.0019459724426269531, "timestamp": "2025-08-05 09:56:25"}, "3. 边界条件测试": {"success": false, "execution_time": 0.0019989013671875, "timestamp": "2025-08-05 09:56:25"}, "4. 接口一致性验证": {"success": false, "execution_time": 0.0019991397857666016, "timestamp": "2025-08-05 09:56:25"}, "5. 文档符合性检查": {"success": true, "execution_time": 0.002000093460083008, "timestamp": "2025-08-05 09:56:25"}, "6. 内存使用分析": {"success": false, "execution_time": 0.2947819232940674, "timestamp": "2025-08-05 09:56:25"}, "7. 数值精度验证": {"success": "True", "execution_time": 0.0021114349365234375, "timestamp": "2025-08-05 09:56:25"}, "8. 集成工作流测试": {"success": false, "execution_time": 0.0009362697601318359, "timestamp": "2025-08-05 09:56:25"}, "系统稳定性测试": {"success": "True", "execution_time": 0.0019991397857666016, "timestamp": "2025-08-05 09:56:25"}}, "performance_metrics": {"bemt_solver": {"execution_time": 0.0009024143218994141, "operations_per_second": 11081.384412153237}, "memory_usage": 294.6796875, "numerical_accuracy": {"rmse": 0.0010498178682688216, "mae": 0.0008637297266770851, "correlation": 0.9999988887307052, "accuracy_pass": "True"}, "stability": {"mean_result": 4.961322659299535, "std_dev": 0.2319138691636283, "coefficient_of_variation": 0.046744363366274404, "is_stable": "True"}}, "overall_status": "FAIL"}