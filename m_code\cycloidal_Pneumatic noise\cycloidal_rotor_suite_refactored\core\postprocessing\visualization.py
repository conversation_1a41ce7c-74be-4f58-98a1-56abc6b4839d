"""
3D可视化器
=========

提供3D可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
# 在新版本matplotlib中，Axes3D会自动注册，不需要显式导入
try:
    from mpl_toolkits.mplot3d import Axes3D
except ImportError:
    pass  # 新版本matplotlib不需要显式导入
import matplotlib.patches as patches
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import warnings

class Visualizer3D:
    """
    3D可视化器
    
    提供各种3D可视化功能：
    - 转子几何可视化
    - 流场可视化
    - 尾迹可视化
    - 声场可视化
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化3D可视化器
        
        Args:
            config: 可视化配置参数
        """
        self.config = config or {}
        
        # 可视化配置
        self.figure_size = self.config.get('figure_size', (12, 10))
        self.dpi = self.config.get('dpi', 300)
        self.view_angle = self.config.get('view_angle', (30, 45))  # (elevation, azimuth)
        
        # 颜色配置
        self.blade_color = self.config.get('blade_color', 'steelblue')
        self.hub_color = self.config.get('hub_color', 'darkgray')
        self.wake_color = self.config.get('wake_color', 'red')
        self.velocity_colormap = self.config.get('velocity_colormap', 'viridis')
        
        print("✅ 3D可视化器初始化完成")
        print(f"   图像尺寸: {self.figure_size}")
        print(f"   视角: 仰角{self.view_angle[0]}°, 方位角{self.view_angle[1]}°")
    
    def plot_rotor_geometry(self, geometry_data: Dict[str, Any],
                          save_path: Optional[Path] = None) -> Path:
        """
        可视化转子几何
        
        Args:
            geometry_data: 几何数据
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig = plt.figure(figsize=self.figure_size)
        ax = fig.add_subplot(111, projection='3d')
        
        # 提取几何参数
        R_rotor = geometry_data.get('R_rotor', 1.0)
        R_hub = geometry_data.get('R_hub', 0.1)
        B = geometry_data.get('B', 3)  # 桨叶数
        chord_distribution = geometry_data.get('chord_distribution', np.full(10, 0.1))
        
        # 绘制桨叶
        for blade_idx in range(B):
            angle = blade_idx * 2 * np.pi / B
            self._draw_blade(ax, angle, R_hub, R_rotor, chord_distribution)
        
        # 绘制轮毂
        self._draw_hub(ax, R_hub)
        
        # 绘制转子圆盘
        self._draw_rotor_disk(ax, R_rotor)
        
        # 设置坐标轴
        ax.set_xlim([-R_rotor*1.2, R_rotor*1.2])
        ax.set_ylim([-R_rotor*1.2, R_rotor*1.2])
        ax.set_zlim([-R_rotor*0.3, R_rotor*0.3])
        
        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_zlabel('Z [m]')
        ax.set_title('转子几何', fontsize=16, fontweight='bold')
        
        # 设置视角
        ax.view_init(elev=self.view_angle[0], azim=self.view_angle[1])
        
        # 保存图像
        if save_path is None:
            save_path = Path('rotor_geometry.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_velocity_field(self, velocity_data: Dict[str, Any],
                          save_path: Optional[Path] = None) -> Path:
        """
        可视化速度场
        
        Args:
            velocity_data: 速度场数据
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig = plt.figure(figsize=self.figure_size)
        ax = fig.add_subplot(111, projection='3d')
        
        # 提取速度场数据
        positions = velocity_data.get('positions', np.random.rand(50, 3) * 2 - 1)
        velocities = velocity_data.get('velocities', np.random.rand(50, 3) * 0.5)
        
        positions = np.array(positions)
        velocities = np.array(velocities)
        
        # 计算速度幅值
        velocity_magnitude = np.linalg.norm(velocities, axis=1)
        
        # 绘制速度矢量
        quiver = ax.quiver(positions[:, 0], positions[:, 1], positions[:, 2],
                          velocities[:, 0], velocities[:, 1], velocities[:, 2],
                          length=0.1, normalize=True,
                          cmap=self.velocity_colormap)
        
        # 添加颜色条
        mappable = plt.cm.ScalarMappable(cmap=self.velocity_colormap)
        mappable.set_array(velocity_magnitude)
        cbar = plt.colorbar(mappable, ax=ax, shrink=0.8)
        cbar.set_label('速度幅值 [m/s]')
        
        # 设置坐标轴
        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_zlabel('Z [m]')
        ax.set_title('速度场可视化', fontsize=16, fontweight='bold')
        
        # 设置视角
        ax.view_init(elev=self.view_angle[0], azim=self.view_angle[1])
        
        # 保存图像
        if save_path is None:
            save_path = Path('velocity_field.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_wake_visualization(self, wake_data: Dict[str, Any],
                              save_path: Optional[Path] = None) -> Path:
        """
        可视化尾迹结构
        
        Args:
            wake_data: 尾迹数据
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig = plt.figure(figsize=self.figure_size)
        ax = fig.add_subplot(111, projection='3d')
        
        # 提取尾迹数据
        wake_positions = wake_data.get('positions', [])
        wake_ages = wake_data.get('ages', [])
        circulation = wake_data.get('circulation', [])
        
        if len(wake_positions) == 0:
            # 生成示例尾迹数据
            wake_positions, wake_ages, circulation = self._generate_sample_wake_data()
        
        wake_positions = np.array(wake_positions)
        wake_ages = np.array(wake_ages)
        circulation = np.array(circulation)
        
        # 根据年龄着色
        colors = plt.cm.Reds(wake_ages / np.max(wake_ages) if len(wake_ages) > 0 else [0.5])
        
        # 绘制尾迹点
        scatter = ax.scatter(wake_positions[:, 0], wake_positions[:, 1], wake_positions[:, 2],
                           c=colors, s=50, alpha=0.7)
        
        # 绘制尾迹线
        if len(wake_positions) > 1:
            ax.plot(wake_positions[:, 0], wake_positions[:, 1], wake_positions[:, 2],
                   color=self.wake_color, alpha=0.5, linewidth=1)
        
        # 添加颜色条
        if len(wake_ages) > 0:
            mappable = plt.cm.ScalarMappable(cmap='Reds')
            mappable.set_array(wake_ages)
            cbar = plt.colorbar(mappable, ax=ax, shrink=0.8)
            cbar.set_label('尾迹年龄 [s]')
        
        # 设置坐标轴
        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_zlabel('Z [m]')
        ax.set_title('尾迹可视化', fontsize=16, fontweight='bold')
        
        # 设置视角
        ax.view_init(elev=self.view_angle[0], azim=self.view_angle[1])
        
        # 保存图像
        if save_path is None:
            save_path = Path('wake_visualization.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_acoustic_field(self, acoustic_data: Dict[str, Any],
                          save_path: Optional[Path] = None) -> Path:
        """
        可视化声场
        
        Args:
            acoustic_data: 声学数据
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig = plt.figure(figsize=self.figure_size)
        ax = fig.add_subplot(111, projection='3d')
        
        # 提取声学数据
        observer_positions = acoustic_data.get('observer_positions', 
                                             [[10, 0, 0], [0, 10, 0], [-10, 0, 0], [0, -10, 0]])
        sound_levels = acoustic_data.get('sound_levels', [80, 75, 70, 72])
        
        observer_positions = np.array(observer_positions)
        sound_levels = np.array(sound_levels)
        
        # 绘制观察点
        scatter = ax.scatter(observer_positions[:, 0], observer_positions[:, 1], observer_positions[:, 2],
                           c=sound_levels, s=100, cmap='hot', alpha=0.8)
        
        # 添加声源位置（原点）
        ax.scatter([0], [0], [0], c='black', s=200, marker='*', label='声源')
        
        # 绘制声传播线
        for pos in observer_positions:
            ax.plot([0, pos[0]], [0, pos[1]], [0, pos[2]], 
                   'k--', alpha=0.3, linewidth=1)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
        cbar.set_label('声压级 [dB]')
        
        # 设置坐标轴
        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_zlabel('Z [m]')
        ax.set_title('声场可视化', fontsize=16, fontweight='bold')
        ax.legend()
        
        # 设置视角
        ax.view_init(elev=self.view_angle[0], azim=self.view_angle[1])
        
        # 保存图像
        if save_path is None:
            save_path = Path('acoustic_field.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def _draw_blade(self, ax, angle: float, r_hub: float, r_tip: float, 
                   chord_distribution: np.ndarray):
        """绘制单个桨叶"""
        n_sections = len(chord_distribution)
        r_stations = np.linspace(r_hub, r_tip, n_sections)
        
        # 桨叶前缘和后缘
        leading_edge = []
        trailing_edge = []
        
        for i, (r, chord) in enumerate(zip(r_stations, chord_distribution)):
            # 桨叶截面位置
            x_le = r * np.cos(angle) - chord/4 * np.sin(angle)
            y_le = r * np.sin(angle) + chord/4 * np.cos(angle)
            x_te = r * np.cos(angle) + 3*chord/4 * np.sin(angle)
            y_te = r * np.sin(angle) - 3*chord/4 * np.cos(angle)
            
            leading_edge.append([x_le, y_le, 0])
            trailing_edge.append([x_te, y_te, 0])
        
        leading_edge = np.array(leading_edge)
        trailing_edge = np.array(trailing_edge)
        
        # 绘制桨叶轮廓
        ax.plot(leading_edge[:, 0], leading_edge[:, 1], leading_edge[:, 2],
               color=self.blade_color, linewidth=3, label='前缘' if angle == 0 else "")
        ax.plot(trailing_edge[:, 0], trailing_edge[:, 1], trailing_edge[:, 2],
               color=self.blade_color, linewidth=2, linestyle='--', 
               label='后缘' if angle == 0 else "")
        
        # 连接前后缘
        for i in range(0, n_sections, 2):  # 每隔一个截面连接
            ax.plot([leading_edge[i, 0], trailing_edge[i, 0]],
                   [leading_edge[i, 1], trailing_edge[i, 1]],
                   [leading_edge[i, 2], trailing_edge[i, 2]],
                   color=self.blade_color, linewidth=1, alpha=0.5)
    
    def _draw_hub(self, ax, r_hub: float):
        """绘制轮毂"""
        # 创建轮毂圆
        theta = np.linspace(0, 2*np.pi, 50)
        x_hub = r_hub * np.cos(theta)
        y_hub = r_hub * np.sin(theta)
        z_hub = np.zeros_like(x_hub)
        
        ax.plot(x_hub, y_hub, z_hub, color=self.hub_color, linewidth=4, label='轮毂')
    
    def _draw_rotor_disk(self, ax, r_rotor: float):
        """绘制转子圆盘"""
        # 创建转子圆盘轮廓
        theta = np.linspace(0, 2*np.pi, 100)
        x_disk = r_rotor * np.cos(theta)
        y_disk = r_rotor * np.sin(theta)
        z_disk = np.zeros_like(x_disk)
        
        ax.plot(x_disk, y_disk, z_disk, color='gray', linewidth=1, 
               linestyle=':', alpha=0.5, label='转子圆盘')
    
    def _generate_sample_wake_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """生成示例尾迹数据"""
        n_points = 50
        
        # 螺旋形尾迹
        t = np.linspace(0, 4*np.pi, n_points)
        x = 0.5 * t * np.cos(t)
        y = 0.5 * t * np.sin(t)
        z = -0.1 * t
        
        positions = np.column_stack([x, y, z])
        ages = t / (4*np.pi) * 2.0  # 0到2秒
        circulation = 2.0 * np.exp(-ages/1.0)  # 衰减的环量
        
        return positions, ages, circulation


class AdvancedVisualizationSuite:
    """
    高级可视化套件

    提供专业级的可视化功能：
    - 交互式3D可视化
    - 动画生成
    - 多视图对比
    - 专业报告图表
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化高级可视化套件"""
        self.config = config or {}

        # 基础可视化器
        self.visualizer_3d = Visualizer3D(config)

        # 动画配置
        self.animation_fps = self.config.get('animation_fps', 30)
        self.animation_duration = self.config.get('animation_duration', 5.0)

        # 交互式配置
        self.enable_interactive = self.config.get('enable_interactive', True)

        print("✅ 高级可视化套件初始化完成")

    def create_comprehensive_dashboard(self, simulation_data: Dict[str, Any],
                                     output_path: Optional[str] = None) -> plt.Figure:
        """
        创建综合仪表板

        Args:
            simulation_data: 仿真数据
            output_path: 输出路径

        Returns:
            matplotlib图形对象
        """
        # 创建多子图布局
        fig = plt.figure(figsize=(20, 16))

        # 子图1: 转子几何和尾迹
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')
        self._plot_rotor_and_wake(ax1, simulation_data)
        ax1.set_title('转子几何与尾迹结构', fontsize=14, fontweight='bold')

        # 子图2: 力系数时间历程
        ax2 = fig.add_subplot(2, 3, 2)
        self._plot_force_coefficients(ax2, simulation_data)
        ax2.set_title('力系数时间历程', fontsize=14, fontweight='bold')

        # 子图3: 频谱分析
        ax3 = fig.add_subplot(2, 3, 3)
        self._plot_frequency_spectrum(ax3, simulation_data)
        ax3.set_title('声学频谱分析', fontsize=14, fontweight='bold')

        # 子图4: 载荷分布
        ax4 = fig.add_subplot(2, 3, 4)
        self._plot_load_distribution(ax4, simulation_data)
        ax4.set_title('径向载荷分布', fontsize=14, fontweight='bold')

        # 子图5: 性能参数
        ax5 = fig.add_subplot(2, 3, 5)
        self._plot_performance_metrics(ax5, simulation_data)
        ax5.set_title('性能参数对比', fontsize=14, fontweight='bold')

        # 子图6: 流场可视化
        ax6 = fig.add_subplot(2, 3, 6)
        self._plot_flow_field(ax6, simulation_data)
        ax6.set_title('流场速度分布', fontsize=14, fontweight='bold')

        plt.tight_layout()

        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"✅ 综合仪表板已保存: {output_path}")

        return fig

    def _plot_rotor_and_wake(self, ax, data: Dict[str, Any]):
        """绘制转子和尾迹"""
        # 简化的转子几何
        theta = np.linspace(0, 2*np.pi, 100)
        rotor_radius = data.get('rotor_radius', 1.0)

        # 转子圆盘
        x_rotor = rotor_radius * np.cos(theta)
        y_rotor = rotor_radius * np.sin(theta)
        z_rotor = np.zeros_like(theta)

        ax.plot(x_rotor, y_rotor, z_rotor, 'b-', linewidth=3, label='转子圆盘')

        # 简化的尾迹
        if 'wake_data' in data:
            wake_x = data['wake_data'].get('x', np.linspace(0, 5, 50))
            wake_y = data['wake_data'].get('y', 0.1 * np.sin(wake_x))
            wake_z = data['wake_data'].get('z', np.zeros_like(wake_x))

            ax.plot(wake_x, wake_y, wake_z, 'r--', linewidth=2, label='尾迹')

        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_zlabel('Z [m]')
        ax.legend()

    def _plot_force_coefficients(self, ax, data: Dict[str, Any]):
        """绘制力系数时间历程"""
        time = data.get('time', np.linspace(0, 1, 100))

        # 模拟力系数数据
        cl = data.get('cl', 0.8 + 0.2 * np.sin(2*np.pi*time))
        cd = data.get('cd', 0.05 + 0.01 * np.sin(4*np.pi*time))

        ax.plot(time, cl, 'b-', linewidth=2, label='升力系数 Cl')
        ax.plot(time, cd, 'r-', linewidth=2, label='阻力系数 Cd')

        ax.set_xlabel('时间 [s]')
        ax.set_ylabel('力系数')
        ax.grid(True, alpha=0.3)
        ax.legend()

    def _plot_frequency_spectrum(self, ax, data: Dict[str, Any]):
        """绘制频谱分析"""
        frequency = data.get('frequency', np.logspace(1, 3, 100))
        spl = data.get('spl', 60 + 20 * np.exp(-frequency/100))

        ax.semilogx(frequency, spl, 'g-', linewidth=2)
        ax.set_xlabel('频率 [Hz]')
        ax.set_ylabel('声压级 [dB]')
        ax.grid(True, alpha=0.3)
        ax.set_xlim([10, 1000])

    def _plot_load_distribution(self, ax, data: Dict[str, Any]):
        """绘制载荷分布"""
        radius = data.get('radius', np.linspace(0.1, 1.0, 20))
        thrust_distribution = data.get('thrust_distribution',
                                     100 * (1 - radius) * np.exp(-2*radius))

        ax.plot(radius, thrust_distribution, 'purple', linewidth=3, marker='o')
        ax.set_xlabel('径向位置 r/R')
        ax.set_ylabel('推力分布 [N/m]')
        ax.grid(True, alpha=0.3)

    def _plot_performance_metrics(self, ax, data: Dict[str, Any]):
        """绘制性能参数"""
        metrics = ['推力系数', '功率系数', '效率', '噪声级']
        values = data.get('performance_values', [0.008, 0.002, 0.75, 65])

        bars = ax.bar(metrics, values, color=['blue', 'red', 'green', 'orange'])
        ax.set_ylabel('数值')
        ax.set_title('关键性能指标')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom')

    def _plot_flow_field(self, ax, data: Dict[str, Any]):
        """绘制流场"""
        # 创建网格
        x = np.linspace(-2, 2, 20)
        y = np.linspace(-2, 2, 20)
        X, Y = np.meshgrid(x, y)

        # 模拟速度场
        U = data.get('velocity_u', -Y)
        V = data.get('velocity_v', X)

        # 绘制流线
        ax.streamplot(X, Y, U, V, density=1.5, color='blue')

        # 添加转子位置
        circle = plt.Circle((0, 0), 1.0, fill=False, color='red', linewidth=3)
        ax.add_patch(circle)

        ax.set_xlabel('X [m]')
        ax.set_ylabel('Y [m]')
        ax.set_aspect('equal')
