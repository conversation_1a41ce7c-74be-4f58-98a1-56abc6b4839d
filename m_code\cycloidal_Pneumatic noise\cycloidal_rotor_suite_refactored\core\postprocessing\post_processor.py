"""
后处理器核心实现
===============

提供完整的后处理功能
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import logging

from .plot_manager import PlotManager
from .data_exporter import DataExporter
from .visualization import Visualizer3D
from .report_generator import ReportGenerator

class PostProcessor:
    """
    后处理器主类
    
    提供完整的后处理功能，包括：
    - 数据分析和处理
    - 结果可视化
    - 数据导出
    - 报告生成
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化后处理器
        
        Args:
            config: 后处理配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化子模块
        self.plot_manager = PlotManager(self.config.get('plot_config', {}))
        self.data_exporter = DataExporter(self.config.get('export_config', {}))
        self.visualizer = Visualizer3D(self.config.get('visualization_config', {}))
        self.report_generator = ReportGenerator(self.config.get('report_config', {}))
        
        # 输出目录
        self.output_directory = Path(self.config.get('output_directory', './results'))
        self.output_directory.mkdir(exist_ok=True)
        
        # 处理历史
        self.processing_history = []
        
        print("✅ 后处理器初始化完成")
        print(f"   输出目录: {self.output_directory}")
        print(f"   可用功能: 绘图、导出、3D可视化、报告生成")
    
    def process_simulation_results(self, results: Dict[str, Any], 
                                 analysis_type: str = 'comprehensive') -> Dict[str, Any]:
        """
        处理仿真结果
        
        Args:
            results: 仿真结果数据
            analysis_type: 分析类型 ('basic', 'comprehensive', 'custom')
            
        Returns:
            处理后的结果字典
        """
        print(f"\n🔧 开始后处理分析: {analysis_type}")
        start_time = time.time()
        
        processed_results = {
            'metadata': {
                'analysis_type': analysis_type,
                'processing_time': None,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'statistics': {},
            'plots': {},
            'exports': {},
            'visualizations': {}
        }
        
        try:
            # 1. 数据统计分析
            processed_results['statistics'] = self._perform_statistical_analysis(results)
            
            # 2. 生成图表
            if analysis_type in ['comprehensive', 'custom']:
                processed_results['plots'] = self._generate_plots(results)
            
            # 3. 3D可视化
            if analysis_type == 'comprehensive':
                processed_results['visualizations'] = self._create_visualizations(results)
            
            # 4. 数据导出
            processed_results['exports'] = self._export_data(results)
            
            # 5. 生成报告
            if analysis_type == 'comprehensive':
                report_path = self._generate_report(results, processed_results)
                processed_results['report_path'] = report_path
            
            processing_time = time.time() - start_time
            processed_results['metadata']['processing_time'] = processing_time
            
            # 记录处理历史
            self.processing_history.append({
                'timestamp': processed_results['metadata']['timestamp'],
                'analysis_type': analysis_type,
                'processing_time': processing_time,
                'success': True
            })
            
            print(f"✅ 后处理完成，耗时: {processing_time:.2f}s")
            return processed_results
            
        except Exception as e:
            self.logger.error(f"后处理失败: {e}")
            print(f"❌ 后处理失败: {e}")
            
            # 记录失败
            self.processing_history.append({
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'analysis_type': analysis_type,
                'processing_time': time.time() - start_time,
                'success': False,
                'error': str(e)
            })
            
            raise
    
    def _perform_statistical_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计分析"""
        stats = {}
        
        # 力和力矩统计
        if 'forces' in results:
            forces = np.array(results['forces'])
            stats['forces'] = {
                'mean': np.mean(forces, axis=0).tolist(),
                'std': np.std(forces, axis=0).tolist(),
                'max': np.max(forces, axis=0).tolist(),
                'min': np.min(forces, axis=0).tolist(),
                'rms': np.sqrt(np.mean(forces**2, axis=0)).tolist()
            }
        
        if 'moments' in results:
            moments = np.array(results['moments'])
            stats['moments'] = {
                'mean': np.mean(moments, axis=0).tolist(),
                'std': np.std(moments, axis=0).tolist(),
                'max': np.max(moments, axis=0).tolist(),
                'min': np.min(moments, axis=0).tolist(),
                'rms': np.sqrt(np.mean(moments**2, axis=0)).tolist()
            }
        
        # 性能参数统计
        if 'performance' in results:
            perf = results['performance']
            stats['performance'] = {}
            for key, value in perf.items():
                if isinstance(value, (list, np.ndarray)):
                    value_array = np.array(value)
                    stats['performance'][key] = {
                        'mean': np.mean(value_array),
                        'std': np.std(value_array),
                        'max': np.max(value_array),
                        'min': np.min(value_array)
                    }
                else:
                    stats['performance'][key] = value
        
        # 收敛性统计
        if 'convergence_history' in results:
            conv_hist = np.array(results['convergence_history'])
            stats['convergence'] = {
                'final_residual': conv_hist[-1] if len(conv_hist) > 0 else None,
                'iterations': len(conv_hist),
                'convergence_rate': self._calculate_convergence_rate(conv_hist)
            }
        
        return stats
    
    def _calculate_convergence_rate(self, convergence_history: np.ndarray) -> float:
        """计算收敛率"""
        if len(convergence_history) < 2:
            return 0.0
        
        # 计算对数收敛率
        log_residuals = np.log10(np.maximum(convergence_history, 1e-16))
        if len(log_residuals) > 1:
            # 线性拟合计算斜率
            iterations = np.arange(len(log_residuals))
            slope = np.polyfit(iterations, log_residuals, 1)[0]
            return abs(slope)
        
        return 0.0
    
    def _generate_plots(self, results: Dict[str, Any]) -> Dict[str, str]:
        """生成图表"""
        plot_paths = {}
        
        try:
            # 力系数时间历程
            if 'forces' in results and 'time' in results:
                plot_path = self.plot_manager.plot_force_coefficients(
                    results['time'], results['forces'], 
                    save_path=self.output_directory / 'force_coefficients.png'
                )
                plot_paths['force_coefficients'] = str(plot_path)
            
            # 收敛历程
            if 'convergence_history' in results:
                plot_path = self.plot_manager.plot_convergence_history(
                    results['convergence_history'],
                    save_path=self.output_directory / 'convergence.png'
                )
                plot_paths['convergence'] = str(plot_path)
            
            # 频谱分析
            if 'acoustic_results' in results:
                acoustic = results['acoustic_results']
                if 'frequencies' in acoustic and 'spectrum' in acoustic:
                    plot_path = self.plot_manager.plot_frequency_spectrum(
                        acoustic['frequencies'], acoustic['spectrum'],
                        save_path=self.output_directory / 'frequency_spectrum.png'
                    )
                    plot_paths['frequency_spectrum'] = str(plot_path)
            
        except Exception as e:
            self.logger.warning(f"图表生成部分失败: {e}")
        
        return plot_paths
    
    def _create_visualizations(self, results: Dict[str, Any]) -> Dict[str, str]:
        """创建3D可视化"""
        viz_paths = {}
        
        try:
            # 转子几何可视化
            if 'geometry' in results:
                viz_path = self.visualizer.plot_rotor_geometry(
                    results['geometry'],
                    save_path=self.output_directory / 'rotor_geometry.png'
                )
                viz_paths['rotor_geometry'] = str(viz_path)
            
            # 流场可视化
            if 'velocity_field' in results:
                viz_path = self.visualizer.plot_velocity_field(
                    results['velocity_field'],
                    save_path=self.output_directory / 'velocity_field.png'
                )
                viz_paths['velocity_field'] = str(viz_path)
            
        except Exception as e:
            self.logger.warning(f"3D可视化部分失败: {e}")
        
        return viz_paths
    
    def _export_data(self, results: Dict[str, Any]) -> Dict[str, str]:
        """导出数据"""
        export_paths = {}
        
        try:
            # 导出为CSV
            csv_path = self.data_exporter.export_to_csv(
                results, self.output_directory / 'results.csv'
            )
            export_paths['csv'] = str(csv_path)
            
            # 导出为JSON
            json_path = self.data_exporter.export_to_json(
                results, self.output_directory / 'results.json'
            )
            export_paths['json'] = str(json_path)
            
            # 导出为HDF5（如果数据量大）
            if self._is_large_dataset(results):
                hdf5_path = self.data_exporter.export_to_hdf5(
                    results, self.output_directory / 'results.h5'
                )
                export_paths['hdf5'] = str(hdf5_path)
            
        except Exception as e:
            self.logger.warning(f"数据导出部分失败: {e}")
        
        return export_paths
    
    def _generate_report(self, results: Dict[str, Any], 
                        processed_results: Dict[str, Any]) -> str:
        """生成报告"""
        try:
            report_path = self.report_generator.generate_comprehensive_report(
                results, processed_results, 
                output_path=self.output_directory / 'analysis_report.html'
            )
            return str(report_path)
        except Exception as e:
            self.logger.warning(f"报告生成失败: {e}")
            return ""
    
    def _is_large_dataset(self, results: Dict[str, Any]) -> bool:
        """判断是否为大数据集"""
        total_size = 0
        for key, value in results.items():
            if isinstance(value, (list, np.ndarray)):
                total_size += len(value)
        
        return total_size > 10000  # 超过10k数据点认为是大数据集
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        if not self.processing_history:
            return {'message': '暂无处理历史'}
        
        total_runs = len(self.processing_history)
        successful_runs = sum(1 for run in self.processing_history if run['success'])
        avg_time = np.mean([run['processing_time'] for run in self.processing_history])
        
        return {
            'total_runs': total_runs,
            'successful_runs': successful_runs,
            'success_rate': successful_runs / total_runs * 100,
            'average_processing_time': avg_time,
            'last_run': self.processing_history[-1]
        }
