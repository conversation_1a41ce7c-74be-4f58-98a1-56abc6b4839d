"""
CLI工具函数
===========

提供CLI相关的工具函数和配置
"""

import sys
import logging
import colorama
from pathlib import Path
from typing import Optional


def setup_logging(verbose: int = 0, quiet: bool = False, log_file: Optional[str] = None):
    """设置日志配置"""
    
    # 确定日志级别
    if quiet:
        level = logging.ERROR
    elif verbose == 0:
        level = logging.INFO
    elif verbose == 1:
        level = logging.DEBUG
    else:  # verbose >= 2
        level = logging.DEBUG
    
    # 配置日志格式
    if verbose >= 2:
        # 详细格式
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    else:
        # 简单格式
        format_str = '%(levelname)s: %(message)s'
    
    # 配置处理器
    handlers = []
    
    # 控制台处理器
    if not quiet:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(format_str))
        handlers.append(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        handlers.append(file_handler)
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        handlers=handlers,
        force=True
    )


class CLIUtils:
    """CLI工具类"""
    
    @staticmethod
    def init_colorama():
        """初始化颜色支持"""
        colorama.init(autoreset=True)
    
    @staticmethod
    def print_success(message: str):
        """打印成功消息"""
        print(f"✅ {message}")
    
    @staticmethod
    def print_error(message: str):
        """打印错误消息"""
        print(f"❌ {message}")
    
    @staticmethod
    def print_warning(message: str):
        """打印警告消息"""
        print(f"⚠️ {message}")
    
    @staticmethod
    def print_info(message: str):
        """打印信息消息"""
        print(f"ℹ️ {message}")
    
    @staticmethod
    def print_progress(message: str):
        """打印进度消息"""
        print(f"🔄 {message}")
    
    @staticmethod
    def validate_file_path(file_path: str, must_exist: bool = True) -> bool:
        """验证文件路径"""
        path = Path(file_path)
        
        if must_exist and not path.exists():
            CLIUtils.print_error(f"文件不存在: {file_path}")
            return False
        
        if must_exist and not path.is_file():
            CLIUtils.print_error(f"路径不是文件: {file_path}")
            return False
        
        return True
    
    @staticmethod
    def validate_directory_path(dir_path: str, create_if_missing: bool = False) -> bool:
        """验证目录路径"""
        path = Path(dir_path)
        
        if not path.exists():
            if create_if_missing:
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    CLIUtils.print_info(f"创建目录: {dir_path}")
                    return True
                except Exception as e:
                    CLIUtils.print_error(f"无法创建目录 {dir_path}: {e}")
                    return False
            else:
                CLIUtils.print_error(f"目录不存在: {dir_path}")
                return False
        
        if not path.is_dir():
            CLIUtils.print_error(f"路径不是目录: {dir_path}")
            return False
        
        return True
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024**2:
            return f"{size_bytes/1024:.1f} KB"
        elif size_bytes < 1024**3:
            return f"{size_bytes/1024**2:.1f} MB"
        else:
            return f"{size_bytes/1024**3:.1f} GB"
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化时间长度"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = seconds % 60
            return f"{minutes}m {secs:.1f}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = seconds % 60
            return f"{hours}h {minutes}m {secs:.1f}s"
    
    @staticmethod
    def confirm_action(message: str, default: bool = False) -> bool:
        """确认操作"""
        suffix = " [Y/n]" if default else " [y/N]"
        
        try:
            response = input(f"{message}{suffix}: ").strip().lower()
            
            if not response:
                return default
            
            return response in ['y', 'yes', '是', 'true', '1']
            
        except (KeyboardInterrupt, EOFError):
            print("\n操作已取消")
            return False
    
    @staticmethod
    def print_table(headers: list, rows: list, title: Optional[str] = None):
        """打印表格"""
        if title:
            print(f"\n{title}")
            print("=" * len(title))
        
        # 计算列宽
        col_widths = [len(str(header)) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # 打印表头
        header_row = " | ".join(
            str(header).ljust(width) 
            for header, width in zip(headers, col_widths)
        )
        print(f"| {header_row} |")
        
        # 打印分隔线
        separator = "-+-".join("-" * width for width in col_widths)
        print(f"|-{separator}-|")
        
        # 打印数据行
        for row in rows:
            data_row = " | ".join(
                str(cell).ljust(width) 
                for cell, width in zip(row, col_widths)
            )
            print(f"| {data_row} |")
        
        print()


class ProgressBar:
    """简单的进度条"""
    
    def __init__(self, total: int, width: int = 50, prefix: str = "进度"):
        self.total = total
        self.width = width
        self.prefix = prefix
        self.current = 0
    
    def update(self, increment: int = 1):
        """更新进度"""
        self.current += increment
        self._print_progress()
    
    def set_progress(self, current: int):
        """设置当前进度"""
        self.current = current
        self._print_progress()
    
    def _print_progress(self):
        """打印进度条"""
        if self.total == 0:
            return
        
        percent = min(100, (self.current / self.total) * 100)
        filled_width = int(self.width * self.current // self.total)
        
        bar = "█" * filled_width + "░" * (self.width - filled_width)
        
        print(f"\r{self.prefix}: |{bar}| {percent:.1f}% ({self.current}/{self.total})", 
              end="", flush=True)
        
        if self.current >= self.total:
            print()  # 完成时换行


# 初始化颜色支持
CLIUtils.init_colorama()
