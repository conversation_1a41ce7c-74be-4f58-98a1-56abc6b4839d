"""
主CLI入口点
===========

提供统一的命令行界面，支持多种操作模式
"""

import sys
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from .commands import SimulationCommand, AnalysisCommand, ValidationCommand, ConfigCommand
from .utils import setup_logging


class CycloidalRotorCLI:
    """循环翼转子仿真套件命令行界面"""
    
    def __init__(self):
        """初始化CLI"""
        self.parser = self._create_parser()
        self.commands = {
            'simulate': SimulationCommand(),
            'analyze': AnalysisCommand(),
            'validate': ValidationCommand(),
            'config': ConfigCommand()
        }
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行解析器"""
        parser = argparse.ArgumentParser(
            prog='cycloidal-rotor',
            description='循环翼转子仿真套件 - 专业的循环翼气动声学仿真工具',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  cycloidal-rotor simulate --config config/default.yaml --output results/
  cycloidal-rotor analyze --input results/simulation_data.h5 --plot
  cycloidal-rotor validate --test-case hovering --tolerance strict
  cycloidal-rotor config --create --template basic
            """
        )
        
        # 全局选项
        parser.add_argument(
            '--version', 
            action='version', 
            version='%(prog)s 1.0.0'
        )
        parser.add_argument(
            '--verbose', '-v',
            action='count',
            default=0,
            help='增加输出详细程度 (-v, -vv, -vvv)'
        )
        parser.add_argument(
            '--quiet', '-q',
            action='store_true',
            help='静默模式，只输出错误信息'
        )
        parser.add_argument(
            '--log-file',
            type=str,
            help='日志文件路径'
        )
        
        # 子命令
        subparsers = parser.add_subparsers(
            dest='command',
            help='可用命令',
            metavar='COMMAND'
        )
        
        # 仿真命令
        sim_parser = subparsers.add_parser(
            'simulate',
            help='运行循环翼转子仿真',
            description='执行完整的气动声学仿真'
        )
        self._add_simulation_args(sim_parser)
        
        # 分析命令
        analyze_parser = subparsers.add_parser(
            'analyze',
            help='分析仿真结果',
            description='对仿真数据进行后处理和分析'
        )
        self._add_analysis_args(analyze_parser)
        
        # 验证命令
        validate_parser = subparsers.add_parser(
            'validate',
            help='运行验证测试',
            description='执行模型验证和确认测试'
        )
        self._add_validation_args(validate_parser)
        
        # 配置命令
        config_parser = subparsers.add_parser(
            'config',
            help='配置管理',
            description='创建和管理仿真配置文件'
        )
        self._add_config_args(config_parser)
        
        return parser
    
    def _add_simulation_args(self, parser: argparse.ArgumentParser):
        """添加仿真命令参数"""
        parser.add_argument(
            '--config', '-c',
            type=str,
            required=True,
            help='仿真配置文件路径'
        )
        parser.add_argument(
            '--output', '-o',
            type=str,
            default='results/',
            help='输出目录路径'
        )
        parser.add_argument(
            '--solver',
            choices=['bemt', 'uvlm', 'coupled'],
            default='bemt',
            help='求解器类型'
        )
        parser.add_argument(
            '--fidelity',
            choices=['low', 'medium', 'high'],
            default='medium',
            help='仿真保真度级别'
        )
        parser.add_argument(
            '--parallel',
            type=int,
            default=1,
            help='并行进程数'
        )
        parser.add_argument(
            '--resume',
            type=str,
            help='从检查点恢复仿真'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不执行实际计算'
        )
    
    def _add_analysis_args(self, parser: argparse.ArgumentParser):
        """添加分析命令参数"""
        parser.add_argument(
            '--input', '-i',
            type=str,
            required=True,
            help='输入数据文件路径'
        )
        parser.add_argument(
            '--output', '-o',
            type=str,
            default='analysis/',
            help='分析结果输出目录'
        )
        parser.add_argument(
            '--plot',
            action='store_true',
            help='生成图表'
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='生成分析报告'
        )
        parser.add_argument(
            '--format',
            choices=['pdf', 'html', 'markdown'],
            default='pdf',
            help='报告格式'
        )
        parser.add_argument(
            '--metrics',
            nargs='+',
            help='计算指定的性能指标'
        )
    
    def _add_validation_args(self, parser: argparse.ArgumentParser):
        """添加验证命令参数"""
        parser.add_argument(
            '--test-case',
            type=str,
            help='指定验证测试用例'
        )
        parser.add_argument(
            '--tolerance',
            choices=['strict', 'moderate', 'relaxed'],
            default='moderate',
            help='验证容差级别'
        )
        parser.add_argument(
            '--output', '-o',
            type=str,
            default='validation_results/',
            help='验证结果输出目录'
        )
        parser.add_argument(
            '--list-cases',
            action='store_true',
            help='列出所有可用的测试用例'
        )
        parser.add_argument(
            '--compare',
            type=str,
            help='与实验数据对比的文件路径'
        )
    
    def _add_config_args(self, parser: argparse.ArgumentParser):
        """添加配置命令参数"""
        parser.add_argument(
            '--create',
            action='store_true',
            help='创建新的配置文件'
        )
        parser.add_argument(
            '--template',
            choices=['basic', 'advanced', 'research'],
            default='basic',
            help='配置模板类型'
        )
        parser.add_argument(
            '--output', '-o',
            type=str,
            default='config.yaml',
            help='配置文件输出路径'
        )
        parser.add_argument(
            '--validate',
            type=str,
            help='验证配置文件'
        )
    
    def run(self, args: Optional[List[str]] = None) -> int:
        """运行CLI"""
        try:
            # 解析命令行参数
            parsed_args = self.parser.parse_args(args)
            
            # 设置日志
            setup_logging(
                verbose=parsed_args.verbose,
                quiet=parsed_args.quiet,
                log_file=getattr(parsed_args, 'log_file', None)
            )
            
            # 检查是否指定了命令
            if not parsed_args.command:
                self.parser.print_help()
                return 1
            
            # 执行对应的命令
            command = self.commands.get(parsed_args.command)
            if command:
                return command.execute(parsed_args)
            else:
                print(f"错误：未知命令 '{parsed_args.command}'")
                return 1
                
        except KeyboardInterrupt:
            print("\n用户中断操作")
            return 130
        except Exception as e:
            logging.error(f"CLI执行失败: {e}")
            if parsed_args.verbose > 2:
                import traceback
                traceback.print_exc()
            return 1


def main_cli():
    """CLI主入口点"""
    cli = CycloidalRotorCLI()
    return cli.run()


if __name__ == '__main__':
    sys.exit(main_cli())
