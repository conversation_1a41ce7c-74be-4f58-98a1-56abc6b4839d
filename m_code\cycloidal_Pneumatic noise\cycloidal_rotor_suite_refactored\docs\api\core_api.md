# 核心API文档
## Core API Documentation

**版本**: v2.0  
**更新时间**: 2025-01-08  
**API稳定性**: 稳定版  

---

## 📋 **API概述**

循环翼转子仿真套件提供了完整的Python API，支持从几何建模到结果分析的全流程编程接口。API设计遵循面向对象原则，提供清晰的模块化结构。

### **主要模块**

- **`core.geometry`**: 几何建模和运动学
- **`core.aerodynamics`**: 空气动力学求解器
- **`core.acoustics`**: 声学分析
- **`core.postprocessing`**: 后处理和可视化
- **`scripts.validation`**: 验证和确认
- **`config`**: 配置管理

---

## 🏗️ **几何模块 (core.geometry)**

### **RotorGeometry类**

```python
class RotorGeometry:
    """循环翼转子几何定义"""
    
    def __init__(self, radius: float, hub_radius: float, blade_count: int):
        """
        初始化转子几何
        
        Parameters:
        -----------
        radius : float
            转子半径 (m)
        hub_radius : float
            轮毂半径 (m)
        blade_count : int
            桨叶数量
        """
        
    def set_blade_geometry(self, chord: float, span: float, 
                          twist_distribution: np.ndarray = None):
        """
        设置桨叶几何参数
        
        Parameters:
        -----------
        chord : float
            桨叶弦长 (m)
        span : float
            桨叶展长 (m)
        twist_distribution : np.ndarray, optional
            扭转分布 (rad)
        """
        
    def get_blade_position(self, blade_id: int, azimuth: float) -> np.ndarray:
        """
        获取指定桨叶的位置
        
        Parameters:
        -----------
        blade_id : int
            桨叶编号 (0-based)
        azimuth : float
            方位角 (rad)
            
        Returns:
        --------
        np.ndarray
            桨叶位置坐标 [x, y, z]
        """
        
    def compute_blade_velocity(self, blade_id: int, azimuth: float, 
                              omega: float, pitch_rate: float) -> np.ndarray:
        """
        计算桨叶速度
        
        Parameters:
        -----------
        blade_id : int
            桨叶编号
        azimuth : float
            方位角 (rad)
        omega : float
            转子角速度 (rad/s)
        pitch_rate : float
            俯仰角速度 (rad/s)
            
        Returns:
        --------
        np.ndarray
            桨叶速度 [vx, vy, vz]
        """
```

### **CycloidalKinematicsCalculator类**

```python
class CycloidalKinematicsCalculator:
    """循环翼运动学计算器"""
    
    def __init__(self, rotor_config: dict):
        """
        初始化运动学计算器
        
        Parameters:
        -----------
        rotor_config : dict
            转子配置参数
        """
        
    def compute_pitch_angle(self, azimuth: float, time: float) -> float:
        """
        计算俯仰角
        
        Parameters:
        -----------
        azimuth : float
            方位角 (rad)
        time : float
            时间 (s)
            
        Returns:
        --------
        float
            俯仰角 (rad)
        """
        
    def compute_inflow_angle(self, radial_position: float, 
                           azimuth: float, operating_conditions: dict) -> float:
        """
        计算入流角
        
        Parameters:
        -----------
        radial_position : float
            径向位置 (无量纲, r/R)
        azimuth : float
            方位角 (rad)
        operating_conditions : dict
            运行条件
            
        Returns:
        --------
        float
            入流角 (rad)
        """
```

---

## 🌪️ **空气动力学模块 (core.aerodynamics)**

### **BEMTSolver类**

```python
class BEMTSolver:
    """叶素动量理论求解器"""
    
    def __init__(self, config: dict = None):
        """
        初始化BEMT求解器
        
        Parameters:
        -----------
        config : dict, optional
            求解器配置参数
        """
        
    def initialize_solver(self, rotor_geometry: RotorGeometry, 
                         operating_conditions: dict):
        """
        初始化求解器
        
        Parameters:
        -----------
        rotor_geometry : RotorGeometry
            转子几何对象
        operating_conditions : dict
            运行条件
        """
        
    def solve_timestep(self, time: float, boundary_conditions: dict) -> dict:
        """
        求解单个时间步
        
        Parameters:
        -----------
        time : float
            当前时间 (s)
        boundary_conditions : dict
            边界条件
            
        Returns:
        --------
        dict
            求解结果，包含：
            - thrust_distribution: 推力分布
            - power_distribution: 功率分布
            - inflow_distribution: 入流分布
            - convergence_info: 收敛信息
        """
        
    def get_performance_coefficients(self) -> dict:
        """
        获取性能系数
        
        Returns:
        --------
        dict
            性能系数，包含：
            - CT: 推力系数
            - CP: 功率系数
            - efficiency: 效率
        """
        
    def set_airfoil_data(self, airfoil_data: dict):
        """
        设置翼型数据
        
        Parameters:
        -----------
        airfoil_data : dict
            翼型气动数据，包含：
            - alpha: 攻角数组 (rad)
            - cl: 升力系数数组
            - cd: 阻力系数数组
            - cm: 力矩系数数组
        """
```

### **UVLMSolver类**

```python
class UVLMSolver:
    """非定常涡格法求解器"""
    
    def __init__(self, config: dict = None):
        """
        初始化UVLM求解器
        
        Parameters:
        -----------
        config : dict, optional
            求解器配置参数
        """
        
    def initialize_solver(self, rotor_geometry: RotorGeometry, 
                         mesh_config: dict):
        """
        初始化求解器和网格
        
        Parameters:
        -----------
        rotor_geometry : RotorGeometry
            转子几何对象
        mesh_config : dict
            网格配置参数
        """
        
    def solve_timestep(self, time: float, boundary_conditions: dict) -> dict:
        """
        求解单个时间步
        
        Parameters:
        -----------
        time : float
            当前时间 (s)
        boundary_conditions : dict
            边界条件
            
        Returns:
        --------
        dict
            求解结果，包含：
            - pressure_distribution: 压力分布
            - velocity_field: 速度场
            - vorticity_field: 涡量场
            - wake_geometry: 尾迹几何
        """
        
    def update_wake(self, dt: float):
        """
        更新尾迹位置
        
        Parameters:
        -----------
        dt : float
            时间步长 (s)
        """
        
    def compute_forces(self) -> dict:
        """
        计算气动力
        
        Returns:
        --------
        dict
            气动力结果，包含：
            - force_distribution: 力分布
            - moment_distribution: 力矩分布
            - total_force: 总力
            - total_moment: 总力矩
        """
```

---

## 🔊 **声学模块 (core.acoustics)**

### **FWHSolver类**

```python
class FWHSolver:
    """Ffowcs Williams-Hawkings声学求解器"""
    
    def __init__(self, config: dict = None):
        """
        初始化FW-H求解器
        
        Parameters:
        -----------
        config : dict, optional
            求解器配置参数
        """
        
    def initialize_solver(self, rotor_geometry: RotorGeometry, 
                         observer_positions: np.ndarray):
        """
        初始化声学求解器
        
        Parameters:
        -----------
        rotor_geometry : RotorGeometry
            转子几何对象
        observer_positions : np.ndarray
            观测点位置 [N_obs, 3]
        """
        
    def compute_acoustic_pressure(self, surface_data: dict, 
                                 time_range: np.ndarray) -> dict:
        """
        计算声压
        
        Parameters:
        -----------
        surface_data : dict
            表面数据，包含：
            - surface_pressure: 表面压力
            - surface_velocity: 表面速度
            - surface_geometry: 表面几何
        time_range : np.ndarray
            时间范围
            
        Returns:
        --------
        dict
            声学结果，包含：
            - acoustic_pressure: 声压时间历程
            - sound_pressure_level: 声压级
            - frequency_spectrum: 频谱
            - directivity: 指向性
        """
        
    def compute_thickness_noise(self, surface_data: dict) -> np.ndarray:
        """
        计算厚度噪声
        
        Parameters:
        -----------
        surface_data : dict
            表面数据
            
        Returns:
        --------
        np.ndarray
            厚度噪声贡献
        """
        
    def compute_loading_noise(self, surface_data: dict) -> np.ndarray:
        """
        计算载荷噪声
        
        Parameters:
        -----------
        surface_data : dict
            表面数据
            
        Returns:
        --------
        np.ndarray
            载荷噪声贡献
        """
```

---

## 📊 **后处理模块 (core.postprocessing)**

### **PostProcessor类**

```python
class PostProcessor:
    """后处理器"""
    
    def __init__(self, config: dict = None):
        """
        初始化后处理器
        
        Parameters:
        -----------
        config : dict, optional
            后处理配置
        """
        
    def load_results(self, filepath: str) -> dict:
        """
        加载仿真结果
        
        Parameters:
        -----------
        filepath : str
            结果文件路径
            
        Returns:
        --------
        dict
            仿真结果数据
        """
        
    def compute_performance_metrics(self, results: dict) -> dict:
        """
        计算性能指标
        
        Parameters:
        -----------
        results : dict
            仿真结果
            
        Returns:
        --------
        dict
            性能指标，包含：
            - thrust_coefficient: 推力系数
            - power_coefficient: 功率系数
            - efficiency: 效率
            - figure_of_merit: 品质因数
        """
        
    def generate_plots(self, results: dict, plot_config: dict = None) -> list:
        """
        生成图表
        
        Parameters:
        -----------
        results : dict
            仿真结果
        plot_config : dict, optional
            绘图配置
            
        Returns:
        --------
        list
            图表对象列表
        """
```

### **PlotManager类**

```python
class PlotManager:
    """绘图管理器"""
    
    def __init__(self, backend: str = 'matplotlib'):
        """
        初始化绘图管理器
        
        Parameters:
        -----------
        backend : str
            绘图后端 ('matplotlib', 'plotly', 'bokeh')
        """
        
    def plot_radial_distribution(self, r_positions: np.ndarray, 
                                data: np.ndarray, **kwargs) -> object:
        """
        绘制径向分布
        
        Parameters:
        -----------
        r_positions : np.ndarray
            径向位置
        data : np.ndarray
            数据
        **kwargs
            绘图参数
            
        Returns:
        --------
        object
            图表对象
        """
        
    def plot_azimuthal_variation(self, azimuth: np.ndarray, 
                                data: np.ndarray, **kwargs) -> object:
        """
        绘制周向变化
        
        Parameters:
        -----------
        azimuth : np.ndarray
            方位角
        data : np.ndarray
            数据
        **kwargs
            绘图参数
            
        Returns:
        --------
        object
            图表对象
        """
        
    def plot_3d_visualization(self, geometry_data: dict, 
                             field_data: dict = None, **kwargs) -> object:
        """
        绘制3D可视化
        
        Parameters:
        -----------
        geometry_data : dict
            几何数据
        field_data : dict, optional
            场数据
        **kwargs
            绘图参数
            
        Returns:
        --------
        object
            3D图表对象
        """
```

---

## ⚙️ **配置管理 (config)**

### **ConfigManager类**

```python
class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Parameters:
        -----------
        config_file : str, optional
            配置文件路径
        """
        
    def load_config(self, filepath: str) -> dict:
        """
        加载配置文件
        
        Parameters:
        -----------
        filepath : str
            配置文件路径
            
        Returns:
        --------
        dict
            配置数据
        """
        
    def validate_config(self, config: dict) -> bool:
        """
        验证配置有效性
        
        Parameters:
        -----------
        config : dict
            配置数据
            
        Returns:
        --------
        bool
            验证结果
        """
        
    def get_default_config(self, solver_type: str) -> dict:
        """
        获取默认配置
        
        Parameters:
        -----------
        solver_type : str
            求解器类型 ('bemt', 'uvlm', 'fwh')
            
        Returns:
        --------
        dict
            默认配置
        """
```

---

## 🔧 **使用示例**

### **基本使用流程**

```python
import numpy as np
from cycloidal_rotor_suite.core.geometry import RotorGeometry
from cycloidal_rotor_suite.core.aerodynamics import BEMTSolver
from cycloidal_rotor_suite.core.postprocessing import PostProcessor

# 1. 创建转子几何
rotor = RotorGeometry(radius=1.0, hub_radius=0.1, blade_count=3)
rotor.set_blade_geometry(chord=0.1, span=0.8)

# 2. 设置运行条件
operating_conditions = {
    'rpm': 1000,
    'collective_pitch': np.radians(5.0),
    'cyclic_pitch': np.radians(10.0),
    'forward_speed': 0.0
}

# 3. 创建求解器
solver = BEMTSolver()
solver.initialize_solver(rotor, operating_conditions)

# 4. 运行仿真
results = solver.solve_timestep(0.0, operating_conditions)

# 5. 后处理
post_processor = PostProcessor()
performance = post_processor.compute_performance_metrics(results)

print(f"推力系数: {performance['thrust_coefficient']:.4f}")
print(f"功率系数: {performance['power_coefficient']:.4f}")
print(f"效率: {performance['efficiency']:.4f}")
```

### **高级使用示例**

```python
# 参数扫描示例
import matplotlib.pyplot as plt

rpms = np.linspace(800, 1200, 21)
ct_values = []
cp_values = []

for rpm in rpms:
    operating_conditions['rpm'] = rpm
    solver.initialize_solver(rotor, operating_conditions)
    results = solver.solve_timestep(0.0, operating_conditions)
    performance = post_processor.compute_performance_metrics(results)
    
    ct_values.append(performance['thrust_coefficient'])
    cp_values.append(performance['power_coefficient'])

# 绘制性能曲线
plt.figure(figsize=(10, 6))
plt.subplot(1, 2, 1)
plt.plot(rpms, ct_values, 'b-o')
plt.xlabel('转速 (RPM)')
plt.ylabel('推力系数 CT')
plt.grid(True)

plt.subplot(1, 2, 2)
plt.plot(rpms, cp_values, 'r-o')
plt.xlabel('转速 (RPM)')
plt.ylabel('功率系数 CP')
plt.grid(True)

plt.tight_layout()
plt.show()
```

---

## 📚 **相关文档**

- **安装指南**: [`installation_guide.md`](../user_guide/installation_guide.md)
- **快速入门**: [`quick_start.md`](../user_guide/quick_start.md)
- **理论基础**: [`../methodology/`](../methodology/)
- **示例代码**: [`../../examples/`](../../examples/)

---

**API文档版本**: v2.0  
**最后更新**: 2025-01-08  
**API稳定性**: 稳定版
