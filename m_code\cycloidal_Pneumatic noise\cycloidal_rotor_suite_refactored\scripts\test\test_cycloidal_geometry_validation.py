"""
循环翼几何深度重构验证测试
==========================

验证重构版本的循环翼几何计算与原始代码的完全一致性。

测试内容：
1. 循环翼几何计算精度验证
2. 运动学方程数值一致性验证
3. 坐标变换矩阵精确性验证
4. 物理约束和边界条件验证
5. 数值稳定性和性能验证

基于原始 cycloidal_rotor_suite 项目的验证标准。

作者: Augment Agent
日期: 2025-08-03
"""

import numpy as np
import time
from typing import Dict, List, Tuple
import warnings

# 导入重构版本的几何模块
from core.geometry.rotor_geometry import RotorGeometry, create_cycloidal_rotor
from core.geometry.cycloidal_kinematics import CycloidalKinematicsCalculator, CycloidalKinematicsState
from core.geometry.conventional_blade_geometry import ConventionalBladeGeometry, BladeGeometryConfig
from core.geometry.coning_angle_calculator import ConingAngleCalculator, ConingAngleConfig


class CycloidalGeometryValidator:
    """循环翼几何验证器 - 完全对比原始算法"""

    def __init__(self):
        """初始化验证器"""
        self.test_results = {}
        self.performance_metrics = {}
        
        # 创建测试几何
        self.rotor_geometry = create_cycloidal_rotor(
            radius=1.0,
            blade_count=4,
            hub_radius=0.05
        )
        
        # 创建循环翼运动学计算器
        self.kinematics_config = {
            "rotor_radius": 1.0,
            "number_of_blades": 4,
            "blade_chord": 0.08,
            "blade_span": 0.5,
            "angular_velocity": 15.0,  # rad/s
            "cycloidal_pitch_parameters": {
                "pitch_amplitude_max": 30.0,
                "pitch_phase_offset": 0.0,
                "enable_asymmetric_pitch": True,
                "pitch_amplitude_top": 35.0,
                "pitch_amplitude_bottom": 15.0,
                "enable_harmonic_pitch": False,
                "harmonic_coefficients": []
            }
        }
        
        self.kinematics_calc = CycloidalKinematicsCalculator(self.kinematics_config)
        
        print("🔧 循环翼几何验证器初始化完成")

    def test_cycloidal_position_accuracy(self) -> bool:
        """测试循环翼位置计算精度"""
        print("\n📐 测试循环翼位置计算精度...")
        
        try:
            # 测试参数集
            test_cases = [
                (0.0, 0.0, 0.0),           # 零参数
                (np.pi/4, np.pi/6, 0.3),   # 标准参数
                (np.pi/2, np.pi/3, 0.5),   # 中等参数
                (np.pi, np.pi/2, 0.8),     # 大参数
                (3*np.pi/2, 2*np.pi/3, 0.9), # 极限参数
            ]
            
            max_error = 0.0
            for azimuth, phase, eccentricity in test_cases:
                # 计算位置
                position = self.rotor_geometry.compute_cycloidal_blade_position_original(
                    azimuth, phase, eccentricity
                )
                
                # 验证摆线运动学方程
                R = self.rotor_geometry.radius
                x_expected = R * (np.cos(azimuth) + eccentricity * np.cos(azimuth + phase))
                y_expected = R * (np.sin(azimuth) + eccentricity * np.sin(azimuth + phase))
                
                error_x = abs(position[0] - x_expected)
                error_y = abs(position[1] - y_expected)
                total_error = np.sqrt(error_x**2 + error_y**2)
                
                max_error = max(max_error, total_error)
                
                # 验证物理合理性
                position_magnitude = np.linalg.norm(position[:2])
                expected_max = R * (1 + eccentricity)
                
                if position_magnitude > expected_max * 1.01:  # 1%容差
                    print(f"❌ 位置超出物理范围: {position_magnitude:.6f} > {expected_max:.6f}")
                    return False
            
            # 精度要求：误差 < 1e-12（机器精度）
            accuracy_passed = max_error < 1e-12
            
            print(f"   最大位置误差: {max_error:.2e}")
            print(f"   精度测试: {'✅ 通过' if accuracy_passed else '❌ 失败'}")
            
            return accuracy_passed
            
        except Exception as e:
            print(f"❌ 位置计算测试失败: {e}")
            return False

    def test_velocity_consistency(self) -> bool:
        """测试速度计算一致性"""
        print("\n🚀 测试速度计算一致性...")
        
        try:
            # 测试参数
            azimuth = np.pi / 3
            omega = 10.0
            phase = np.pi / 4
            eccentricity = 0.4
            
            # 解析速度
            velocity_analytical = self.rotor_geometry.compute_cycloidal_blade_velocity_original(
                azimuth, omega, phase, eccentricity
            )
            
            # 数值微分验证
            dt = 1e-8
            pos_t1 = self.rotor_geometry.compute_cycloidal_blade_position_original(
                azimuth, phase, eccentricity
            )
            pos_t2 = self.rotor_geometry.compute_cycloidal_blade_position_original(
                azimuth + omega * dt, phase, eccentricity
            )
            
            velocity_numerical = (pos_t2 - pos_t1) / dt
            
            # 计算误差
            velocity_error = np.linalg.norm(velocity_analytical[:2] - velocity_numerical[:2])
            velocity_magnitude = np.linalg.norm(velocity_analytical[:2])
            relative_error = velocity_error / max(velocity_magnitude, 1e-12)
            
            # 一致性要求：相对误差 < 1e-6
            consistency_passed = relative_error < 1e-6
            
            print(f"   解析速度: [{velocity_analytical[0]:.6f}, {velocity_analytical[1]:.6f}]")
            print(f"   数值速度: [{velocity_numerical[0]:.6f}, {velocity_numerical[1]:.6f}]")
            print(f"   相对误差: {relative_error:.2e}")
            print(f"   一致性测试: {'✅ 通过' if consistency_passed else '❌ 失败'}")
            
            return consistency_passed
            
        except Exception as e:
            print(f"❌ 速度一致性测试失败: {e}")
            return False

    def test_acceleration_consistency(self) -> bool:
        """测试加速度计算一致性"""
        print("\n⚡ 测试加速度计算一致性...")
        
        try:
            # 测试参数
            azimuth = np.pi / 6
            omega = 8.0
            phase = np.pi / 3
            eccentricity = 0.6
            
            # 解析加速度
            acceleration_analytical = self.rotor_geometry.compute_cycloidal_blade_acceleration_original(
                azimuth, omega, phase, eccentricity
            )
            
            # 数值微分验证
            dt = 1e-8
            vel_t1 = self.rotor_geometry.compute_cycloidal_blade_velocity_original(
                azimuth, omega, phase, eccentricity
            )
            vel_t2 = self.rotor_geometry.compute_cycloidal_blade_velocity_original(
                azimuth + omega * dt, omega, phase, eccentricity
            )
            
            acceleration_numerical = (vel_t2 - vel_t1) / dt
            
            # 计算误差
            acceleration_error = np.linalg.norm(acceleration_analytical[:2] - acceleration_numerical[:2])
            acceleration_magnitude = np.linalg.norm(acceleration_analytical[:2])
            relative_error = acceleration_error / max(acceleration_magnitude, 1e-12)
            
            # 一致性要求：相对误差 < 1e-5
            consistency_passed = relative_error < 1e-5
            
            print(f"   解析加速度: [{acceleration_analytical[0]:.6f}, {acceleration_analytical[1]:.6f}]")
            print(f"   数值加速度: [{acceleration_numerical[0]:.6f}, {acceleration_numerical[1]:.6f}]")
            print(f"   相对误差: {relative_error:.2e}")
            print(f"   一致性测试: {'✅ 通过' if consistency_passed else '❌ 失败'}")
            
            return consistency_passed
            
        except Exception as e:
            print(f"❌ 加速度一致性测试失败: {e}")
            return False

    def test_kinematics_integration(self) -> bool:
        """测试运动学集成计算"""
        print("\n🔄 测试运动学集成计算...")
        
        try:
            # 测试时间序列
            time_points = np.linspace(0, 1.0, 10)  # 1秒，10个点
            angular_velocity = 15.0  # rad/s
            
            all_states = []
            for t in time_points:
                for blade_idx in range(4):  # 4个桨叶
                    state = self.kinematics_calc.calculate_blade_kinematics(
                        t, angular_velocity, blade_idx
                    )
                    all_states.append(state)
            
            # 验证状态完整性
            integration_passed = True
            for state in all_states:
                # 检查所有必要的属性
                required_attrs = [
                    'azimuth_angle', 'angular_velocity',
                    'blade_position_global', 'blade_velocity_global', 'blade_acceleration_global',
                    'pitch_angle_instantaneous', 'pitch_rate_instantaneous', 'pitch_acceleration_instantaneous',
                    'rotation_matrix_hub_to_global', 'rotation_matrix_blade_to_hub', 'rotation_matrix_blade_to_global'
                ]
                
                for attr in required_attrs:
                    if not hasattr(state, attr):
                        print(f"❌ 缺少属性: {attr}")
                        integration_passed = False
                        break
                
                # 检查数值有效性
                if (np.any(np.isnan(state.blade_position_global)) or
                    np.any(np.isinf(state.blade_position_global))):
                    print("❌ 位置包含无效数值")
                    integration_passed = False
                    break
                
                if not integration_passed:
                    break
            
            print(f"   计算状态数量: {len(all_states)}")
            print(f"   集成测试: {'✅ 通过' if integration_passed else '❌ 失败'}")
            
            return integration_passed
            
        except Exception as e:
            print(f"❌ 运动学集成测试失败: {e}")
            return False

    def test_transformation_matrices(self) -> bool:
        """测试坐标变换矩阵"""
        print("\n🔄 测试坐标变换矩阵...")
        
        try:
            # 测试参数
            azimuth = np.pi / 4
            pitch = np.pi / 6
            
            # 计算变换矩阵
            state = self.kinematics_calc.calculate_blade_kinematics(0.1, 10.0, 0)
            
            R_hub_to_global = state.rotation_matrix_hub_to_global
            R_blade_to_hub = state.rotation_matrix_blade_to_hub
            R_blade_to_global = state.rotation_matrix_blade_to_global
            
            # 验证矩阵性质
            # 1. 正交性检查
            orthogonal_1 = np.allclose(np.dot(R_hub_to_global, R_hub_to_global.T), np.eye(3))
            orthogonal_2 = np.allclose(np.dot(R_blade_to_hub, R_blade_to_hub.T), np.eye(3))
            orthogonal_3 = np.allclose(np.dot(R_blade_to_global, R_blade_to_global.T), np.eye(3))
            
            # 2. 行列式检查（应该为1）
            det_1 = abs(np.linalg.det(R_hub_to_global) - 1.0) < 1e-12
            det_2 = abs(np.linalg.det(R_blade_to_hub) - 1.0) < 1e-12
            det_3 = abs(np.linalg.det(R_blade_to_global) - 1.0) < 1e-12
            
            # 3. 复合变换检查
            R_composite = np.dot(R_hub_to_global, R_blade_to_hub)
            composite_check = np.allclose(R_composite, R_blade_to_global)
            
            matrix_passed = (orthogonal_1 and orthogonal_2 and orthogonal_3 and
                           det_1 and det_2 and det_3 and composite_check)
            
            print(f"   正交性检查: {'✅' if orthogonal_1 and orthogonal_2 and orthogonal_3 else '❌'}")
            print(f"   行列式检查: {'✅' if det_1 and det_2 and det_3 else '❌'}")
            print(f"   复合变换检查: {'✅' if composite_check else '❌'}")
            print(f"   变换矩阵测试: {'✅ 通过' if matrix_passed else '❌ 失败'}")
            
            return matrix_passed
            
        except Exception as e:
            print(f"❌ 变换矩阵测试失败: {e}")
            return False

    def test_performance_benchmarks(self) -> bool:
        """测试性能基准"""
        print("\n⚡ 测试性能基准...")
        
        try:
            # 性能测试参数
            n_iterations = 1000
            
            # 测试1: 位置计算性能
            start_time = time.time()
            for i in range(n_iterations):
                azimuth = i * 2 * np.pi / n_iterations
                self.rotor_geometry.compute_cycloidal_blade_position_original(
                    azimuth, 0.0, 0.3
                )
            position_time = (time.time() - start_time) * 1000  # ms
            
            # 测试2: 运动学计算性能
            start_time = time.time()
            for i in range(n_iterations):
                t = i * 0.001  # 1ms步长
                self.kinematics_calc.calculate_blade_kinematics(t, 15.0, 0)
            kinematics_time = (time.time() - start_time) * 1000  # ms
            
            # 性能要求
            position_performance = position_time < 100.0  # 100ms for 1000 iterations
            kinematics_performance = kinematics_time < 500.0  # 500ms for 1000 iterations
            
            print(f"   位置计算时间: {position_time:.2f} ms ({n_iterations} 次)")
            print(f"   运动学计算时间: {kinematics_time:.2f} ms ({n_iterations} 次)")
            print(f"   性能测试: {'✅ 通过' if position_performance and kinematics_performance else '❌ 失败'}")
            
            # 保存性能指标
            self.performance_metrics = {
                'position_calc_time_ms': position_time,
                'kinematics_calc_time_ms': kinematics_time,
                'position_calc_per_sec': n_iterations / (position_time / 1000),
                'kinematics_calc_per_sec': n_iterations / (kinematics_time / 1000)
            }
            
            return position_performance and kinematics_performance
            
        except Exception as e:
            print(f"❌ 性能基准测试失败: {e}")
            return False

    def run_comprehensive_validation(self) -> Dict[str, bool]:
        """运行全面验证测试"""
        print("🧪 开始循环翼几何深度重构验证测试")
        print("=" * 60)
        
        # 运行所有测试
        test_methods = [
            ('position_accuracy', self.test_cycloidal_position_accuracy),
            ('velocity_consistency', self.test_velocity_consistency),
            ('acceleration_consistency', self.test_acceleration_consistency),
            ('kinematics_integration', self.test_kinematics_integration),
            ('transformation_matrices', self.test_transformation_matrices),
            ('performance_benchmarks', self.test_performance_benchmarks),
        ]
        
        results = {}
        for test_name, test_method in test_methods:
            try:
                results[test_name] = test_method()
            except Exception as e:
                print(f"❌ 测试 {test_name} 异常: {e}")
                results[test_name] = False
        
        # 运行内置验证
        try:
            builtin_validation = self.rotor_geometry.validate_geometry_calculations_original()
            results.update(builtin_validation)
        except Exception as e:
            print(f"❌ 内置验证失败: {e}")
        
        return results


def main():
    """主验证函数"""
    validator = CycloidalGeometryValidator()
    results = validator.run_comprehensive_validation()
    
    print("\n" + "=" * 60)
    print("🎯 循环翼几何深度重构验证结果汇总")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:30s}: {status}")
        if passed:
            passed_tests += 1
    
    print("-" * 60)
    print(f"总计: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！循环翼几何深度重构完全成功。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
