"""
接口修复验证测试
===============

快速验证接口修复是否成功的简化测试

作者: Augment Agent
日期: 2025-08-03
"""

import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + '/..')

def test_aerodynamics_interfaces():
    """测试气动力学模块接口"""
    print("🌪️ 测试气动力学模块接口...")
    
    try:
        from core.aerodynamics.solvers.bemt_solver import BEMTSolver
        from core.aerodynamics.solvers.uvlm_solver import UVLMSolver
        from core.aerodynamics.solvers.lifting_line_solver import LiftingLineSolver
        
        # 测试BEMT求解器
        bemt_config = {
            'max_iterations': 50,
            'convergence_tolerance': 1e-6,
            'relaxation_factor': 0.5
        }
        bemt_solver = BEMTSolver(bemt_config)
        print("   ✅ BEMT求解器初始化成功")
        
        # 测试UVLM求解器
        uvlm_config = {
            'n_chordwise_panels': 10,
            'n_spanwise_panels': 8,
            'wake_length': 5.0
        }
        uvlm_solver = UVLMSolver(uvlm_config)
        print("   ✅ UVLM求解器初始化成功")
        
        # 测试升力线求解器
        ll_config = {
            'n_elements': 20,
            'max_iterations': 100,
            'convergence_tolerance': 1e-6
        }
        ll_solver = LiftingLineSolver(ll_config)
        print("   ✅ 升力线求解器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 气动力学模块接口测试失败: {e}")
        return False

def test_acoustics_interfaces():
    """测试声学分析模块接口"""
    print("🔊 测试声学分析模块接口...")
    
    try:
        from core.acoustics.solvers.fwh_solver import FWHSolver
        from core.acoustics.bpm_noise_model import BPMNoiseModel
        
        # 测试FW-H求解器
        fwh_config = {
            'sound_speed': 343.0,
            'air_density': 1.225,
            'observer_positions': [[10.0, 0.0, 0.0]],
            'enable_thickness_noise': True,
            'enable_loading_noise': True
        }
        fwh_solver = FWHSolver(fwh_config)
        print("   ✅ FW-H求解器初始化成功")
        
        # 测试BPM噪声模型
        bpm_config = {
            'sound_speed': 343.0,
            'air_density': 1.225,
            'blade_chord': 0.1,
            'bmp_noise_params': {
                'enable_tbl_te_noise': True,
                'enable_separation_noise': True,
                'frequency_min': 10.0,
                'frequency_max': 10000.0
            }
        }
        bpm_model = BPMNoiseModel(bpm_config)
        print("   ✅ BPM噪声模型初始化成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 声学分析模块接口测试失败: {e}")
        return False

def test_physics_interfaces():
    """测试物理模型接口"""
    print("⚛️ 测试物理模型接口...")
    
    try:
        from core.physics.dynamic_stall import LeishmanBeddoesModel
        from core.physics.vortex_models import VortexCoreManager
        
        # 测试动态失速模型（使用chord参数）
        lb_model = LeishmanBeddoesModel(chord=0.1)
        print("   ✅ 动态失速模型初始化成功（chord参数）")
        
        # 测试动态失速模型（使用完整参数）
        airfoil_params = {
            'alpha_0': 0.0,
            'Cl_alpha': 2 * np.pi,
            'alpha_stall': 15.0,
            'Cl_max': 1.5,
            'chord': 0.1
        }
        lb_model2 = LeishmanBeddoesModel(airfoil_params=airfoil_params)
        print("   ✅ 动态失速模型初始化成功（完整参数）")
        
        # 测试涡核模型
        vortex_config = {
            'vortex_core_model': 'vatistas',
            'default_core_radius': 0.01,
            'vatistas_n_parameter': 2.0
        }
        vortex_manager = VortexCoreManager(vortex_config)
        print("   ✅ 涡核模型管理器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 物理模型接口测试失败: {e}")
        return False

def test_numerical_interfaces():
    """测试数值分析模块接口"""
    print("🔢 测试数值分析模块接口...")
    
    try:
        from core.numerical.linear_solvers import create_linear_solver
        
        # 测试线性求解器
        solver_config = {
            'tolerance': 1e-6,
            'max_iterations': 1000,
            'direct_method': 'lu',
            'iterative_method': 'gmres'
        }
        
        direct_solver = create_linear_solver('direct', solver_config)
        print("   ✅ 直接线性求解器创建成功")
        
        iterative_solver = create_linear_solver('iterative', solver_config)
        print("   ✅ 迭代线性求解器创建成功")
        
        adaptive_solver = create_linear_solver('adaptive', solver_config)
        print("   ✅ 自适应线性求解器创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数值分析模块接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 接口修复验证测试")
    print("=" * 50)
    
    # 运行所有接口测试
    test_results = {
        'aerodynamics': test_aerodynamics_interfaces(),
        'acoustics': test_acoustics_interfaces(),
        'physics': test_physics_interfaces(),
        'numerical': test_numerical_interfaces(),
    }
    
    print("\n" + "=" * 50)
    print("🎯 接口修复验证结果汇总")
    print("=" * 50)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for module_name, passed in test_results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{module_name:20s}: {status}")
        if passed:
            passed_tests += 1
    
    print("-" * 50)
    print(f"总计: {passed_tests}/{total_tests} 模块接口修复成功")
    
    if passed_tests == total_tests:
        print("🎉 所有模块接口修复成功！")
        return True
    else:
        print("⚠️  部分模块接口仍需修复。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
