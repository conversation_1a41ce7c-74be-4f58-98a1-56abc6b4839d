"""
误差指标计算器
=============

提供各种误差分析和统计指标计算
"""

import numpy as np
from typing import Dict, Any, List, Tuple
from scipy import stats
import warnings

class MetricsCalculator:
    """
    误差指标计算器
    
    提供完整的误差分析功能，包括：
    - RMSE, MAE, MAPE等基础指标
    - 相关系数和决定系数
    - 统计显著性检验
    - 频域误差分析
    """
    
    def __init__(self):
        """初始化指标计算器"""
        self.supported_metrics = [
            'rmse', 'mae', 'mape', 'r_squared', 'correlation',
            'force_rmse', 'moment_rmse', 'max_error', 'mean_bias'
        ]
    
    def calculate_all_metrics(self, simulation_data: Dict[str, Any],
                            reference_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算所有支持的误差指标
        
        Args:
            simulation_data: 仿真数据
            reference_data: 参考数据
            
        Returns:
            误差指标字典
        """
        metrics = {}
        
        # 提取力和力矩数据
        sim_forces = simulation_data.get('forces', np.array([0.0, 0.0, 0.0]))
        ref_forces = reference_data.get('forces', np.array([0.0, 0.0, 0.0]))
        
        sim_moments = simulation_data.get('moments', np.array([0.0, 0.0, 0.0]))
        ref_moments = reference_data.get('moments', np.array([0.0, 0.0, 0.0]))
        
        # 确保数据格式一致
        sim_forces = np.atleast_1d(sim_forces)
        ref_forces = np.atleast_1d(ref_forces)
        sim_moments = np.atleast_1d(sim_moments)
        ref_moments = np.atleast_1d(ref_moments)
        
        # 基础误差指标
        if len(sim_forces) == len(ref_forces):
            metrics['force_rmse'] = self.calculate_rmse(sim_forces, ref_forces)
            metrics['force_mae'] = self.calculate_mae(sim_forces, ref_forces)
            metrics['force_mape'] = self.calculate_mape(sim_forces, ref_forces)
            metrics['force_correlation'] = self.calculate_correlation(sim_forces, ref_forces)
        
        if len(sim_moments) == len(ref_moments):
            metrics['moment_rmse'] = self.calculate_rmse(sim_moments, ref_moments)
            metrics['moment_mae'] = self.calculate_mae(sim_moments, ref_moments)
            metrics['moment_mape'] = self.calculate_mape(sim_moments, ref_moments)
            metrics['moment_correlation'] = self.calculate_correlation(sim_moments, ref_moments)
        
        # 综合指标
        all_sim = np.concatenate([sim_forces, sim_moments])
        all_ref = np.concatenate([ref_forces, ref_moments])
        
        metrics['rmse'] = self.calculate_rmse(all_sim, all_ref)
        metrics['mae'] = self.calculate_mae(all_sim, all_ref)
        metrics['mape'] = self.calculate_mape(all_sim, all_ref)
        metrics['correlation'] = self.calculate_correlation(all_sim, all_ref)
        metrics['r_squared'] = self.calculate_r_squared(all_sim, all_ref)
        metrics['max_error'] = self.calculate_max_error(all_sim, all_ref)
        metrics['mean_bias'] = self.calculate_mean_bias(all_sim, all_ref)
        
        return metrics
    
    def calculate_rmse(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算均方根误差 (RMSE)"""
        try:
            if len(simulation) != len(reference):
                warnings.warn("数据长度不匹配，使用较短长度")
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            mse = np.mean((simulation - reference) ** 2)
            return np.sqrt(mse)
        except Exception as e:
            warnings.warn(f"RMSE计算失败: {e}")
            return float('inf')
    
    def calculate_mae(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算平均绝对误差 (MAE)"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            return np.mean(np.abs(simulation - reference))
        except Exception as e:
            warnings.warn(f"MAE计算失败: {e}")
            return float('inf')
    
    def calculate_mape(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算平均绝对百分比误差 (MAPE)"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            # 避免除零
            reference_safe = np.where(np.abs(reference) < 1e-10, 1e-10, reference)
            mape = np.mean(np.abs((simulation - reference) / reference_safe)) * 100
            return mape
        except Exception as e:
            warnings.warn(f"MAPE计算失败: {e}")
            return float('inf')
    
    def calculate_correlation(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算皮尔逊相关系数"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            if len(simulation) < 2:
                return 0.0
            
            correlation, _ = stats.pearsonr(simulation, reference)
            return correlation if not np.isnan(correlation) else 0.0
        except Exception as e:
            warnings.warn(f"相关系数计算失败: {e}")
            return 0.0
    
    def calculate_r_squared(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算决定系数 (R²)"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            ss_res = np.sum((reference - simulation) ** 2)
            ss_tot = np.sum((reference - np.mean(reference)) ** 2)
            
            if ss_tot == 0:
                return 1.0 if ss_res == 0 else 0.0
            
            r_squared = 1 - (ss_res / ss_tot)
            return max(0.0, r_squared)  # R²不应为负
        except Exception as e:
            warnings.warn(f"R²计算失败: {e}")
            return 0.0
    
    def calculate_max_error(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算最大绝对误差"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            return np.max(np.abs(simulation - reference))
        except Exception as e:
            warnings.warn(f"最大误差计算失败: {e}")
            return float('inf')
    
    def calculate_mean_bias(self, simulation: np.ndarray, reference: np.ndarray) -> float:
        """计算平均偏差"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            return np.mean(simulation - reference)
        except Exception as e:
            warnings.warn(f"平均偏差计算失败: {e}")
            return float('inf')
    
    def calculate_statistical_significance(self, simulation: np.ndarray, 
                                         reference: np.ndarray) -> Dict[str, float]:
        """计算统计显著性"""
        try:
            if len(simulation) != len(reference):
                min_len = min(len(simulation), len(reference))
                simulation = simulation[:min_len]
                reference = reference[:min_len]
            
            # t检验
            t_stat, p_value = stats.ttest_rel(simulation, reference)
            
            # Wilcoxon符号秩检验（非参数）
            try:
                w_stat, w_p_value = stats.wilcoxon(simulation, reference)
            except:
                w_stat, w_p_value = 0.0, 1.0
            
            return {
                't_statistic': t_stat,
                't_p_value': p_value,
                'wilcoxon_statistic': w_stat,
                'wilcoxon_p_value': w_p_value,
                'significant_at_0.05': p_value < 0.05
            }
        except Exception as e:
            warnings.warn(f"统计显著性计算失败: {e}")
            return {
                't_statistic': 0.0,
                't_p_value': 1.0,
                'wilcoxon_statistic': 0.0,
                'wilcoxon_p_value': 1.0,
                'significant_at_0.05': False
            }
    
    def generate_error_summary(self, metrics: Dict[str, float]) -> str:
        """生成误差摘要报告"""
        summary = "误差分析摘要\n"
        summary += "=" * 30 + "\n"
        
        # 基础指标
        summary += f"RMSE: {metrics.get('rmse', 0.0):.6f}\n"
        summary += f"MAE: {metrics.get('mae', 0.0):.6f}\n"
        summary += f"MAPE: {metrics.get('mape', 0.0):.2f}%\n"
        summary += f"相关系数: {metrics.get('correlation', 0.0):.4f}\n"
        summary += f"R²: {metrics.get('r_squared', 0.0):.4f}\n"
        
        # 分类指标
        summary += "\n分类误差:\n"
        summary += f"力误差 (RMSE): {metrics.get('force_rmse', 0.0):.6f}\n"
        summary += f"力矩误差 (RMSE): {metrics.get('moment_rmse', 0.0):.6f}\n"
        
        # 极值
        summary += f"\n最大误差: {metrics.get('max_error', 0.0):.6f}\n"
        summary += f"平均偏差: {metrics.get('mean_bias', 0.0):.6f}\n"
        
        return summary
