{"summary": {"total_tests": 9, "passed_tests": 4, "failed_tests": 5, "pass_rate": 44.44444444444444, "total_execution_time": 2.532789945602417, "timestamp": "2025-08-05 09:55:18"}, "detailed_results": {"1. 功能完整性验证": {"success": false, "execution_time": 2.5193114280700684, "timestamp": "2025-08-05 09:55:18"}, "2. 性能基准测试": {"success": true, "execution_time": 0.001993417739868164, "timestamp": "2025-08-05 09:55:18"}, "3. 边界条件测试": {"success": false, "execution_time": 0.0, "timestamp": "2025-08-05 09:55:18"}, "4. 接口一致性验证": {"success": false, "execution_time": 0.0010182857513427734, "timestamp": "2025-08-05 09:55:18"}, "5. 文档符合性检查": {"success": true, "execution_time": 0.002012014389038086, "timestamp": "2025-08-05 09:55:18"}, "6. 内存使用分析": {"success": false, "execution_time": 0.0, "timestamp": "2025-08-05 09:55:18"}, "7. 数值精度验证": {"success": "True", "execution_time": 0.0030078887939453125, "timestamp": "2025-08-05 09:55:18"}, "8. 集成工作流测试": {"success": false, "execution_time": 0.0, "timestamp": "2025-08-05 09:55:18"}, "系统稳定性测试": {"success": "True", "execution_time": 0.0019915103912353516, "timestamp": "2025-08-05 09:55:18"}}, "performance_metrics": {"bemt_solver": {"execution_time": 0.0009996891021728516, "operations_per_second": 10003.109945146673}, "memory_usage": 294.21875, "numerical_accuracy": {"rmse": 0.0009623305084699748, "mae": 0.0007652088462883247, "correlation": 0.9999990750588057, "accuracy_pass": "True"}, "stability": {"mean_result": 4.941924952081123, "std_dev": 0.14010122930005023, "coefficient_of_variation": 0.02834952587474065, "is_stable": "True"}}, "overall_status": "FAIL"}