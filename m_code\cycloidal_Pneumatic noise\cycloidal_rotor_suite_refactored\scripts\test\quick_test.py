"""
快速测试 - 检查模块导入和基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

print("🚀 开始快速测试...")

# 测试1: BEMT求解器导入
try:
    from core.aerodynamics.solvers.bemt_solver import BEMTSolver
    print("✅ BEMT求解器导入成功")
except Exception as e:
    print(f"❌ BEMT求解器导入失败: {e}")

# 测试2: UVLM求解器导入
try:
    from core.aerodynamics.solvers.uvlm_solver import UVLMSolver
    print("✅ UVLM求解器导入成功")
except Exception as e:
    print(f"❌ UVLM求解器导入失败: {e}")

# 测试3: 动态失速模型导入
try:
    from core.physics.dynamic_stall import LeishmanBeddoesModel
    print("✅ 动态失速模型导入成功")
except Exception as e:
    print(f"❌ 动态失速模型导入失败: {e}")

# 测试4: 几何模块导入
try:
    from core.geometry.cycloidal_kinematics import CycloidalKinematicsCalculator
    print("✅ 循环翼运动学导入成功")
except Exception as e:
    print(f"❌ 循环翼运动学导入失败: {e}")

# 测试5: 配置管理导入
try:
    from core.config.config_manager import ConfigManager
    print("✅ 配置管理导入成功")
except Exception as e:
    print(f"❌ 配置管理导入失败: {e}")

print("🎉 快速测试完成！")
