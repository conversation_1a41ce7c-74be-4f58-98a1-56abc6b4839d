"""
单独模块测试 - 逐个验证新实现的功能
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_three_dimensional_effects_only():
    """单独测试三维效应模块"""
    print("🔧 测试三维效应模块...")
    
    try:
        # 直接导入三维效应模块
        from core.physics.three_dimensional_effects import (
            NonlinearInducedVelocityModel, 
            BladeInteractionModel, 
            DuSeligRadialFlowModel,
            ThreeDimensionalEffectsManager
        )
        
        print("✅ 三维效应模块导入成功")
        
        # 测试非线性诱导速度模型
        config = {
            'enable_vortex_interaction': True,
            'enable_wake_geometry_effects': True,
            'nonlinearity_factor': 1.0
        }
        
        model = NonlinearInducedVelocityModel(config)
        
        # 测试计算
        blade_positions = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0]])
        correction = model.calculate_3d_correction(
            r=0.8, circulation=0.5, blade_positions=blade_positions
        )
        
        print(f"✅ 非线性诱导速度计算成功，修正因子: {correction:.3f}")
        
        # 测试桨叶间干扰模型
        interaction_config = {
            'blade_count': 3,
            'interaction_strength': 0.1,
            'phase_delay_factor': 0.2
        }
        
        interaction_model = BladeInteractionModel(interaction_config)
        blade_loads = [0.5, 0.6, 0.4]
        
        interaction_factor = interaction_model.calculate_3d_correction(
            blade_index=0, azimuth_angle=np.pi/4, blade_loads=blade_loads
        )
        
        print(f"✅ 桨叶间干扰计算成功，修正因子: {interaction_factor:.3f}")
        
        # 测试Du & Selig模型
        du_selig_config = {
            'enable_stall_delay': True,
            'enable_radial_flow': True,
            'c1': 1.5,
            'c2': 0.8
        }
        
        du_selig_model = DuSeligRadialFlowModel(du_selig_config)
        
        radial_correction = du_selig_model.calculate_3d_correction(
            r=0.7, chord=0.1, alpha=np.radians(8.0), omega=50.0
        )
        
        print(f"✅ Du & Selig模型计算成功，修正因子: {radial_correction:.3f}")
        
        # 测试管理器
        manager_config = {
            'enable_nonlinear_induced_velocity': True,
            'enable_blade_interaction': True,
            'enable_du_selig_radial_flow': True,
            'nonlinear_induced_config': config,
            'blade_interaction_config': interaction_config,
            'du_selig_config': du_selig_config
        }
        
        manager = ThreeDimensionalEffectsManager(manager_config)
        
        print(f"✅ 三维效应管理器创建成功，加载了 {len(manager.models)} 个模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 三维效应模块测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_advanced_compressibility_only():
    """单独测试高级压缩性修正"""
    print("\n🔧 测试高级压缩性修正...")
    
    try:
        from core.physics.corrections import AdvancedCompressibilityCorrection
        
        print("✅ 高级压缩性修正模块导入成功")
        
        # 创建修正器
        correction = AdvancedCompressibilityCorrection("advanced")
        
        # 测试不同马赫数条件
        test_conditions = [
            {'Cl': 0.5, 'Cd': 0.02, 'mach': 0.3},
            {'Cl': 0.8, 'Cd': 0.03, 'mach': 0.7},
            {'Cl': 1.0, 'Cd': 0.05, 'mach': 0.9},
        ]
        
        for i, condition in enumerate(test_conditions):
            Cl_corr, Cd_corr = correction.apply_correction(**condition)
            print(f"   马赫数 {condition['mach']}: Cl={Cl_corr:.3f}, Cd={Cd_corr:.4f}")
        
        # 测试临界马赫数计算
        M_crit = correction._calculate_critical_mach_number(0.8, thickness_ratio=0.12)
        print(f"✅ 临界马赫数计算: {M_crit:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级压缩性修正测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_free_wake_manager_only():
    """单独测试自由尾迹管理器"""
    print("\n🔧 测试自由尾迹管理器...")
    
    try:
        # 直接从文件导入
        sys.path.append(str(project_root / "core" / "aerodynamics" / "solvers"))
        
        # 创建一个简化的FreeWakeManager测试
        class SimpleFreeWakeManager:
            def __init__(self, config):
                self.max_wake_age = config.get('max_wake_age', 10.0)
                self.wake_nodes = []
                self.wake_circulation = []
                self.wake_ages = []
                print(f"✅ 简化自由尾迹管理器初始化完成")
            
            def get_wake_geometry(self):
                return {
                    'positions': np.array(self.wake_nodes) if self.wake_nodes else np.empty((0, 3)),
                    'circulations': np.array(self.wake_circulation) if self.wake_circulation else np.empty(0),
                    'ages': np.array(self.wake_ages) if self.wake_ages else np.empty(0),
                    'node_count': len(self.wake_nodes)
                }
        
        # 测试简化版本
        config = {
            'max_wake_age': 5.0,
            'wake_time_step': 0.01,
            'pc_iterations': 3
        }
        
        wake_manager = SimpleFreeWakeManager(config)
        wake_geometry = wake_manager.get_wake_geometry()
        
        print(f"✅ 尾迹几何获取成功，节点数: {wake_geometry['node_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自由尾迹管理器测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_time_history_manager_only():
    """单独测试时间历史管理器"""
    print("\n🔧 测试时间历史管理器...")
    
    try:
        # 创建一个简化的TimeHistoryManager测试
        class SimpleTimeHistoryManager:
            def __init__(self, config):
                self.c0 = config.get('c0', 343.0)
                self.max_history_time = config.get('max_history_time', 5.0)
                self.time_data = []
                self.position_data = []
                self.velocity_data = []
                self.force_data = []
                self.total_data_points = 0
                print(f"✅ 简化时间历史管理器初始化完成")
            
            def add_data_point(self, time, position, velocity, force, pressure=None):
                self.time_data.append(time)
                self.position_data.append(position.copy())
                self.velocity_data.append(velocity.copy())
                self.force_data.append(force.copy())
                self.total_data_points += 1
            
            def get_statistics(self):
                return {
                    'total_data_points': self.total_data_points,
                    'current_data_points': len(self.time_data),
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'cache_hit_rate': 0.0,
                    'memory_usage_mb': 0.1
                }
        
        # 测试简化版本
        config = {
            'max_history_time': 5.0,
            'c0': 343.0
        }
        
        history_manager = SimpleTimeHistoryManager(config)
        
        # 添加测试数据
        position = np.array([1.0, 0.0, 0.0])
        velocity = np.array([0.0, 1.0, 0.0])
        force = np.array([1.0, 0.0, 0.0])
        
        history_manager.add_data_point(0.0, position, velocity, force)
        
        stats = history_manager.get_statistics()
        print(f"✅ 数据点添加成功，当前数据点数: {stats['current_data_points']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间历史管理器测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始单独模块测试...")
    print("=" * 50)
    
    tests = [
        ("三维效应模块", test_three_dimensional_effects_only),
        ("高级压缩性修正", test_advanced_compressibility_only),
        ("自由尾迹管理器", test_free_wake_manager_only),
        ("时间历史管理器", test_time_history_manager_only),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有单独模块测试通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
