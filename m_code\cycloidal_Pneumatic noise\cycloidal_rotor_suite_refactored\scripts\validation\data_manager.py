"""
实验数据管理器
=============

管理实验数据和参考数据
"""

import numpy as np
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path
import json
import warnings
import pandas as pd
import h5py
import yaml
from scipy import interpolate
from scipy.io import loadmat
import csv

class ExperimentalDataManager:
    """
    实验数据管理器
    
    提供实验数据和参考数据的管理功能，包括：
    - 标准参考数据加载
    - 实验数据格式化
    - 数据插值和处理
    - 数据验证和清理
    """
    
    def __init__(self, data_directory: Optional[str] = None):
        """
        初始化数据管理器
        
        Args:
            data_directory: 数据目录路径
        """
        self.data_directory = Path(data_directory) if data_directory else Path('./validation_data')
        self.data_directory.mkdir(exist_ok=True)
        
        # 内置参考数据
        self.reference_data = {}
        self._initialize_reference_data()
        
        # 支持的数据格式
        self.supported_formats = {
            '.csv': self._load_csv,
            '.json': self._load_json,
            '.yaml': self._load_yaml,
            '.yml': self._load_yaml,
            '.h5': self._load_hdf5,
            '.hdf5': self._load_hdf5,
            '.mat': self._load_matlab,
            '.xlsx': self._load_excel,
            '.xls': self._load_excel,
            '.txt': self._load_text
        }

        # 数据缓存
        self.data_cache = {}

        print(f"✅ 实验数据管理器初始化完成")
        print(f"   数据目录: {self.data_directory}")
        print(f"   支持格式: {list(self.supported_formats.keys())}")
    
    def _initialize_reference_data(self):
        """初始化内置参考数据"""
        
        # BEMT基础验证参考数据
        self.reference_data['bemt_basic'] = {
            'forces': np.array([150.0, 10.0, 200.0]),  # [推力, 侧力, 升力]
            'moments': np.array([25.0, 5.0, 2.0]),     # [扭矩, 俯仰力矩, 滚转力矩]
            'performance': {
                'thrust_coefficient': 0.008,
                'power_coefficient': 0.0012,
                'figure_of_merit': 0.75
            },
            'convergence': {
                'iterations': 45,
                'final_residual': 8e-7
            },
            'source': 'analytical_solution',
            'uncertainty': 0.05  # 5% 不确定度
        }
        
        # 循环翼转子参考数据
        self.reference_data['cycloidal_basic'] = {
            'forces': np.array([80.0, 15.0, 85.0]),
            'moments': np.array([12.0, 3.0, 1.5]),
            'performance': {
                'mean_thrust': 80.0,
                'thrust_variation': 18.0,
                'power_coefficient': 0.85,
                'efficiency': 0.72
            },
            'time_series': {
                'time': np.linspace(0, 0.1, 100),
                'thrust_history': 80.0 + 18.0 * np.sin(2 * np.pi * 20 * np.linspace(0, 0.1, 100)),
                'torque_history': 12.0 + 3.0 * np.sin(2 * np.pi * 20 * np.linspace(0, 0.1, 100))
            },
            'source': 'experimental_data',
            'uncertainty': 0.08
        }
        
        # UVLM验证参考数据
        self.reference_data['uvlm_validation'] = {
            'forces': np.array([180.0, 8.0, 190.0]),
            'moments': np.array([30.0, 4.0, 2.5]),
            'flow_field': {
                'induced_velocity_ratio': 0.15,
                'wake_contraction': 0.82,
                'tip_vortex_strength': 2.3
            },
            'pressure_distribution': {
                'radial_positions': np.linspace(0.2, 1.0, 10),
                'pressure_coefficients': np.array([-0.8, -0.6, -0.4, -0.3, -0.2, -0.15, -0.1, -0.08, -0.06, -0.04])
            },
            'source': 'cfd_simulation',
            'uncertainty': 0.06
        }
        
        # 声学验证参考数据
        self.reference_data['acoustic_validation'] = {
            'forces': np.array([120.0, 5.0, 125.0]),
            'moments': np.array([20.0, 2.0, 1.0]),
            'acoustic': {
                'bpf_spl': 83.5,
                'broadband_spl': 68.2,
                'peak_frequency': 295.0,
                'directivity_pattern': 'dipole'
            },
            'frequency_spectrum': {
                'frequencies': np.logspace(1, 3, 50),  # 10 Hz to 1000 Hz
                'spl_levels': 85.0 - 20 * np.log10(np.logspace(1, 3, 50) / 300.0)  # 简化频谱
            },
            'source': 'experimental_measurement',
            'uncertainty': 0.10
        }
        
        # 收敛性测试参考数据
        self.reference_data['convergence_test'] = {
            'forces': np.array([95.0, 6.0, 100.0]),
            'moments': np.array([15.0, 2.5, 1.2]),
            'convergence': {
                'iterations': 75,
                'final_residual': 5e-9,
                'convergence_rate': 0.08,
                'solution_stability': True
            },
            'residual_history': np.logspace(-1, -8, 75),  # 收敛历史
            'source': 'numerical_benchmark',
            'uncertainty': 0.02
        }
        
        # 极限工况参考数据
        self.reference_data['extreme_conditions'] = {
            'forces': np.array([200.0, 25.0, 210.0]),
            'moments': np.array([40.0, 8.0, 4.0]),
            'stability': {
                'solution_convergence': True,
                'physical_validity': True,
                'numerical_stability': True,
                'performance_degradation': 0.15  # 15% 性能下降
            },
            'extreme_metrics': {
                'max_local_mach': 0.85,
                'stall_fraction': 0.25,
                'separation_onset': 0.7  # 70% 半径处开始分离
            },
            'source': 'high_fidelity_simulation',
            'uncertainty': 0.12
        }
        
        print(f"   内置参考数据: {len(self.reference_data)} 个测试用例")
    
    def get_reference_data(self, test_case_name: str) -> Dict[str, Any]:
        """
        获取指定测试用例的参考数据
        
        Args:
            test_case_name: 测试用例名称
            
        Returns:
            参考数据字典
        """
        if test_case_name in self.reference_data:
            return self.reference_data[test_case_name].copy()
        
        # 尝试从文件加载
        data_file = self.data_directory / f"{test_case_name}.json"
        if data_file.exists():
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换numpy数组
                    data = self._convert_lists_to_arrays(data)
                    return data
            except Exception as e:
                warnings.warn(f"加载参考数据失败 {test_case_name}: {e}")
        
        # 返回默认数据
        warnings.warn(f"未找到参考数据 {test_case_name}，使用默认数据")
        return self._get_default_reference_data()
    
    def _convert_lists_to_arrays(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """递归转换列表为numpy数组"""
        converted = {}
        for key, value in data.items():
            if isinstance(value, list):
                converted[key] = np.array(value)
            elif isinstance(value, dict):
                converted[key] = self._convert_lists_to_arrays(value)
            else:
                converted[key] = value
        return converted
    
    def _get_default_reference_data(self) -> Dict[str, Any]:
        """获取默认参考数据"""
        return {
            'forces': np.array([100.0, 5.0, 105.0]),
            'moments': np.array([15.0, 2.0, 1.0]),
            'performance': {
                'thrust_coefficient': 0.006,
                'power_coefficient': 0.001
            },
            'source': 'default_values',
            'uncertainty': 0.20
        }
    
    def save_reference_data(self, test_case_name: str, data: Dict[str, Any]) -> None:
        """
        保存参考数据到文件
        
        Args:
            test_case_name: 测试用例名称
            data: 参考数据
        """
        data_file = self.data_directory / f"{test_case_name}.json"
        
        # 转换numpy数组为列表
        serializable_data = self._convert_arrays_to_lists(data)
        
        try:
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 参考数据已保存: {data_file}")
        except Exception as e:
            warnings.warn(f"保存参考数据失败: {e}")
    
    def _convert_arrays_to_lists(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """递归转换numpy数组为列表"""
        converted = {}
        for key, value in data.items():
            if isinstance(value, np.ndarray):
                converted[key] = value.tolist()
            elif isinstance(value, dict):
                converted[key] = self._convert_arrays_to_lists(value)
            else:
                converted[key] = value
        return converted
    
    def validate_reference_data(self, test_case_name: str) -> bool:
        """
        验证参考数据的完整性和有效性
        
        Args:
            test_case_name: 测试用例名称
            
        Returns:
            验证结果
        """
        try:
            data = self.get_reference_data(test_case_name)
            
            # 检查必需字段
            required_fields = ['forces', 'moments']
            for field in required_fields:
                if field not in data:
                    warnings.warn(f"缺少必需字段: {field}")
                    return False
            
            # 检查数据类型和形状
            forces = data['forces']
            moments = data['moments']
            
            if not isinstance(forces, np.ndarray) or forces.shape != (3,):
                warnings.warn("力数据格式错误")
                return False
            
            if not isinstance(moments, np.ndarray) or moments.shape != (3,):
                warnings.warn("力矩数据格式错误")
                return False
            
            # 检查数值合理性
            if np.any(np.isnan(forces)) or np.any(np.isnan(moments)):
                warnings.warn("数据包含NaN值")
                return False
            
            return True
            
        except Exception as e:
            warnings.warn(f"参考数据验证失败: {e}")
            return False
    
    def get_available_datasets(self) -> List[str]:
        """获取所有可用的数据集"""
        datasets = list(self.reference_data.keys())
        
        # 添加文件中的数据集
        for data_file in self.data_directory.glob("*.json"):
            dataset_name = data_file.stem
            if dataset_name not in datasets:
                datasets.append(dataset_name)
        
        return sorted(datasets)
    
    def get_data_summary(self) -> str:
        """获取数据摘要"""
        summary = "实验数据摘要\n"
        summary += "=" * 30 + "\n"
        
        datasets = self.get_available_datasets()
        summary += f"可用数据集: {len(datasets)}\n\n"
        
        for dataset in datasets:
            try:
                data = self.get_reference_data(dataset)
                source = data.get('source', 'unknown')
                uncertainty = data.get('uncertainty', 'unknown')
                summary += f"{dataset}: {source} (不确定度: {uncertainty})\n"
            except:
                summary += f"{dataset}: 数据加载失败\n"
        
        return summary

    # ========== 增强的数据加载方法 ==========

    def load_experimental_data(self, file_path: Union[str, Path],
                             data_format: Optional[str] = None) -> Dict[str, Any]:
        """
        加载实验数据文件

        Args:
            file_path: 数据文件路径
            data_format: 数据格式（可选，自动检测）

        Returns:
            加载的数据字典
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"数据文件不存在: {file_path}")

        # 检查缓存
        cache_key = str(file_path.absolute())
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]

        # 自动检测格式
        if data_format is None:
            data_format = file_path.suffix.lower()

        # 加载数据
        if data_format in self.supported_formats:
            loader = self.supported_formats[data_format]
            data = loader(file_path)

            # 缓存数据
            self.data_cache[cache_key] = data

            print(f"✅ 成功加载数据文件: {file_path}")
            return data
        else:
            raise ValueError(f"不支持的数据格式: {data_format}")

    def _load_csv(self, file_path: Path) -> Dict[str, Any]:
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            return {
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'shape': df.shape,
                'metadata': {
                    'format': 'csv',
                    'file_path': str(file_path)
                }
            }
        except Exception as e:
            raise ValueError(f"CSV文件加载失败: {e}")

    def _load_json(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if isinstance(data, dict):
                data['metadata'] = data.get('metadata', {})
                data['metadata'].update({
                    'format': 'json',
                    'file_path': str(file_path)
                })

            return data
        except Exception as e:
            raise ValueError(f"JSON文件加载失败: {e}")

    def _load_yaml(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            if isinstance(data, dict):
                data['metadata'] = data.get('metadata', {})
                data['metadata'].update({
                    'format': 'yaml',
                    'file_path': str(file_path)
                })

            return data
        except Exception as e:
            raise ValueError(f"YAML文件加载失败: {e}")

    def _load_hdf5(self, file_path: Path) -> Dict[str, Any]:
        """加载HDF5文件"""
        try:
            data = {}

            with h5py.File(file_path, 'r') as f:
                def extract_data(name, obj):
                    if isinstance(obj, h5py.Dataset):
                        data[name] = obj[:]
                    elif isinstance(obj, h5py.Group):
                        group_data = {}
                        for key in obj.keys():
                            if isinstance(obj[key], h5py.Dataset):
                                group_data[key] = obj[key][:]
                        if group_data:
                            data[name] = group_data

                f.visititems(extract_data)

            data['metadata'] = {
                'format': 'hdf5',
                'file_path': str(file_path)
            }

            return data
        except Exception as e:
            raise ValueError(f"HDF5文件加载失败: {e}")

    def _load_matlab(self, file_path: Path) -> Dict[str, Any]:
        """加载MATLAB文件"""
        try:
            mat_data = loadmat(file_path)

            # 过滤掉MATLAB内部变量
            filtered_data = {
                key: value for key, value in mat_data.items()
                if not key.startswith('__')
            }

            filtered_data['metadata'] = {
                'format': 'matlab',
                'file_path': str(file_path)
            }

            return filtered_data
        except Exception as e:
            raise ValueError(f"MATLAB文件加载失败: {e}")

    def _load_excel(self, file_path: Path) -> Dict[str, Any]:
        """加载Excel文件"""
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None)

            data = {}
            for sheet_name, df in excel_data.items():
                data[sheet_name] = {
                    'data': df.to_dict('records'),
                    'columns': df.columns.tolist(),
                    'shape': df.shape
                }

            data['metadata'] = {
                'format': 'excel',
                'file_path': str(file_path),
                'sheets': list(excel_data.keys())
            }

            return data
        except Exception as e:
            raise ValueError(f"Excel文件加载失败: {e}")

    def _load_text(self, file_path: Path) -> Dict[str, Any]:
        """加载文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试解析为数值数据
            lines = content.strip().split('\n')
            numeric_data = []

            for line in lines:
                if line.strip() and not line.startswith('#'):
                    try:
                        values = [float(x) for x in line.split()]
                        numeric_data.append(values)
                    except ValueError:
                        pass

            data = {
                'raw_content': content,
                'numeric_data': np.array(numeric_data) if numeric_data else None,
                'metadata': {
                    'format': 'text',
                    'file_path': str(file_path),
                    'lines': len(lines)
                }
            }

            return data
        except Exception as e:
            raise ValueError(f"文本文件加载失败: {e}")

    # ========== 数据处理和分析功能 ==========

    def interpolate_data(self, x_data: np.ndarray, y_data: np.ndarray,
                        x_new: np.ndarray, method: str = 'linear') -> np.ndarray:
        """
        数据插值

        Args:
            x_data: 原始x数据
            y_data: 原始y数据
            x_new: 新的x坐标
            method: 插值方法 ('linear', 'cubic', 'spline')

        Returns:
            插值后的y数据
        """
        try:
            if method == 'linear':
                f = interpolate.interp1d(x_data, y_data, kind='linear',
                                       bounds_error=False, fill_value='extrapolate')
            elif method == 'cubic':
                f = interpolate.interp1d(x_data, y_data, kind='cubic',
                                       bounds_error=False, fill_value='extrapolate')
            elif method == 'spline':
                f = interpolate.UnivariateSpline(x_data, y_data, s=0)
            else:
                raise ValueError(f"不支持的插值方法: {method}")

            return f(x_new)

        except Exception as e:
            raise ValueError(f"数据插值失败: {e}")

    def filter_data(self, data: np.ndarray, filter_type: str = 'moving_average',
                   window_size: int = 5) -> np.ndarray:
        """
        数据滤波

        Args:
            data: 输入数据
            filter_type: 滤波类型 ('moving_average', 'median')
            window_size: 窗口大小

        Returns:
            滤波后的数据
        """
        try:
            if filter_type == 'moving_average':
                # 移动平均滤波
                filtered = np.convolve(data, np.ones(window_size)/window_size, mode='same')
            elif filter_type == 'median':
                # 中值滤波
                from scipy.signal import medfilt
                filtered = medfilt(data, kernel_size=window_size)
            else:
                raise ValueError(f"不支持的滤波类型: {filter_type}")

            return filtered

        except Exception as e:
            raise ValueError(f"数据滤波失败: {e}")

    def calculate_statistics(self, data: np.ndarray) -> Dict[str, float]:
        """
        计算数据统计信息

        Args:
            data: 输入数据

        Returns:
            统计信息字典
        """
        try:
            stats = {
                'mean': float(np.mean(data)),
                'std': float(np.std(data)),
                'min': float(np.min(data)),
                'max': float(np.max(data)),
                'median': float(np.median(data)),
                'q25': float(np.percentile(data, 25)),
                'q75': float(np.percentile(data, 75)),
                'count': len(data),
                'rms': float(np.sqrt(np.mean(data**2)))
            }

            return stats

        except Exception as e:
            raise ValueError(f"统计计算失败: {e}")

    def compare_datasets(self, data1: np.ndarray, data2: np.ndarray) -> Dict[str, float]:
        """
        比较两个数据集

        Args:
            data1: 数据集1
            data2: 数据集2

        Returns:
            比较结果字典
        """
        try:
            # 确保数据长度一致
            min_len = min(len(data1), len(data2))
            data1 = data1[:min_len]
            data2 = data2[:min_len]

            # 计算比较指标
            diff = data1 - data2

            comparison = {
                'mean_absolute_error': float(np.mean(np.abs(diff))),
                'root_mean_square_error': float(np.sqrt(np.mean(diff**2))),
                'max_absolute_error': float(np.max(np.abs(diff))),
                'correlation_coefficient': float(np.corrcoef(data1, data2)[0, 1]),
                'relative_error_percent': float(np.mean(np.abs(diff / data1)) * 100) if np.all(data1 != 0) else float('inf')
            }

            return comparison

        except Exception as e:
            raise ValueError(f"数据比较失败: {e}")

    def export_data(self, data: Dict[str, Any], output_path: Union[str, Path],
                   format_type: str = 'json') -> bool:
        """
        导出数据到文件

        Args:
            data: 要导出的数据
            output_path: 输出文件路径
            format_type: 导出格式 ('json', 'csv', 'yaml', 'hdf5')

        Returns:
            导出是否成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            if format_type == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False, default=str)

            elif format_type == 'yaml':
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)

            elif format_type == 'csv':
                # 假设数据包含表格形式的数据
                if 'data' in data and isinstance(data['data'], list):
                    df = pd.DataFrame(data['data'])
                    df.to_csv(output_path, index=False)
                else:
                    raise ValueError("CSV导出需要表格形式的数据")

            elif format_type == 'hdf5':
                with h5py.File(output_path, 'w') as f:
                    def save_to_hdf5(group, data_dict):
                        for key, value in data_dict.items():
                            if isinstance(value, dict):
                                subgroup = group.create_group(key)
                                save_to_hdf5(subgroup, value)
                            elif isinstance(value, (np.ndarray, list)):
                                group.create_dataset(key, data=value)
                            else:
                                group.attrs[key] = str(value)

                    save_to_hdf5(f, data)

            else:
                raise ValueError(f"不支持的导出格式: {format_type}")

            print(f"✅ 数据已导出到: {output_path}")
            return True

        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False

    def clear_cache(self):
        """清空数据缓存"""
        self.data_cache.clear()
        print("✅ 数据缓存已清空")
