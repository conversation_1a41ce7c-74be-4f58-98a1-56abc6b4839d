"""
绘图管理器
=========

提供各种图表绘制功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import warnings

# 设置matplotlib中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PlotManager:
    """
    绘图管理器
    
    提供各种专业的图表绘制功能，包括：
    - 力系数时间历程
    - 收敛历程图
    - 频谱分析图
    - 性能对比图
    - 自定义图表
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化绘图管理器
        
        Args:
            config: 绘图配置参数
        """
        self.config = config or {}
        
        # 绘图样式配置
        self.figure_size = self.config.get('figure_size', (12, 8))
        self.dpi = self.config.get('dpi', 300)
        self.style = self.config.get('style', 'seaborn-v0_8')
        
        # 颜色配置
        self.colors = self.config.get('colors', [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ])
        
        # 设置绘图样式
        try:
            plt.style.use(self.style)
        except:
            warnings.warn(f"样式 {self.style} 不可用，使用默认样式")
        
        print("✅ 绘图管理器初始化完成")
        print(f"   图像尺寸: {self.figure_size}")
        print(f"   分辨率: {self.dpi} DPI")
    
    def plot_force_coefficients(self, time: np.ndarray, forces: np.ndarray,
                              save_path: Optional[Path] = None) -> Path:
        """
        绘制力系数时间历程
        
        Args:
            time: 时间数组
            forces: 力数组 [N x 3] (Fx, Fy, Fz)
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig, axes = plt.subplots(3, 1, figsize=self.figure_size, sharex=True)
        
        force_labels = ['推力 (Fx)', '侧力 (Fy)', '升力 (Fz)']
        force_colors = self.colors[:3]
        
        forces = np.array(forces)
        if forces.ndim == 1:
            forces = forces.reshape(-1, 1)
        
        for i in range(min(3, forces.shape[1])):
            axes[i].plot(time, forces[:, i], color=force_colors[i], linewidth=2)
            axes[i].set_ylabel(f'{force_labels[i]} [N]')
            axes[i].grid(True, alpha=0.3)
            axes[i].set_title(f'{force_labels[i]}时间历程')
        
        axes[-1].set_xlabel('时间 [s]')
        plt.suptitle('力系数时间历程', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('force_coefficients.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_convergence_history(self, convergence_history: np.ndarray,
                               save_path: Optional[Path] = None) -> Path:
        """
        绘制收敛历程图
        
        Args:
            convergence_history: 收敛历史数组
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        iterations = np.arange(1, len(convergence_history) + 1)
        
        # 对数坐标绘制
        ax.semilogy(iterations, convergence_history, 
                   color=self.colors[0], linewidth=2, marker='o', markersize=4)
        
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('残差 (对数尺度)')
        ax.set_title('收敛历程', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 添加收敛信息
        final_residual = convergence_history[-1]
        ax.text(0.7, 0.9, f'最终残差: {final_residual:.2e}', 
               transform=ax.transAxes, fontsize=12,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('convergence_history.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_frequency_spectrum(self, frequencies: np.ndarray, spectrum: np.ndarray,
                              save_path: Optional[Path] = None) -> Path:
        """
        绘制频谱分析图
        
        Args:
            frequencies: 频率数组 [Hz]
            spectrum: 频谱数组 [dB]
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 转换为dB（如果需要）
        if np.max(spectrum) > 200:  # 假设已经是dB
            spectrum_db = spectrum
        else:  # 转换为dB
            spectrum_db = 20 * np.log10(np.maximum(spectrum, 1e-12))
        
        ax.semilogx(frequencies, spectrum_db, color=self.colors[1], linewidth=2)
        
        ax.set_xlabel('频率 [Hz]')
        ax.set_ylabel('声压级 [dB]')
        ax.set_title('频谱分析', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 标记峰值
        peak_idx = np.argmax(spectrum_db)
        peak_freq = frequencies[peak_idx]
        peak_level = spectrum_db[peak_idx]
        
        ax.plot(peak_freq, peak_level, 'ro', markersize=8)
        ax.annotate(f'峰值: {peak_freq:.1f} Hz\n{peak_level:.1f} dB',
                   xy=(peak_freq, peak_level), xytext=(peak_freq*2, peak_level+10),
                   arrowprops=dict(arrowstyle='->', color='red'),
                   fontsize=10, ha='center')
        
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('frequency_spectrum.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_performance_comparison(self, data: Dict[str, np.ndarray],
                                  save_path: Optional[Path] = None) -> Path:
        """
        绘制性能对比图
        
        Args:
            data: 性能数据字典
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        labels = list(data.keys())
        values = [np.mean(data[key]) if isinstance(data[key], (list, np.ndarray)) 
                 else data[key] for key in labels]
        errors = [np.std(data[key]) if isinstance(data[key], (list, np.ndarray)) 
                 else 0 for key in labels]
        
        bars = ax.bar(labels, values, yerr=errors, capsize=5, 
                     color=self.colors[:len(labels)], alpha=0.7)
        
        ax.set_ylabel('性能指标')
        ax.set_title('性能对比', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(errors)*0.1,
                   f'{value:.3f}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('performance_comparison.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_polar_diagram(self, angles: np.ndarray, values: np.ndarray,
                          save_path: Optional[Path] = None) -> Path:
        """
        绘制极坐标图（如方向性图案）
        
        Args:
            angles: 角度数组 [rad]
            values: 数值数组
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        fig, ax = plt.subplots(figsize=self.figure_size, subplot_kw=dict(projection='polar'))
        
        ax.plot(angles, values, color=self.colors[2], linewidth=2)
        ax.fill(angles, values, alpha=0.3, color=self.colors[2])
        
        ax.set_title('极坐标图', fontsize=16, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('polar_diagram.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def create_custom_plot(self, plot_function, *args, save_path: Optional[Path] = None, **kwargs) -> Path:
        """
        创建自定义图表
        
        Args:
            plot_function: 绘图函数
            *args: 位置参数
            save_path: 保存路径
            **kwargs: 关键字参数
            
        Returns:
            图像保存路径
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 执行自定义绘图函数
        plot_function(ax, *args, **kwargs)
        
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('custom_plot.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def create_subplot_grid(self, plot_configs: List[Dict[str, Any]],
                          save_path: Optional[Path] = None) -> Path:
        """
        创建子图网格
        
        Args:
            plot_configs: 子图配置列表
            save_path: 保存路径
            
        Returns:
            图像保存路径
        """
        n_plots = len(plot_configs)
        n_cols = int(np.ceil(np.sqrt(n_plots)))
        n_rows = int(np.ceil(n_plots / n_cols))
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(self.figure_size[0]*n_cols/2, 
                                                         self.figure_size[1]*n_rows/2))
        
        if n_plots == 1:
            axes = [axes]
        elif n_rows == 1 or n_cols == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        for i, config in enumerate(plot_configs):
            ax = axes[i]
            plot_type = config.get('type', 'line')
            
            if plot_type == 'line':
                ax.plot(config['x'], config['y'], label=config.get('label', ''))
            elif plot_type == 'scatter':
                ax.scatter(config['x'], config['y'], label=config.get('label', ''))
            elif plot_type == 'bar':
                ax.bar(config['x'], config['y'], label=config.get('label', ''))
            
            ax.set_title(config.get('title', f'子图 {i+1}'))
            ax.set_xlabel(config.get('xlabel', 'X'))
            ax.set_ylabel(config.get('ylabel', 'Y'))
            ax.grid(True, alpha=0.3)
            
            if config.get('label'):
                ax.legend()
        
        # 隐藏多余的子图
        for i in range(n_plots, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图像
        if save_path is None:
            save_path = Path('subplot_grid.png')
        
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return save_path
