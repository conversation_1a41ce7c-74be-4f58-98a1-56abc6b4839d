#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构版本集成测试
==============

测试重构后的cycloidal_rotor_suite的完整功能
"""

import numpy as np
import sys
import os
import time
from typing import Dict, Any

# 添加路径以导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_core_interfaces():
    """测试核心接口"""
    print("🧪 测试核心接口...")
    
    try:
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel, SolverType
        from core.interfaces.data_interface import AerodynamicData, AcousticData
        from core.interfaces.coupling_interface import CouplingConfig
        
        # 测试配置创建
        config = SolverConfig(
            fidelity_level=FidelityLevel.MEDIUM,
            time_step=0.01,
            max_iterations=100
        )
        
        # 测试数据结构
        aero_data = AerodynamicData(
            forces=np.array([1.0, 2.0, 3.0]),
            moments=np.array([0.1, 0.2, 0.3]),
            pressure=np.array([101325.0]),
            velocity=np.array([[10.0, 0.0, 0.0]]),
            time_stamp=0.0,
            blade_positions=np.array([[1.0, 0.0, 0.0]])
        )
        
        print("✅ 核心接口测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 核心接口测试失败: {str(e)}")
        return False

def test_geometry_module():
    """测试几何模块"""
    print("🧪 测试几何模块...")
    
    try:
        from core.geometry import RotorGeometry, BladeGeometry, CycloidalKinematics
        
        # 测试旋翼几何
        rotor = RotorGeometry(
            radius=1.0,
            hub_radius=0.1,
            blade_count=4,
            num_stations=15
        )
        
        # 设置弦长和扭转分布
        rotor.set_chord_distribution(np.ones(15) * 0.08)
        rotor.set_twist_distribution(np.linspace(-8, -18, 15) * np.pi/180)
        
        # 测试桨叶几何
        blade = BladeGeometry(radius=1.0, hub_radius=0.1, num_stations=15)
        blade.set_constant_chord_distribution(0.08)
        blade.set_linear_twist_distribution(8.0, -2.0)
        
        # 测试运动学
        kinematics_config = {
            'rotor_radius': 1.0,
            'blade_count': 4,
            'pitch_amplitude': 15.0,
            'pitch_phase': 0.0
        }
        kinematics = CycloidalKinematics(kinematics_config)
        
        # 计算运动学状态
        state = kinematics.compute_kinematics_state(0.0, 157.08)  # 1500 RPM
        
        print("✅ 几何模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 几何模块测试失败: {str(e)}")
        return False

def test_aerodynamic_solvers():
    """测试气动求解器"""
    print("🧪 测试气动求解器...")
    
    try:
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel
        from core.factories.solver_factory import get_solver_factory
        
        factory = get_solver_factory()
        
        # 测试BEMT求解器
        bemt_config = SolverConfig(
            fidelity_level=FidelityLevel.LOW,
            time_step=0.01,
            solver_specific_params={
                'blade_elements_count': 10,
                'enable_tip_loss': True
            }
        )
        
        bemt_solver = factory.create_aerodynamic_solver("BEMT", bemt_config)
        
        # 初始化几何
        geometry_data = {
            'rotor_radius': 1.0,
            'blade_count': 4,
            'hub_radius': 0.1,
            'chord_distribution': np.ones(10) * 0.08,
            'twist_distribution': np.linspace(-8, -18, 10) * np.pi/180
        }
        
        bemt_solver.initialize(geometry_data)
        
        # 求解一个时间步
        boundary_conditions = {
            'rotor_rpm': 1500.0,
            'freestream_velocity': np.array([0.0, 0.0, 0.0]),
            'blade_pitch': np.radians(8.0)
        }
        
        result = bemt_solver.solve_timestep(0.0, boundary_conditions)
        
        print(f"   BEMT求解完成，力: {result.forces}")
        print("✅ 气动求解器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 气动求解器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_acoustic_solvers():
    """测试声学求解器"""
    print("🧪 测试声学求解器...")
    
    try:
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel
        from core.interfaces.data_interface import AerodynamicData
        from core.factories.solver_factory import get_solver_factory
        
        factory = get_solver_factory()
        
        # 测试FWH求解器
        fwh_config = SolverConfig(
            fidelity_level=FidelityLevel.HIGH,
            time_step=0.01,
            solver_specific_params={
                'observer_positions': np.array([[10.0, 0.0, 0.0]]),
                'enable_thickness_noise': True,
                'enable_loading_noise': True
            }
        )
        
        fwh_solver = factory.create_acoustic_solver("FWH", fwh_config)
        
        # 初始化几何
        geometry_data = {
            'rotor_radius': 1.0,
            'blade_count': 4
        }
        
        fwh_solver.initialize(geometry_data)
        
        # 创建气动数据
        aero_data = AerodynamicData(
            forces=np.array([100.0, 50.0, 200.0]),
            moments=np.array([10.0, 5.0, 20.0]),
            pressure=np.array([101325.0]),
            velocity=np.array([[50.0, 0.0, 0.0]]),
            time_stamp=0.0,
            blade_positions=np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [-1.0, 0.0, 0.0], [0.0, -1.0, 0.0]])
        )
        
        # 求解声学
        boundary_conditions = {
            'aerodynamic_data': aero_data
        }
        
        result = fwh_solver.solve_timestep(0.0, boundary_conditions)
        
        print(f"   FWH求解完成")
        print("✅ 声学求解器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 声学求解器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_coupling_framework():
    """测试耦合框架"""
    print("🧪 测试耦合框架...")
    
    try:
        from core.coupling import CouplingManager
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel
        from core.interfaces.coupling_interface import CouplingConfig
        from core.factories.solver_factory import get_solver_factory
        
        # 创建求解器
        factory = get_solver_factory()
        
        aero_config = SolverConfig(
            fidelity_level=FidelityLevel.LOW,
            time_step=0.01,
            solver_specific_params={'blade_elements_count': 8}
        )
        
        acoustic_config = SolverConfig(
            fidelity_level=FidelityLevel.MEDIUM,
            time_step=0.01,
            solver_specific_params={'observer_positions': np.array([[5.0, 0.0, 0.0]])}
        )
        
        aero_solver = factory.create_aerodynamic_solver("BEMT", aero_config)
        acoustic_solver = factory.create_acoustic_solver("FWH", acoustic_config)
        
        # 创建耦合管理器
        coupling_config = {
            'coupling_method': 'one_way',
            'time_step': 0.01,
            'max_time': 0.1,
            'output_interval': 0.05
        }
        
        manager = CouplingManager(coupling_config)
        manager.register_solver("aero", aero_solver)
        manager.register_solver("acoustic", acoustic_solver)
        
        # 创建耦合
        coupling_name = manager.create_aero_acoustic_coupling("aero", "acoustic")
        
        # 运行简短的耦合仿真
        geometry_data = {
            'rotor_radius': 1.0,
            'blade_count': 3,
            'hub_radius': 0.1,
            'chord_distribution': np.ones(8) * 0.08,
            'twist_distribution': np.linspace(-8, -18, 8) * np.pi/180
        }
        
        boundary_conditions = {
            'rotor_rpm': 1200.0,
            'freestream_velocity': np.array([0.0, 0.0, 0.0]),
            'blade_pitch': np.radians(6.0)
        }
        
        results = manager.run_coupled_simulation(geometry_data, boundary_conditions, coupling_name)
        
        print(f"   耦合仿真完成，时间步数: {len(results['results_history'])}")
        print("✅ 耦合框架测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 耦合框架测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_physics_models():
    """测试物理模型"""
    print("🧪 测试物理模型...")
    
    try:
        from core.physics import LeishmanBeddoesModel, TipLossCorrection, GPUAccelerator
        
        # 测试动态失速模型
        ds_model = LeishmanBeddoesModel({
            'alpha_stall': 15.0,
            'Cl_max': 1.4,
            'Cl_alpha': 6.28
        })
        
        # 计算动态系数
        Cl, Cd, Cm = ds_model.compute_dynamic_coefficients(
            alpha=np.radians(10.0),
            alpha_dot=np.radians(5.0),
            velocity=50.0,
            chord=0.1,
            dt=0.01
        )
        
        # 测试叶尖损失修正
        tip_correction = TipLossCorrection("prandtl")
        tip_factor = tip_correction.apply_correction(r=0.9, R=1.0, B=3)
        
        # 测试GPU加速器
        gpu_accel = GPUAccelerator("auto")
        
        print(f"   动态失速系数: Cl={Cl:.3f}, Cd={Cd:.4f}")
        print(f"   叶尖损失因子: {tip_factor:.3f}")
        print(f"   GPU可用: {gpu_accel.is_available}")
        print("✅ 物理模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 物理模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_performance_benchmark():
    """运行性能基准测试"""
    print("🧪 运行性能基准测试...")
    
    try:
        from core.factories.solver_factory import get_solver_factory
        from core.interfaces.solver_interface import SolverConfig, FidelityLevel
        
        factory = get_solver_factory()
        
        # 创建BEMT求解器
        config = SolverConfig(
            fidelity_level=FidelityLevel.LOW,
            time_step=0.01,
            solver_specific_params={'blade_elements_count': 20}
        )
        
        solver = factory.create_aerodynamic_solver("BEMT", config)
        
        # 初始化
        geometry_data = {
            'rotor_radius': 1.0,
            'blade_count': 4,
            'hub_radius': 0.1,
            'chord_distribution': np.ones(20) * 0.08,
            'twist_distribution': np.linspace(-8, -18, 20) * np.pi/180
        }
        
        solver.initialize(geometry_data)
        
        # 性能测试
        boundary_conditions = {
            'rotor_rpm': 1500.0,
            'freestream_velocity': np.array([0.0, 0.0, 0.0]),
            'blade_pitch': np.radians(8.0)
        }
        
        # 计时多次求解
        n_iterations = 100
        start_time = time.time()
        
        for i in range(n_iterations):
            result = solver.solve_timestep(i * 0.01, boundary_conditions)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / n_iterations
        
        print(f"   {n_iterations}次求解总时间: {total_time:.3f} s")
        print(f"   平均每次求解时间: {avg_time*1000:.2f} ms")
        print("✅ 性能基准测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("cycloidal_rotor_suite 重构版本集成测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    test_functions = [
        ("核心接口", test_core_interfaces),
        ("几何模块", test_geometry_module),
        ("气动求解器", test_aerodynamic_solvers),
        ("声学求解器", test_acoustic_solvers),
        ("耦合框架", test_coupling_framework),
        ("物理模型", test_physics_models),
        ("性能基准", run_performance_benchmark)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'-' * 60}")
        result = test_func()
        test_results.append((test_name, result))
    
    # 汇总结果
    print(f"\n{'=' * 80}")
    print("测试结果汇总:")
    print(f"{'=' * 80}")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构版本功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
