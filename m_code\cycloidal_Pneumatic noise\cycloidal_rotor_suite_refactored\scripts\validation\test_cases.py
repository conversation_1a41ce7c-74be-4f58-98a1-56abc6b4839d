"""
标准测试用例管理
===============

定义和管理标准验证测试用例
"""

import numpy as np
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class TestCaseConfig:
    """测试用例配置"""
    name: str
    description: str
    geometry: Dict[str, Any]
    operating_conditions: Dict[str, Any]
    solver_settings: Dict[str, Any]
    expected_results: Dict[str, Any]

class StandardTestCases:
    """
    标准测试用例管理器
    
    提供预定义的标准测试用例，包括：
    - 基础验证用例
    - 循环翼转子特定用例
    - 极限工况测试用例
    - 收敛性测试用例
    """
    
    def __init__(self):
        """初始化标准测试用例"""
        self.test_cases = {}
        self._initialize_standard_cases()
    
    def _initialize_standard_cases(self):
        """初始化标准测试用例"""
        
        # 基础BEMT验证用例
        self.test_cases['bemt_basic'] = TestCaseConfig(
            name='bemt_basic',
            description='BEMT求解器基础验证',
            geometry={
                'R_rotor': 1.0,
                'R_hub': 0.1,
                'B': 3,
                'chord_distribution': np.linspace(0.1, 0.05, 10),
                'twist_distribution': np.linspace(0.2, 0.0, 10)
            },
            operating_conditions={
                'omega_rotor': 100.0,
                'V_inf': np.array([10.0, 0.0, 0.0]),
                'rho': 1.225,
                'mu': 1.81e-5
            },
            solver_settings={
                'max_iterations': 100,
                'convergence_tolerance': 1e-6,
                'relaxation_factor': 0.5
            },
            expected_results={
                'thrust': 150.0,
                'torque': 25.0,
                'power': 2500.0,
                'convergence_iterations': 50
            }
        )
        
        # 循环翼转子特定用例
        self.test_cases['cycloidal_basic'] = TestCaseConfig(
            name='cycloidal_basic',
            description='循环翼转子基础验证',
            geometry={
                'R_rotor': 0.5,
                'R_hub': 0.05,
                'B': 4,
                'chord_distribution': np.full(15, 0.08),
                'twist_distribution': np.zeros(15),
                'cycloidal_amplitude': 0.3,
                'phase_offset': 0.0
            },
            operating_conditions={
                'omega_rotor': 200.0,
                'V_inf': np.array([5.0, 0.0, 0.0]),
                'rho': 1.225,
                'advance_ratio': 0.1
            },
            solver_settings={
                'max_iterations': 150,
                'convergence_tolerance': 1e-6,
                'enable_dynamic_stall': True,
                'enable_3d_effects': True
            },
            expected_results={
                'mean_thrust': 80.0,
                'thrust_variation': 20.0,
                'side_force': 15.0,
                'power_coefficient': 0.8
            }
        )
        
        # 高保真度UVLM用例
        self.test_cases['uvlm_validation'] = TestCaseConfig(
            name='uvlm_validation',
            description='UVLM求解器验证',
            geometry={
                'R_rotor': 1.2,
                'R_hub': 0.12,
                'B': 3,
                'chord_distribution': np.linspace(0.12, 0.06, 20),
                'twist_distribution': np.linspace(0.25, -0.05, 20)
            },
            operating_conditions={
                'omega_rotor': 80.0,
                'V_inf': np.array([15.0, 0.0, 0.0]),
                'rho': 1.225,
                'collective_pitch': 0.1
            },
            solver_settings={
                'panel_count_chordwise': 10,
                'panel_count_spanwise': 20,
                'enable_free_wake': True,
                'wake_iterations': 5
            },
            expected_results={
                'lift_coefficient': 0.6,
                'induced_velocity_ratio': 0.15,
                'wake_contraction': 0.8,
                'tip_vortex_strength': 2.5
            }
        )
        
        # 声学验证用例
        self.test_cases['acoustic_validation'] = TestCaseConfig(
            name='acoustic_validation',
            description='FW-H声学求解器验证',
            geometry={
                'R_rotor': 1.0,
                'R_hub': 0.1,
                'B': 2,
                'chord_distribution': np.full(12, 0.1),
                'twist_distribution': np.zeros(12)
            },
            operating_conditions={
                'omega_rotor': 150.0,
                'V_inf': np.array([20.0, 0.0, 0.0]),
                'rho': 1.225,
                'tip_mach': 0.6
            },
            solver_settings={
                'observer_positions': [[10.0, 0.0, 0.0], [0.0, 10.0, 0.0]],
                'frequency_range': [10.0, 2000.0],
                'enable_thickness_noise': True,
                'enable_loading_noise': True
            },
            expected_results={
                'bpf_spl': 85.0,
                'broadband_spl': 70.0,
                'directivity_pattern': 'dipole',
                'peak_frequency': 300.0
            }
        )
        
        # 收敛性测试用例
        self.test_cases['convergence_test'] = TestCaseConfig(
            name='convergence_test',
            description='求解器收敛性测试',
            geometry={
                'R_rotor': 0.8,
                'R_hub': 0.08,
                'B': 3,
                'chord_distribution': np.linspace(0.09, 0.045, 8),
                'twist_distribution': np.linspace(0.15, 0.0, 8)
            },
            operating_conditions={
                'omega_rotor': 120.0,
                'V_inf': np.array([12.0, 0.0, 0.0]),
                'rho': 1.225
            },
            solver_settings={
                'max_iterations': 200,
                'convergence_tolerance': 1e-8,
                'relaxation_factor': 0.3
            },
            expected_results={
                'final_residual': 1e-8,
                'convergence_rate': 0.1,
                'iterations_to_convergence': 80,
                'solution_stability': True
            }
        )
        
        # 极限工况测试
        self.test_cases['extreme_conditions'] = TestCaseConfig(
            name='extreme_conditions',
            description='极限工况稳定性测试',
            geometry={
                'R_rotor': 1.5,
                'R_hub': 0.15,
                'B': 4,
                'chord_distribution': np.linspace(0.15, 0.075, 12),
                'twist_distribution': np.linspace(0.3, -0.1, 12)
            },
            operating_conditions={
                'omega_rotor': 60.0,
                'V_inf': np.array([30.0, 0.0, 0.0]),
                'rho': 0.8,  # 高海拔条件
                'advance_ratio': 0.8  # 高前进比
            },
            solver_settings={
                'max_iterations': 300,
                'convergence_tolerance': 1e-5,
                'enable_stall_model': True,
                'enable_compressibility': True
            },
            expected_results={
                'solution_convergence': True,
                'physical_validity': True,
                'numerical_stability': True,
                'performance_degradation': 'acceptable'
            }
        )
        
        print(f"✅ 标准测试用例初始化完成，共 {len(self.test_cases)} 个用例")
    
    def get_test_case(self, name: str) -> TestCaseConfig:
        """获取指定的测试用例"""
        if name not in self.test_cases:
            raise ValueError(f"未找到测试用例: {name}")
        return self.test_cases[name]
    
    def get_available_test_cases(self) -> List[str]:
        """获取所有可用的测试用例名称"""
        return list(self.test_cases.keys())
    
    def get_test_cases_by_category(self, category: str) -> List[str]:
        """按类别获取测试用例"""
        category_mapping = {
            'basic': ['bemt_basic', 'cycloidal_basic'],
            'advanced': ['uvlm_validation', 'acoustic_validation'],
            'stability': ['convergence_test', 'extreme_conditions'],
            'aerodynamic': ['bemt_basic', 'cycloidal_basic', 'uvlm_validation'],
            'acoustic': ['acoustic_validation'],
            'all': list(self.test_cases.keys())
        }
        
        return category_mapping.get(category, [])
    
    def add_custom_test_case(self, test_case: TestCaseConfig) -> None:
        """添加自定义测试用例"""
        self.test_cases[test_case.name] = test_case
        print(f"✅ 已添加自定义测试用例: {test_case.name}")
    
    def get_test_case_summary(self) -> str:
        """获取测试用例摘要"""
        summary = "标准测试用例摘要\n"
        summary += "=" * 40 + "\n"
        
        for name, case in self.test_cases.items():
            summary += f"{name}: {case.description}\n"
        
        summary += f"\n总计: {len(self.test_cases)} 个测试用例"
        return summary
