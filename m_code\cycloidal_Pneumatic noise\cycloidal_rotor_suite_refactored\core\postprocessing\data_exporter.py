"""
数据导出器
=========

提供多种格式的数据导出功能
"""

import numpy as np
import pandas as pd
import json
import csv
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import warnings

class DataExporter:
    """
    数据导出器
    
    支持多种格式的数据导出：
    - CSV格式
    - JSON格式
    - Excel格式
    - HDF5格式
    - MATLAB格式
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据导出器
        
        Args:
            config: 导出配置参数
        """
        self.config = config or {}
        
        # 导出配置
        self.precision = self.config.get('precision', 6)
        self.encoding = self.config.get('encoding', 'utf-8')
        self.compression = self.config.get('compression', None)
        
        print("✅ 数据导出器初始化完成")
        print(f"   数值精度: {self.precision} 位小数")
        print(f"   编码格式: {self.encoding}")
    
    def export_to_csv(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出为CSV格式
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            # 准备数据
            export_data = self._prepare_tabular_data(data)
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 导出CSV
            df.to_csv(file_path, index=False, encoding=self.encoding, 
                     float_format=f'%.{self.precision}f')
            
            print(f"✅ CSV导出成功: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ CSV导出失败: {e}")
            raise
    
    def export_to_json(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出为JSON格式
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            # 准备数据（转换numpy数组为列表）
            export_data = self._prepare_json_data(data)
            
            # 导出JSON
            with open(file_path, 'w', encoding=self.encoding) as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ JSON导出成功: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ JSON导出失败: {e}")
            raise
    
    def export_to_excel(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出为Excel格式
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            # 准备数据
            export_data = self._prepare_tabular_data(data)
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 导出Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                
                # 添加元数据工作表
                if 'metadata' in data:
                    metadata_df = pd.DataFrame([data['metadata']])
                    metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
            
            print(f"✅ Excel导出成功: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ Excel导出失败: {e}")
            # 如果openpyxl不可用，尝试使用xlsxwriter
            try:
                df = pd.DataFrame(self._prepare_tabular_data(data))
                df.to_excel(file_path, index=False, engine='xlsxwriter')
                print(f"✅ Excel导出成功（备用引擎）: {file_path}")
                return file_path
            except:
                raise
    
    def export_to_hdf5(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出为HDF5格式（适用于大数据集）
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            import h5py
            
            with h5py.File(file_path, 'w') as f:
                self._write_hdf5_group(f, data)
            
            print(f"✅ HDF5导出成功: {file_path}")
            return file_path
            
        except ImportError:
            warnings.warn("h5py未安装，无法导出HDF5格式")
            # 回退到JSON格式
            json_path = file_path.with_suffix('.json')
            return self.export_to_json(data, json_path)
        except Exception as e:
            print(f"❌ HDF5导出失败: {e}")
            raise
    
    def export_to_matlab(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出为MATLAB格式
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            from scipy.io import savemat
            
            # 准备MATLAB兼容的数据
            matlab_data = self._prepare_matlab_data(data)
            
            # 导出MAT文件
            savemat(file_path, matlab_data)
            
            print(f"✅ MATLAB导出成功: {file_path}")
            return file_path
            
        except ImportError:
            warnings.warn("scipy未安装，无法导出MATLAB格式")
            # 回退到JSON格式
            json_path = file_path.with_suffix('.json')
            return self.export_to_json(data, json_path)
        except Exception as e:
            print(f"❌ MATLAB导出失败: {e}")
            raise
    
    def export_summary_report(self, data: Dict[str, Any], file_path: Path) -> Path:
        """
        导出摘要报告（文本格式）
        
        Args:
            data: 要导出的数据
            file_path: 输出文件路径
            
        Returns:
            实际保存的文件路径
        """
        try:
            with open(file_path, 'w', encoding=self.encoding) as f:
                f.write("仿真结果摘要报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 基本信息
                if 'metadata' in data:
                    f.write("基本信息:\n")
                    for key, value in data['metadata'].items():
                        f.write(f"  {key}: {value}\n")
                    f.write("\n")
                
                # 统计信息
                if 'statistics' in data:
                    f.write("统计信息:\n")
                    self._write_statistics_section(f, data['statistics'])
                    f.write("\n")
                
                # 性能指标
                if 'performance' in data:
                    f.write("性能指标:\n")
                    for key, value in data['performance'].items():
                        if isinstance(value, (int, float)):
                            f.write(f"  {key}: {value:.{self.precision}f}\n")
                        else:
                            f.write(f"  {key}: {value}\n")
                    f.write("\n")
                
                # 文件信息
                if 'exports' in data:
                    f.write("导出文件:\n")
                    for format_type, file_path in data['exports'].items():
                        f.write(f"  {format_type.upper()}: {file_path}\n")
            
            print(f"✅ 摘要报告导出成功: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ 摘要报告导出失败: {e}")
            raise
    
    def _prepare_tabular_data(self, data: Dict[str, Any]) -> Dict[str, List]:
        """准备表格数据"""
        tabular_data = {}
        max_length = 0

        # 首先找到最大长度
        for key, value in data.items():
            if isinstance(value, (list, np.ndarray)):
                value_array = np.array(value)
                if value_array.ndim == 1:
                    max_length = max(max_length, len(value_array))
                elif value_array.ndim == 2:
                    max_length = max(max_length, value_array.shape[0])

        # 如果没有数组数据，设置默认长度为1
        if max_length == 0:
            max_length = 1

        # 处理数据，确保所有列长度一致
        for key, value in data.items():
            if isinstance(value, (list, np.ndarray)):
                value_array = np.array(value)
                if value_array.ndim == 1:
                    # 如果长度不足，用最后一个值填充
                    if len(value_array) < max_length:
                        padded_array = np.full(max_length, value_array[-1] if len(value_array) > 0 else 0)
                        padded_array[:len(value_array)] = value_array
                        tabular_data[key] = padded_array.tolist()
                    else:
                        tabular_data[key] = value_array[:max_length].tolist()
                elif value_array.ndim == 2:
                    # 多维数组展开为多列
                    for i in range(value_array.shape[1]):
                        col_data = value_array[:, i]
                        if len(col_data) < max_length:
                            padded_col = np.full(max_length, col_data[-1] if len(col_data) > 0 else 0)
                            padded_col[:len(col_data)] = col_data
                            tabular_data[f"{key}_{i}"] = padded_col.tolist()
                        else:
                            tabular_data[f"{key}_{i}"] = col_data[:max_length].tolist()
            elif isinstance(value, (int, float)):
                # 标量值重复到最大长度
                tabular_data[key] = [value] * max_length
            elif isinstance(value, dict):
                # 嵌套字典展平
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, (int, float)):
                        tabular_data[f"{key}_{sub_key}"] = [sub_value] * max_length

        return tabular_data
    
    def _prepare_json_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """准备JSON数据（转换numpy数组）"""
        json_data = {}
        
        for key, value in data.items():
            if isinstance(value, np.ndarray):
                json_data[key] = value.tolist()
            elif isinstance(value, dict):
                json_data[key] = self._prepare_json_data(value)
            elif isinstance(value, (list, tuple)):
                json_data[key] = [
                    item.tolist() if isinstance(item, np.ndarray) else item 
                    for item in value
                ]
            else:
                json_data[key] = value
        
        return json_data
    
    def _prepare_matlab_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """准备MATLAB兼容数据"""
        matlab_data = {}
        
        for key, value in data.items():
            # MATLAB变量名不能包含特殊字符
            matlab_key = key.replace('-', '_').replace(' ', '_')
            
            if isinstance(value, np.ndarray):
                matlab_data[matlab_key] = value
            elif isinstance(value, (list, tuple)):
                matlab_data[matlab_key] = np.array(value)
            elif isinstance(value, dict):
                # 嵌套字典转换为结构体
                for sub_key, sub_value in value.items():
                    matlab_sub_key = f"{matlab_key}_{sub_key}".replace('-', '_').replace(' ', '_')
                    if isinstance(sub_value, (list, np.ndarray)):
                        matlab_data[matlab_sub_key] = np.array(sub_value)
                    else:
                        matlab_data[matlab_sub_key] = sub_value
            else:
                matlab_data[matlab_key] = value
        
        return matlab_data
    
    def _write_hdf5_group(self, group, data: Dict[str, Any]):
        """递归写入HDF5组"""
        for key, value in data.items():
            if isinstance(value, np.ndarray):
                group.create_dataset(key, data=value)
            elif isinstance(value, (list, tuple)):
                group.create_dataset(key, data=np.array(value))
            elif isinstance(value, dict):
                subgroup = group.create_group(key)
                self._write_hdf5_group(subgroup, value)
            elif isinstance(value, (int, float, str)):
                group.attrs[key] = value
    
    def _write_statistics_section(self, file, statistics: Dict[str, Any]):
        """写入统计信息部分"""
        for category, stats in statistics.items():
            file.write(f"  {category}:\n")
            if isinstance(stats, dict):
                for stat_name, stat_value in stats.items():
                    if isinstance(stat_value, (list, np.ndarray)):
                        stat_value = np.array(stat_value)
                        file.write(f"    {stat_name}: [{', '.join([f'{x:.{self.precision}f}' for x in stat_value])}]\n")
                    elif isinstance(stat_value, (int, float)):
                        file.write(f"    {stat_name}: {stat_value:.{self.precision}f}\n")
                    else:
                        file.write(f"    {stat_name}: {stat_value}\n")
            else:
                file.write(f"    值: {stats}\n")
            file.write("\n")
