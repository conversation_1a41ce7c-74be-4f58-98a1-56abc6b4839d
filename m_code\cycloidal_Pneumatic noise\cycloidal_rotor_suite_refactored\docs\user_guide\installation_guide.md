# 循环翼转子仿真套件安装指南
## Installation Guide for Cycloidal Rotor Simulation Suite

**版本**: v2.0  
**更新时间**: 2025-01-08  
**适用平台**: Windows, Linux, macOS  

---

## 📋 **系统要求**

### **最低系统要求**
- **操作系统**: Windows 10/11, Ubuntu 18.04+, macOS 10.15+
- **Python版本**: Python 3.8+
- **内存**: 8GB RAM (推荐16GB+)
- **存储空间**: 5GB可用空间
- **处理器**: Intel i5或AMD Ryzen 5同等性能

### **推荐系统配置**
- **操作系统**: Windows 11, Ubuntu 20.04+, macOS 12+
- **Python版本**: Python 3.9-3.11
- **内存**: 32GB RAM
- **存储空间**: 20GB可用空间（包含示例数据）
- **处理器**: Intel i7/i9或AMD Ryzen 7/9
- **GPU**: NVIDIA GTX 1060+（可选，用于GPU加速）

---

## 🚀 **快速安装**

### **方法1: 使用pip安装（推荐）**

```bash
# 创建虚拟环境
python -m venv cycloidal_env
source cycloidal_env/bin/activate  # Linux/macOS
# 或
cycloidal_env\Scripts\activate     # Windows

# 安装套件
pip install cycloidal-rotor-suite

# 验证安装
cycloidal-rotor --version
```

### **方法2: 从源码安装**

```bash
# 克隆仓库
git clone https://github.com/your-org/cycloidal_rotor_suite_refactored.git
cd cycloidal_rotor_suite_refactored

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .
```

---

## 📦 **依赖包安装**

### **核心依赖**

```bash
# 数值计算
pip install numpy>=1.21.0
pip install scipy>=1.7.0
pip install matplotlib>=3.5.0

# 数据处理
pip install pandas>=1.3.0
pip install h5py>=3.1.0

# 可视化
pip install plotly>=5.0.0
pip install vtk>=9.0.0

# 配置管理
pip install pyyaml>=6.0
pip install jsonschema>=4.0.0
```

### **可选依赖**

```bash
# GPU加速（NVIDIA GPU）
pip install cupy-cuda11x  # 根据CUDA版本选择

# 并行计算
pip install mpi4py>=3.1.0

# 高级可视化
pip install mayavi>=4.7.0
pip install pyvista>=0.32.0

# 机器学习（用于代理模型）
pip install scikit-learn>=1.0.0
pip install tensorflow>=2.8.0  # 可选
```

---

## 🔧 **详细安装步骤**

### **步骤1: Python环境准备**

#### **Windows系统**
```powershell
# 下载并安装Python 3.9+
# 从 https://python.org 下载官方安装包

# 验证Python安装
python --version
pip --version

# 升级pip
python -m pip install --upgrade pip
```

#### **Linux系统**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip

# 验证安装
python3 --version
pip3 --version
```

#### **macOS系统**
```bash
# 使用Homebrew安装
brew install python

# 或使用官方安装包
# 从 https://python.org 下载

# 验证安装
python3 --version
pip3 --version
```

### **步骤2: 虚拟环境设置**

```bash
# 创建项目目录
mkdir cycloidal_projects
cd cycloidal_projects

# 创建虚拟环境
python -m venv cycloidal_env

# 激活虚拟环境
# Linux/macOS:
source cycloidal_env/bin/activate

# Windows:
cycloidal_env\Scripts\activate

# 验证虚拟环境
which python  # Linux/macOS
where python  # Windows
```

### **步骤3: 套件安装**

#### **从PyPI安装**
```bash
# 安装最新稳定版
pip install cycloidal-rotor-suite

# 安装特定版本
pip install cycloidal-rotor-suite==2.0.0

# 安装预发布版本
pip install --pre cycloidal-rotor-suite
```

#### **从源码安装**
```bash
# 克隆仓库
git clone https://github.com/your-org/cycloidal_rotor_suite_refactored.git
cd cycloidal_rotor_suite_refactored

# 查看可用分支/标签
git branch -a
git tag

# 切换到特定版本
git checkout v2.0.0

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发依赖（可选）

# 安装套件
pip install -e .  # 开发模式
# 或
pip install .     # 标准安装
```

### **步骤4: 验证安装**

```bash
# 检查安装
cycloidal-rotor --version
cycloidal-rotor --help

# 运行快速测试
python -c "import cycloidal_rotor_suite; print('安装成功!')"

# 运行内置测试
cycloidal-rotor test --quick
```

---

## ⚙️ **配置设置**

### **环境变量配置**

```bash
# Linux/macOS (.bashrc 或 .zshrc)
export CYCLOIDAL_HOME=/path/to/cycloidal_rotor_suite_refactored
export CYCLOIDAL_DATA=/path/to/data
export CYCLOIDAL_RESULTS=/path/to/results

# Windows (系统环境变量)
CYCLOIDAL_HOME=C:\path\to\cycloidal_rotor_suite_refactored
CYCLOIDAL_DATA=C:\path\to\data
CYCLOIDAL_RESULTS=C:\path\to\results
```

### **配置文件设置**

创建用户配置文件 `~/.cycloidal/config.yaml`:

```yaml
# 用户配置文件
user:
  name: "Your Name"
  email: "<EMAIL>"

paths:
  data_dir: "/path/to/data"
  results_dir: "/path/to/results"
  temp_dir: "/tmp/cycloidal"

computing:
  num_cores: 8
  use_gpu: false
  gpu_device: 0

logging:
  level: "INFO"
  file: "cycloidal.log"

visualization:
  backend: "plotly"
  dpi: 300
  format: "png"
```

---

## 🧪 **安装验证**

### **基本功能测试**

```python
# test_installation.py
import numpy as np
from cycloidal_rotor_suite.core.geometry import RotorGeometry
from cycloidal_rotor_suite.core.aerodynamics import BEMTSolver
from cycloidal_rotor_suite.core.postprocessing import PostProcessor

def test_basic_functionality():
    """测试基本功能"""
    # 创建转子几何
    rotor = RotorGeometry(
        radius=1.0,
        hub_radius=0.1,
        blade_count=3
    )
    
    # 创建求解器
    solver = BEMTSolver()
    
    # 创建后处理器
    post_processor = PostProcessor()
    
    print("✅ 基本功能测试通过")

if __name__ == "__main__":
    test_basic_functionality()
```

### **性能基准测试**

```bash
# 运行性能测试
cycloidal-rotor benchmark --quick

# 详细性能测试
cycloidal-rotor benchmark --full

# 生成性能报告
cycloidal-rotor benchmark --report
```

### **示例运行测试**

```bash
# 运行基本示例
cycloidal-rotor run examples/basic_hover.yaml

# 运行所有示例
cycloidal-rotor run-examples --all

# 检查结果
ls results/
```

---

## 🔧 **故障排除**

### **常见问题**

#### **问题1: 导入错误**
```
ImportError: No module named 'cycloidal_rotor_suite'
```

**解决方案**:
```bash
# 检查虚拟环境是否激活
which python

# 重新安装
pip uninstall cycloidal-rotor-suite
pip install cycloidal-rotor-suite

# 检查Python路径
python -c "import sys; print(sys.path)"
```

#### **问题2: 依赖冲突**
```
ERROR: pip's dependency resolver does not currently consider all the ways...
```

**解决方案**:
```bash
# 创建新的虚拟环境
python -m venv fresh_env
source fresh_env/bin/activate

# 逐步安装依赖
pip install numpy scipy matplotlib
pip install cycloidal-rotor-suite
```

#### **问题3: GPU支持问题**
```
CUDA not available or incompatible version
```

**解决方案**:
```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的CuPy版本
pip install cupy-cuda11x  # 根据CUDA版本调整

# 禁用GPU加速（如果不需要）
export CYCLOIDAL_USE_GPU=false
```

### **获取帮助**

- **文档**: [https://cycloidal-rotor-suite.readthedocs.io](https://cycloidal-rotor-suite.readthedocs.io)
- **GitHub Issues**: [https://github.com/your-org/cycloidal_rotor_suite_refactored/issues](https://github.com/your-org/cycloidal_rotor_suite_refactored/issues)
- **讨论论坛**: [https://github.com/your-org/cycloidal_rotor_suite_refactored/discussions](https://github.com/your-org/cycloidal_rotor_suite_refactored/discussions)
- **邮件列表**: <EMAIL>

---

## 📚 **下一步**

安装完成后，建议按以下顺序学习：

1. **快速入门**: 阅读 [`docs/user_guide/quick_start.md`](quick_start.md)
2. **基础教程**: 查看 [`docs/user_guide/tutorials/`](tutorials/)
3. **示例代码**: 运行 [`examples/`](../../examples/) 目录中的示例
4. **API文档**: 参考 [`docs/api/`](../api/) 了解详细接口
5. **高级功能**: 学习 [`docs/methodology/`](../methodology/) 中的理论基础

---

**安装指南版本**: v2.0  
**最后更新**: 2025-01-08  
**维护者**: Cycloidal Rotor Suite Development Team
