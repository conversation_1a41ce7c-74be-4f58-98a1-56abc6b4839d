"""
性能监控器
=========

实时监控计算性能和系统资源使用情况
"""

import time
import psutil
import threading
import numpy as np
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from collections import deque
import logging


@dataclass
class PerformanceSnapshot:
    """性能快照数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    computation_time: float
    iteration_count: int
    convergence_residual: float


class PerformanceMonitor:
    """
    性能监控器
    
    监控仿真计算的性能指标：
    - CPU使用率
    - 内存使用情况
    - 计算时间
    - 收敛性能
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化性能监控器
        
        Args:
            config: 监控配置参数
        """
        self.config = config or {}
        
        # 监控配置
        self.sampling_interval = self.config.get('sampling_interval', 1.0)  # 秒
        self.max_history_size = self.config.get('max_history_size', 1000)
        self.enable_real_time = self.config.get('enable_real_time', True)
        
        # 数据存储
        self.performance_history = deque(maxlen=self.max_history_size)
        self.computation_times = deque(maxlen=self.max_history_size)
        self.memory_usage = deque(maxlen=self.max_history_size)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.start_time = None
        
        # 计算统计
        self.total_iterations = 0
        self.total_computation_time = 0.0
        self.peak_memory_usage = 0.0
        
        print("✅ 性能监控器初始化完成")
        print(f"   采样间隔: {self.sampling_interval}s")
        print(f"   历史记录: {self.max_history_size}条")
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.is_monitoring:
            print("⚠️ 性能监控已在运行")
            return
        
        self.is_monitoring = True
        self.start_time = time.time()
        
        if self.enable_real_time:
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
        
        print("🔄 性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        print("⏹️ 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                snapshot = self._take_snapshot()
                self.performance_history.append(snapshot)
                
                # 更新峰值内存使用
                if snapshot.memory_used_mb > self.peak_memory_usage:
                    self.peak_memory_usage = snapshot.memory_used_mb
                
                time.sleep(self.sampling_interval)
                
            except Exception as e:
                logging.error(f"性能监控错误: {e}")
                break
    
    def _take_snapshot(self) -> PerformanceSnapshot:
        """获取性能快照"""
        current_time = time.time()
        
        # 系统资源使用
        cpu_percent = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        memory_percent = memory_info.percent
        memory_used_mb = memory_info.used / (1024 * 1024)
        
        # 计算时间
        computation_time = current_time - self.start_time if self.start_time else 0.0
        
        # 收敛残差（模拟值）
        convergence_residual = 1e-6 * np.random.random()
        
        return PerformanceSnapshot(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            computation_time=computation_time,
            iteration_count=self.total_iterations,
            convergence_residual=convergence_residual
        )
    
    def record_iteration(self, iteration_time: float, residual: float = 0.0):
        """
        记录迭代性能
        
        Args:
            iteration_time: 迭代时间
            residual: 收敛残差
        """
        self.total_iterations += 1
        self.total_computation_time += iteration_time
        self.computation_times.append(iteration_time)
        
        # 记录当前快照
        if not self.enable_real_time:
            snapshot = self._take_snapshot()
            snapshot.convergence_residual = residual
            self.performance_history.append(snapshot)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_history:
            return {"error": "无性能数据"}
        
        # 计算统计信息
        cpu_usage = [s.cpu_percent for s in self.performance_history]
        memory_usage = [s.memory_used_mb for s in self.performance_history]
        computation_times = list(self.computation_times)
        
        summary = {
            'monitoring_duration': time.time() - self.start_time if self.start_time else 0.0,
            'total_iterations': self.total_iterations,
            'total_computation_time': self.total_computation_time,
            'average_iteration_time': np.mean(computation_times) if computation_times else 0.0,
            'cpu_usage': {
                'average': np.mean(cpu_usage),
                'max': np.max(cpu_usage),
                'min': np.min(cpu_usage)
            },
            'memory_usage': {
                'average_mb': np.mean(memory_usage),
                'peak_mb': self.peak_memory_usage,
                'current_mb': memory_usage[-1] if memory_usage else 0.0
            },
            'performance_efficiency': self._calculate_efficiency(),
            'data_points': len(self.performance_history)
        }
        
        return summary
    
    def _calculate_efficiency(self) -> float:
        """计算性能效率"""
        if self.total_iterations == 0 or self.total_computation_time == 0:
            return 0.0
        
        # 简化的效率计算：迭代数/计算时间
        efficiency = self.total_iterations / self.total_computation_time
        return efficiency
    
    def export_performance_data(self, output_path: str) -> bool:
        """
        导出性能数据
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否成功导出
        """
        try:
            import json
            
            # 准备导出数据
            export_data = {
                'summary': self.get_performance_summary(),
                'history': [
                    {
                        'timestamp': s.timestamp,
                        'cpu_percent': s.cpu_percent,
                        'memory_percent': s.memory_percent,
                        'memory_used_mb': s.memory_used_mb,
                        'computation_time': s.computation_time,
                        'iteration_count': s.iteration_count,
                        'convergence_residual': s.convergence_residual
                    }
                    for s in self.performance_history
                ]
            }
            
            # 保存到文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 性能数据已导出: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 性能数据导出失败: {e}")
            return False


class SystemMonitor:
    """
    系统监控器
    
    监控系统级别的资源使用情况
    """
    
    def __init__(self):
        """初始化系统监控器"""
        self.process = psutil.Process()
        print("✅ 系统监控器初始化完成")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            cpu_info = {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'cpu_percent': psutil.cpu_percent(interval=1)
            }
            
            memory_info = psutil.virtual_memory()._asdict()
            
            disk_info = psutil.disk_usage('/')._asdict()
            
            return {
                'cpu': cpu_info,
                'memory': memory_info,
                'disk': disk_info,
                'process_count': len(psutil.pids())
            }
            
        except Exception as e:
            return {'error': f'获取系统信息失败: {e}'}
    
    def get_process_info(self) -> Dict[str, Any]:
        """获取当前进程信息"""
        try:
            return {
                'pid': self.process.pid,
                'name': self.process.name(),
                'cpu_percent': self.process.cpu_percent(),
                'memory_info': self.process.memory_info()._asdict(),
                'memory_percent': self.process.memory_percent(),
                'num_threads': self.process.num_threads(),
                'create_time': self.process.create_time()
            }
            
        except Exception as e:
            return {'error': f'获取进程信息失败: {e}'}
    
    def check_resource_limits(self) -> Dict[str, bool]:
        """检查资源限制"""
        try:
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            return {
                'memory_warning': memory.percent > 80,
                'memory_critical': memory.percent > 95,
                'cpu_warning': cpu_percent > 80,
                'cpu_critical': cpu_percent > 95,
                'available_memory_gb': memory.available / (1024**3)
            }
            
        except Exception as e:
            return {'error': f'资源检查失败: {e}'}
