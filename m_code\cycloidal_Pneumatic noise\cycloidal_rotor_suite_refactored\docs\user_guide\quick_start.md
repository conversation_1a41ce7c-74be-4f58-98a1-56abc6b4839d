# 循环翼转子仿真套件快速入门
## Quick Start Guide for Cycloidal Rotor Simulation Suite

**版本**: v2.0  
**更新时间**: 2025-01-08  
**预计完成时间**: 30分钟  

---

## 🎯 **学习目标**

通过本快速入门指南，您将学会：
- 设置基本的循环翼转子仿真
- 运行BEMT气动分析
- 进行基础后处理和可视化
- 理解主要输出结果

---

## 📋 **前提条件**

- 已完成软件安装（参见[安装指南](installation_guide.md)）
- 基本的Python编程知识
- 对循环翼转子概念的基本了解

---

## 🚀 **第一个仿真：悬停状态分析**

### **步骤1: 创建工作目录**

```bash
# 创建项目目录
mkdir my_first_simulation
cd my_first_simulation

# 激活虚拟环境
source cycloidal_env/bin/activate  # Linux/macOS
# 或
cycloidal_env\Scripts\activate     # Windows
```

### **步骤2: 创建配置文件**

创建文件 `hover_config.yaml`:

```yaml
# 悬停状态配置文件
simulation:
  name: "basic_hover"
  description: "基础悬停状态分析"
  solver: "bemt"

rotor:
  radius: 1.0          # 转子半径 (m)
  hub_radius: 0.1      # 轮毂半径 (m)
  blade_count: 3       # 桨叶数
  blade_chord: 0.1     # 桨叶弦长 (m)
  blade_span: 0.8      # 桨叶展长 (m)

operating_conditions:
  rpm: 1000            # 转速 (RPM)
  collective_pitch: 5.0 # 总距角 (度)
  cyclic_pitch: 10.0   # 周期变距幅值 (度)
  phase_angle: 0.0     # 相位角 (度)
  altitude: 0.0        # 海拔高度 (m)
  temperature: 15.0    # 温度 (°C)

solver_settings:
  max_iterations: 100
  convergence_tolerance: 1e-6
  relaxation_factor: 0.5

output:
  save_results: true
  output_directory: "results"
  plot_results: true
```

### **步骤3: 运行仿真**

```python
# run_simulation.py
from cycloidal_rotor_suite import CycloidalRotorSuite
import yaml

def main():
    # 加载配置
    with open('hover_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 创建仿真套件实例
    suite = CycloidalRotorSuite(config)
    
    # 运行仿真
    print("🚀 开始仿真...")
    results = suite.run()
    
    # 显示基本结果
    print(f"✅ 仿真完成!")
    print(f"推力系数 CT: {results.thrust_coefficient:.4f}")
    print(f"功率系数 CP: {results.power_coefficient:.4f}")
    print(f"效率 η: {results.efficiency:.4f}")
    
    # 保存结果
    suite.save_results("results/hover_analysis.h5")
    print("📁 结果已保存到 results/hover_analysis.h5")

if __name__ == "__main__":
    main()
```

运行仿真：
```bash
python run_simulation.py
```

### **步骤4: 查看结果**

```python
# analyze_results.py
from cycloidal_rotor_suite.postprocessing import PostProcessor
import matplotlib.pyplot as plt

def analyze_results():
    # 创建后处理器
    post_processor = PostProcessor()
    
    # 加载结果
    results = post_processor.load_results("results/hover_analysis.h5")
    
    # 绘制径向载荷分布
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 推力分布
    ax1.plot(results.radial_positions, results.thrust_distribution)
    ax1.set_xlabel('径向位置 r/R')
    ax1.set_ylabel('推力分布 (N/m)')
    ax1.set_title('径向推力分布')
    ax1.grid(True)
    
    # 功率分布
    ax2.plot(results.radial_positions, results.power_distribution)
    ax2.set_xlabel('径向位置 r/R')
    ax2.set_ylabel('功率分布 (W/m)')
    ax2.set_title('径向功率分布')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('results/radial_distributions.png', dpi=300)
    plt.show()
    
    print("📊 图表已保存到 results/radial_distributions.png")

if __name__ == "__main__":
    analyze_results()
```

---

## 🔄 **第二个仿真：前飞状态分析**

### **步骤1: 创建前飞配置**

创建文件 `forward_flight_config.yaml`:

```yaml
simulation:
  name: "forward_flight"
  description: "前飞状态分析"
  solver: "bemt"

rotor:
  radius: 1.0
  hub_radius: 0.1
  blade_count: 3
  blade_chord: 0.1
  blade_span: 0.8

operating_conditions:
  rpm: 1200
  forward_speed: 20.0    # 前飞速度 (m/s)
  collective_pitch: 3.0
  cyclic_pitch: 12.0
  phase_angle: 90.0      # 相位角调整
  altitude: 0.0
  temperature: 15.0

solver_settings:
  max_iterations: 150
  convergence_tolerance: 1e-6
  relaxation_factor: 0.4

output:
  save_results: true
  output_directory: "results"
  plot_results: true
```

### **步骤2: 运行前飞仿真**

```python
# run_forward_flight.py
from cycloidal_rotor_suite import CycloidalRotorSuite
import yaml
import numpy as np

def run_forward_flight():
    # 加载配置
    with open('forward_flight_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 创建仿真套件
    suite = CycloidalRotorSuite(config)
    
    # 运行仿真
    print("🚀 开始前飞仿真...")
    results = suite.run()
    
    # 计算前飞性能参数
    advance_ratio = config['operating_conditions']['forward_speed'] / \
                   (config['operating_conditions']['rpm'] / 60 * 2 * np.pi * config['rotor']['radius'])
    
    print(f"✅ 前飞仿真完成!")
    print(f"前进比 μ: {advance_ratio:.3f}")
    print(f"推力系数 CT: {results.thrust_coefficient:.4f}")
    print(f"功率系数 CP: {results.power_coefficient:.4f}")
    print(f"推进效率 ηp: {results.propulsive_efficiency:.4f}")
    
    # 保存结果
    suite.save_results("results/forward_flight_analysis.h5")
    
    return results

if __name__ == "__main__":
    run_forward_flight()
```

---

## 📊 **结果对比分析**

### **性能对比**

```python
# compare_results.py
from cycloidal_rotor_suite.postprocessing import PostProcessor
import matplotlib.pyplot as plt
import numpy as np

def compare_hover_vs_forward():
    post_processor = PostProcessor()
    
    # 加载两个仿真结果
    hover_results = post_processor.load_results("results/hover_analysis.h5")
    forward_results = post_processor.load_results("results/forward_flight_analysis.h5")
    
    # 创建对比图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 推力系数对比
    conditions = ['悬停', '前飞']
    ct_values = [hover_results.thrust_coefficient, forward_results.thrust_coefficient]
    ax1.bar(conditions, ct_values, color=['blue', 'red'], alpha=0.7)
    ax1.set_ylabel('推力系数 CT')
    ax1.set_title('推力系数对比')
    ax1.grid(True, alpha=0.3)
    
    # 功率系数对比
    cp_values = [hover_results.power_coefficient, forward_results.power_coefficient]
    ax2.bar(conditions, cp_values, color=['blue', 'red'], alpha=0.7)
    ax2.set_ylabel('功率系数 CP')
    ax2.set_title('功率系数对比')
    ax2.grid(True, alpha=0.3)
    
    # 径向推力分布对比
    r_positions = hover_results.radial_positions
    ax3.plot(r_positions, hover_results.thrust_distribution, 'b-', label='悬停', linewidth=2)
    ax3.plot(r_positions, forward_results.thrust_distribution, 'r-', label='前飞', linewidth=2)
    ax3.set_xlabel('径向位置 r/R')
    ax3.set_ylabel('推力分布 (N/m)')
    ax3.set_title('径向推力分布对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 效率对比
    efficiency_values = [hover_results.efficiency, forward_results.propulsive_efficiency]
    ax4.bar(conditions, efficiency_values, color=['blue', 'red'], alpha=0.7)
    ax4.set_ylabel('效率')
    ax4.set_title('效率对比')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 对比分析图表已保存到 results/performance_comparison.png")

if __name__ == "__main__":
    compare_hover_vs_forward()
```

---

## 🎛️ **使用命令行界面**

### **基本命令**

```bash
# 查看帮助
cycloidal-rotor --help

# 运行仿真
cycloidal-rotor simulate hover_config.yaml

# 批量运行
cycloidal-rotor simulate *.yaml

# 后处理
cycloidal-rotor analyze results/hover_analysis.h5

# 生成报告
cycloidal-rotor report results/ --format pdf
```

### **参数扫描**

```bash
# 转速扫描
cycloidal-rotor sweep hover_config.yaml --parameter rpm --range 800:1200:100

# 多参数扫描
cycloidal-rotor sweep hover_config.yaml \
  --parameter rpm --range 800:1200:100 \
  --parameter collective_pitch --range 0:10:2
```

---

## 🔍 **理解输出结果**

### **主要性能参数**

- **推力系数 (CT)**: $C_T = \frac{T}{\rho A (\Omega R)^2}$
  - 无量纲推力，表征转子产生推力的能力
  - 典型值：0.005-0.015

- **功率系数 (CP)**: $C_P = \frac{P}{\rho A (\Omega R)^3}$
  - 无量纲功率，表征转子功率消耗
  - 典型值：0.0005-0.002

- **效率 (η)**: $\eta = \frac{T \cdot V}{P}$
  - 推进效率，表征能量利用效率
  - 典型值：0.6-0.8

### **载荷分布**

- **径向推力分布**: 沿桨叶展向的推力分布
- **弦向压力分布**: 沿桨叶弦向的压力分布
- **周向载荷变化**: 随方位角变化的载荷

### **流场信息**

- **诱导速度分布**: 转子盘面的诱导速度场
- **攻角分布**: 沿桨叶的攻角变化
- **马赫数分布**: 局部马赫数分布

---

## 🚨 **常见问题解决**

### **收敛问题**

```yaml
# 调整求解器设置
solver_settings:
  max_iterations: 200        # 增加迭代次数
  convergence_tolerance: 1e-5 # 放宽收敛标准
  relaxation_factor: 0.3     # 减小松弛因子
```

### **非物理结果**

```yaml
# 检查输入参数合理性
operating_conditions:
  rpm: 1000          # 确保转速合理
  collective_pitch: 5.0  # 检查俯仰角范围
  cyclic_pitch: 10.0     # 检查周期变距幅值
```

### **性能优化**

```python
# 使用并行计算
from cycloidal_rotor_suite import set_num_threads
set_num_threads(8)  # 设置线程数

# 启用GPU加速（如果可用）
config['solver_settings']['use_gpu'] = True
```

---

## 📚 **下一步学习**

完成快速入门后，建议继续学习：

1. **详细教程**: [`docs/user_guide/tutorials/`](tutorials/)
   - 高级配置选项
   - 多保真度建模
   - 声学分析

2. **示例库**: [`examples/`](../../examples/)
   - 复杂几何建模
   - 参数优化
   - 验证案例

3. **API文档**: [`docs/api/`](../api/)
   - 编程接口详解
   - 自定义扩展
   - 高级功能

4. **理论基础**: [`docs/methodology/`](../methodology/)
   - 数学原理
   - 数值方法
   - 验证方法

---

## 💡 **小贴士**

- **保存配置**: 为不同的仿真场景创建配置模板
- **版本控制**: 使用Git管理仿真配置和结果
- **文档记录**: 记录仿真目的、参数选择和结果分析
- **结果备份**: 定期备份重要的仿真结果
- **性能监控**: 关注仿真的收敛性和计算效率

---

**快速入门指南版本**: v2.0  
**最后更新**: 2025-01-08  
**预计学习时间**: 30分钟  
**难度级别**: 初级
