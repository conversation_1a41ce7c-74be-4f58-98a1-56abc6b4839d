# FW-H高保真度声学模型理论与GPU并行实现
## FW-H High Fidelity Acoustic Model Theory and GPU Parallel Implementation

**文档版本**: v3.0 (高性能计算版)
**更新时间**: 2025-01-08
**保真度级别**: 高保真度 (High Fidelity)
**适用范围**: 精确噪声预测、声学优化设计、环境影响评估
**计算平台**: CPU多核 + GPU加速
**学术标准**: 适合直接引用于学术论文

---

## 概述

Ffowcs Williams-Hawkings(FW-H)方程是现代气动声学理论的基石，它将Lighthill的声学类比理论扩展到运动边界的情况，为旋翼、螺旋桨等运动叶片的噪声预测提供了理论基础。对于循环翼转子这种具有复杂运动模式和强非定常载荷特性的系统，FW-H方法是目前最为有效的噪声预测工具。

**FW-H方程的物理本质**在于将复杂的气动噪声问题转化为已知声源的声辐射问题。通过引入广义函数，FW-H方程能够将流场分为固体区域和流体区域，并将噪声源项集中在运动表面上。这种处理方式使得我们能够基于相对容易获得的表面载荷信息来预测远场噪声。

**循环翼转子的声学特征**与传统旋翼存在显著差异。由于桨叶攻角的大幅度周期性变化，循环翼转子产生的载荷脉动具有独特的频谱特征，主要表现为强烈的谐波成分。这种载荷特性直接影响了噪声的频谱分布和指向性，使得循环翼转子的噪声预测比传统旋翼更加复杂。

**声学类比方法的优势**：FW-H方法的主要优势在于其能够将气动分析和声学分析相分离。气动分析可以采用适合的CFD方法或工程方法（如BEMT、UVLM等），而声学分析则基于气动分析的结果进行。这种分离使得我们能够根据精度要求和计算资源来选择合适的气动分析方法。

**噪声源的分类理解**：FW-H理论将噪声源分为三类：厚度噪声（单极子源）、载荷噪声（偶极子源）和四极子噪声。对于循环翼转子，载荷噪声通常是主导的，这是因为其强烈的载荷脉动；厚度噪声相对较弱，但在高速运动时可能变得重要；四极子噪声在低马赫数情况下通常可以忽略。

**理论发展层次**：

- **基础层次**：经典Lighthill声学类比理论
- **扩展层次**：FW-H方程的推导和物理意义
- **实用层次**：Farassat积分公式的数值实现
- **应用层次**：针对循环翼转子的特殊处理

**工程应用价值**：
FW-H方法不仅能够预测噪声的总体水平，还能够提供详细的频谱信息和指向性特征。这些信息对于循环翼转子的低噪声设计、噪声控制策略制定以及环境影响评估都具有重要价值。通过分析不同噪声源的贡献，设计师可以有针对性地优化设计参数，实现噪声的有效控制。

---

## 1. 声学基础理论 (Acoustic Foundation)

### 1.1 线性声学波动方程

**从连续性方程出发**：
$$\frac{\partial \rho}{\partial t} + \nabla \cdot (\rho \vec{v}) = 0$$

**动量方程**：
$$\rho \frac{\partial \vec{v}}{\partial t} + \rho (\vec{v} \cdot \nabla)\vec{v} = -\nabla p$$

**状态方程**：
$$p = p(\rho, s)$$

其中$s$为熵。

**线性化假设**：
设$\rho = \rho_0 + \rho'$，$p = p_0 + p'$，$\vec{v} = \vec{v}'$，其中下标0表示平均值，上标'表示扰动量。

**线性化连续性方程**：
$$\frac{\partial \rho'}{\partial t} + \rho_0 \nabla \cdot \vec{v}' = 0$$

**线性化动量方程**：
$$\rho_0 \frac{\partial \vec{v}'}{\partial t} = -\nabla p'$$

**等熵假设下的状态方程**：
$$p' = c_0^2 \rho'$$

其中$c_0 = \sqrt{\frac{\partial p}{\partial \rho}\big|_{s=const}}$为声速。

**波动方程推导**：
对连续性方程求时间导数：
$$\frac{\partial^2 \rho'}{\partial t^2} + \rho_0 \nabla \cdot \frac{\partial \vec{v}'}{\partial t} = 0$$

将动量方程代入：
$$\frac{\partial^2 \rho'}{\partial t^2} - c_0^2 \nabla^2 \rho' = 0$$

或等价地：
$$\frac{\partial^2 p'}{\partial t^2} - c_0^2 \nabla^2 p' = 0$$

这是经典的声学波动方程。

### 1.2 Lighthill声学类比

**Lighthill方程推导**：

从完整的Navier-Stokes方程出发：
$$\frac{\partial \rho}{\partial t} + \frac{\partial}{\partial x_i}(\rho v_i) = 0$$
$$\frac{\partial}{\partial t}(\rho v_i) + \frac{\partial}{\partial x_j}(\rho v_i v_j + p\delta_{ij} - \tau_{ij}) = 0$$

重新整理为：
$$\frac{\partial^2 \rho}{\partial t^2} - c_0^2 \frac{\partial^2 \rho}{\partial x_i^2} = \frac{\partial^2 T_{ij}}{\partial x_i \partial x_j}$$

其中Lighthill应力张量：
$$T_{ij} = \rho v_i v_j + (p - c_0^2\rho)\delta_{ij} - \tau_{ij}$$

**物理意义**：
- 左边：线性声学算子
- 右边：非线性源项（四极子源）

### 1.3 广义函数理论基础

**Heaviside函数**：
$$H(f) = \begin{cases}
1, & f > 0 \\
0, & f < 0
\end{cases}$$

**Dirac函数**：
$$\delta(f) = \frac{dH(f)}{df}$$

**性质**：
$$\int_{-\infty}^{\infty} \delta(f) g(f) df = g(0)$$

**多维情况**：
$$\delta(\vec{f}) = \delta(f_1)\delta(f_2)\delta(f_3)$$

---

## 2. FW-H方程完整推导 (Complete FW-H Derivation)

### 2.1 控制体分析

**定义运动表面**：
设$f(\vec{x}, t) = 0$定义运动的物体表面，其中：
- $f > 0$：流体区域
- $f < 0$：固体区域

**表面运动速度**：
$$v_n = -\frac{\partial f/\partial t}{|\nabla f|}$$

其中$v_n$为表面法向速度。

### 2.2 广义导数

**密度的广义导数**：
$$\frac{\partial}{\partial t}[\rho H(f)] = \frac{\partial \rho}{\partial t}H(f) + \rho v_n \delta(f)$$

**动量的广义导数**：
$$\frac{\partial}{\partial x_i}[(\rho v_i + p\delta_{ij})H(f)] = \frac{\partial}{\partial x_i}(\rho v_i + p\delta_{ij})H(f) + (\rho v_i + p\delta_{ij})n_j \delta(f)$$

### 2.3 FW-H方程推导

**从Lighthill方程出发**：
$$\frac{\partial^2 \rho}{\partial t^2} - c_0^2 \nabla^2 \rho = \frac{\partial^2 T_{ij}}{\partial x_i \partial x_j}$$

**引入广义函数**：
将流场分为固体区域和流体区域：
$$\rho(\vec{x}, t) = \rho(\vec{x}, t)H(f) + \rho_s(\vec{x}, t)[1-H(f)]$$

其中$\rho_s$为固体密度。

**应用广义导数规则**：
$$\frac{\partial^2}{\partial t^2}[\rho H(f)] - c_0^2 \nabla^2[\rho H(f)] = \frac{\partial^2}{\partial x_i \partial x_j}[T_{ij} H(f)]$$

**展开各项**：

**时间导数项**：
$$\frac{\partial^2}{\partial t^2}[\rho H(f)] = \frac{\partial^2 \rho}{\partial t^2}H(f) + 2\frac{\partial \rho}{\partial t}v_n\delta(f) + \rho\frac{\partial}{\partial t}[v_n\delta(f)]$$

**空间导数项**：
$$\nabla^2[\rho H(f)] = \nabla^2\rho \cdot H(f) + 2\nabla\rho \cdot \nabla H(f) + \rho\nabla^2 H(f)$$

**源项**：
$$\frac{\partial^2}{\partial x_i \partial x_j}[T_{ij} H(f)] = \frac{\partial^2 T_{ij}}{\partial x_i \partial x_j}H(f) + \text{表面项}$$

### 2.4 最终FW-H方程

**标准形式**：
$$\frac{1}{c_0^2}\frac{\partial^2 p'}{\partial t^2} - \nabla^2 p' = \frac{\partial}{\partial t}[\rho_0 v_n \delta(f)] - \frac{\partial}{\partial x_i}[l_i \delta(f)] + \frac{\partial^2}{\partial x_i \partial x_j}[T_{ij} H(f)]$$

其中：
- **厚度噪声项**：$\frac{\partial}{\partial t}[\rho_0 v_n \delta(f)]$
- **载荷噪声项**：$-\frac{\partial}{\partial x_i}[l_i \delta(f)]$
- **四极子噪声项**：$\frac{\partial^2}{\partial x_i \partial x_j}[T_{ij} H(f)]$

**表面力密度**：
$$l_i = [(p - p_0)\delta_{ij} - \tau_{ij}]n_j$$

---

## 3. 物理意义解释 (Physical Interpretation)

### 3.1 厚度噪声 (Thickness Noise)

**物理机制**：
由于物体的存在导致流体被排开，产生的声学扰动。

**数学表达**：
$$p'_{thickness} = \frac{\partial}{\partial t}[\rho_0 v_n \delta(f)] * G$$

**特点**：
- 与物体体积变化率相关
- 主要在低频段贡献
- 对于刚体，主要由运动产生

### 3.2 载荷噪声 (Loading Noise)

**物理机制**：
物体表面的压力和粘性力脉动向远场辐射声波。

**数学表达**：
$$p'_{loading} = -\frac{\partial}{\partial x_i}[l_i \delta(f)] * G$$

**特点**：
- 与表面力脉动相关
- 通常是主要噪声源
- 频率特性与载荷脉动相关

### 3.3 四极子噪声 (Quadrupole Noise)

**物理机制**：
流体区域内的非线性效应产生的声源。

**数学表达**：
$$p'_{quadrupole} = \frac{\partial^2}{\partial x_i \partial x_j}[T_{ij} H(f)] * G$$

**特点**：
- 与流场非线性相关
- 高速流动中重要
- 计算复杂度最高

---

## 4. Green函数解法 (Green's Function Solution)

### 4.1 自由空间Green函数

**时域Green函数**：
$$G(\vec{x}, t|\vec{y}, \tau) = \frac{\delta(t - \tau - |\vec{x} - \vec{y}|/c_0)}{4\pi|\vec{x} - \vec{y}|}$$

**物理意义**：
点源在$(\vec{y}, \tau)$时刻发出的声波在$(\vec{x}, t)$时刻的响应。

### 4.2 积分解

**厚度噪声积分**：
$$p'_T(\vec{x}, t) = \frac{1}{4\pi} \int_S \frac{\rho_0 \dot{v}_n}{r(1-M_r)} \bigg|_{\tau} dS$$

**载荷噪声积分**：
$$p'_L(\vec{x}, t) = \frac{1}{4\pi} \int_S \frac{\dot{l}_r}{r(1-M_r)} + \frac{l_r}{r^2(1-M_r)^2} \bigg|_{\tau} dS$$

其中：
- $r = |\vec{x} - \vec{y}|$：源点到观测点距离
- $M_r = \vec{M} \cdot \hat{r}$：径向Mach数
- $\tau = t - r/c_0$：延迟时间
- 上标点表示对延迟时间的导数

### 4.3 Doppler效应

**频率变换**：
$$f_{observed} = \frac{f_{source}}{1 - M_r}$$

**幅值变换**：
$$A_{observed} = \frac{A_{source}}{(1 - M_r)^2}$$

---

## 5. 数值实现方法 (Numerical Implementation)

### 5.1 表面积分离散化

**面板离散化**：
$$\int_S (\cdot) dS \approx \sum_{i=1}^{N} (\cdot)_i \Delta S_i$$

**时间插值**：
由于延迟时间$\tau$通常不在时间网格点上，需要插值：
$$f(\tau) = \sum_{j} f(t_j) L_j(\tau)$$

其中$L_j$为Lagrange插值基函数。

### 5.2 延迟时间计算

**迭代求解**：
延迟时间$\tau$满足：
$$\tau = t - \frac{|\vec{x} - \vec{y}(\tau)|}{c_0}$$

**Newton-Raphson方法**：
$$\tau^{(k+1)} = \tau^{(k)} - \frac{F(\tau^{(k)})}{F'(\tau^{(k)})}$$

其中：
$$F(\tau) = \tau - t + \frac{|\vec{x} - \vec{y}(\tau)|}{c_0}$$

### 5.3 数值稳定化

**时间步长限制**：
$$\Delta t \leq \frac{\Delta x_{min}}{c_0 + V_{max}}$$

**空间分辨率要求**：
$$\Delta x \leq \frac{\lambda_{min}}{10}$$

其中$\lambda_{min}$为最小声波波长。

---

## 6. 循环翼转子应用 (Cycloidal Rotor Application)

### 6.1 几何建模

**桨叶表面参数化**：
$$\vec{r}_{blade}(s, t) = \vec{r}_{hub} + s \cdot \vec{e}_{span} + \text{profile}(s, t)$$

**运动描述**：
$$\vec{v}_{blade} = \vec{V}_{translation} + \vec{\Omega} \times \vec{r} + \vec{v}_{pitch}(t)$$

### 6.2 载荷计算

**压力载荷**：
$$l_{p,i} = (p - p_0) n_i$$

**粘性载荷**：
$$l_{\tau,i} = -\tau_{ij} n_j$$

**总载荷**：
$$l_i = l_{p,i} + l_{\tau,i}$$

### 6.3 频域分析

**Fourier变换**：
$$\tilde{p}(\omega) = \int_{-\infty}^{\infty} p(t) e^{-i\omega t} dt$$

**功率谱密度**：
$$S_{pp}(\omega) = |\tilde{p}(\omega)|^2$$

**总声功率级**：
$$PWL = 10\log_{10}\left(\frac{P_{acoustic}}{P_{ref}}\right)$$

---

## 7. 验证与确认 (Verification & Validation)

### 7.1 解析解验证

**单极子源**：
$$p'(r, t) = \frac{Q_0 \delta(t - r/c_0)}{4\pi r}$$

**偶极子源**：
$$p'(r, t) = \frac{F_0 \cos\theta}{4\pi r} \frac{\partial}{\partial t}\delta(t - r/c_0)$$

### 7.2 实验数据对比

**声压级对比**：
$$SPL = 20\log_{10}\left(\frac{p_{rms}}{p_{ref}}\right)$$

**指向性对比**：
$$D(\theta, \phi) = \frac{p^2(\theta, \phi)}{\langle p^2 \rangle}$$

### 7.3 网格收敛性

**Richardson外推**：
$$p_{exact} = p_h + \frac{p_h - p_{2h}}{2^p - 1}$$

---

## 8. 结论 (Conclusions)

本文建立了循环翼转子FW-H声学分析的完整理论框架，从基本的波动方程出发，严格推导了FW-H方程的数学表述，阐明了各项的物理意义，并给出了数值实现方法。该理论框架为循环翼转子的气动噪声预测提供了坚实的科学基础。

---

## 参考文献 (References)

1. Ffowcs Williams, J.E. and Hawkings, D.L., "Sound Generation by Turbulence and Surfaces in Arbitrary Motion", Philosophical Transactions of the Royal Society, 1969.
2. Lighthill, M.J., "On Sound Generated Aerodynamically", Proceedings of the Royal Society, 1952.
3. Farassat, F., "Derivation of Formulations 1 and 1A of Farassat", NASA TM-2007-214853, 2007.
4. Brentner, K.S. and Farassat, F., "Analytical Comparison of the Acoustic Analogy and Kirchhoff Formulation", AIAA Journal, 1998.
5. Leishman, J.G., "Principles of Helicopter Aerodynamics", Cambridge University Press, 2006.

---

**文档状态**: ✅ 完成  
**审核状态**: 待审核  
**适用范围**: 学术研究与工程应用
