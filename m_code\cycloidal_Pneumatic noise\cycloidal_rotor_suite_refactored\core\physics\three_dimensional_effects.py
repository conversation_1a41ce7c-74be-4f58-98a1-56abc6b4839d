"""
三维效应模块 - 完整实现版本
============================

基于adevice_complement5.md规范实现完整的三维效应模型，包括：
- 非线性诱导速度计算
- 桨叶间干扰效应
- Du & Selig径向流动模型
- Snel失速延迟模型
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from abc import ABC, abstractmethod
import warnings


class ThreeDimensionalEffectsBase(ABC):
    """三维效应基类"""
    
    @abstractmethod
    def calculate_3d_correction(self, *args, **kwargs) -> float:
        """计算三维修正因子"""
        pass


class NonlinearInducedVelocityModel(ThreeDimensionalEffectsBase):
    """
    非线性诱导速度模型（基于adevice_complement5.md规范）
    
    实现高阶诱导效应计算，包括：
    - 涡丝相互作用
    - 非线性诱导速度场
    - 尾迹几何影响
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化非线性诱导速度模型
        
        Args:
            config: 配置参数
        """
        self.enable_vortex_interaction = config.get('enable_vortex_interaction', True)
        self.enable_wake_geometry_effects = config.get('enable_wake_geometry_effects', True)
        self.nonlinearity_factor = config.get('nonlinearity_factor', 1.0)
        
        # 数值参数
        self.convergence_tolerance = config.get('convergence_tolerance', 1e-6)
        self.max_iterations = config.get('max_iterations', 50)
        
        print(f"✅ 非线性诱导速度模型初始化完成")
        print(f"   涡丝相互作用: {'启用' if self.enable_vortex_interaction else '禁用'}")
        print(f"   尾迹几何效应: {'启用' if self.enable_wake_geometry_effects else '禁用'}")
    
    def calculate_3d_correction(self, r: float, circulation: float, 
                              blade_positions: np.ndarray, **kwargs) -> float:
        """
        计算三维修正因子
        
        Args:
            r: 径向位置 [m]
            circulation: 环量 [m²/s]
            blade_positions: 桨叶位置数组
            **kwargs: 额外参数
            
        Returns:
            correction_factor: 三维修正因子
        """
        # 基础线性诱导速度
        v_linear = self._calculate_linear_induced_velocity(r, circulation)
        
        # 非线性修正
        if self.enable_vortex_interaction:
            v_nonlinear = self._calculate_nonlinear_induced_velocity(
                r, circulation, blade_positions, **kwargs
            )
        else:
            v_nonlinear = v_linear
        
        # 尾迹几何修正
        if self.enable_wake_geometry_effects:
            wake_correction = self._calculate_wake_geometry_correction(r, **kwargs)
            v_nonlinear *= wake_correction
        
        # 计算修正因子
        if abs(v_linear) > 1e-6:
            correction_factor = v_nonlinear / v_linear
        else:
            correction_factor = 1.0
        
        return correction_factor
    
    def _calculate_linear_induced_velocity(self, r: float, circulation: float) -> float:
        """计算线性诱导速度"""
        # 简化的动量理论
        if abs(circulation) < 1e-6:
            return 0.0
        
        # 基于环量的诱导速度
        v_induced = circulation / (2 * np.pi * r)
        
        return v_induced
    
    def _calculate_nonlinear_induced_velocity(self, r: float, circulation: float,
                                            blade_positions: np.ndarray, **kwargs) -> float:
        """
        计算非线性诱导速度
        
        Args:
            r: 径向位置 [m]
            circulation: 环量 [m²/s]
            blade_positions: 桨叶位置数组
            **kwargs: 额外参数
            
        Returns:
            v_nonlinear: 非线性诱导速度 [m/s]
        """
        # 基础线性速度
        v_base = self._calculate_linear_induced_velocity(r, circulation)
        
        # 涡丝相互作用修正
        interaction_factor = self._calculate_vortex_interaction_factor(
            r, blade_positions, **kwargs
        )
        
        # 非线性增强
        nonlinear_enhancement = 1.0 + self.nonlinearity_factor * (
            0.1 * interaction_factor + 0.05 * (circulation / 1.0)**2
        )
        
        v_nonlinear = v_base * nonlinear_enhancement
        
        return v_nonlinear
    
    def _calculate_vortex_interaction_factor(self, r: float, blade_positions: np.ndarray,
                                           **kwargs) -> float:
        """计算涡丝相互作用因子"""
        if len(blade_positions) < 2:
            return 0.0
        
        # 计算桨叶间的相互影响
        interaction_sum = 0.0
        current_position = np.array([r, 0, 0])  # 简化为径向位置
        
        for pos in blade_positions:
            distance = np.linalg.norm(current_position - pos)
            if distance > 1e-6:
                # 基于距离的相互作用强度
                interaction_strength = 1.0 / (1.0 + distance)
                interaction_sum += interaction_strength
        
        # 归一化
        if len(blade_positions) > 1:
            interaction_factor = interaction_sum / (len(blade_positions) - 1)
        else:
            interaction_factor = 0.0
        
        return interaction_factor
    
    def _calculate_wake_geometry_correction(self, r: float, **kwargs) -> float:
        """计算尾迹几何修正"""
        # 获取尾迹参数
        wake_age = kwargs.get('wake_age', 1.0)  # 尾迹年龄
        wake_contraction = kwargs.get('wake_contraction', 0.9)  # 尾迹收缩
        
        # 尾迹几何对诱导速度的影响
        age_factor = 1.0 - 0.1 * np.exp(-wake_age)  # 年龄效应
        contraction_factor = wake_contraction  # 收缩效应
        
        correction = age_factor * contraction_factor
        
        return max(correction, 0.5)  # 限制最小值


class BladeInteractionModel(ThreeDimensionalEffectsBase):
    """
    桨叶间干扰效应模型（基于adevice_complement5.md规范）
    
    实现桨叶相互影响建模，包括：
    - 桨叶间气动干扰
    - 尾迹相互作用
    - 相位延迟效应
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化桨叶间干扰模型
        
        Args:
            config: 配置参数
        """
        self.blade_count = config.get('blade_count', 3)
        self.interaction_strength = config.get('interaction_strength', 0.1)
        self.phase_delay_factor = config.get('phase_delay_factor', 0.2)
        
        # 干扰范围参数
        self.interaction_radius = config.get('interaction_radius', 2.0)  # 相对半径
        self.angular_influence_range = config.get('angular_influence_range', np.pi/3)  # 角度范围
        
        print(f"✅ 桨叶间干扰模型初始化完成")
        print(f"   桨叶数量: {self.blade_count}")
        print(f"   干扰强度: {self.interaction_strength:.3f}")
    
    def calculate_3d_correction(self, blade_index: int, azimuth_angle: float,
                              blade_loads: List[float], **kwargs) -> float:
        """
        计算桨叶间干扰修正因子
        
        Args:
            blade_index: 当前桨叶索引
            azimuth_angle: 方位角 [rad]
            blade_loads: 各桨叶载荷列表
            **kwargs: 额外参数
            
        Returns:
            correction_factor: 干扰修正因子
        """
        if len(blade_loads) != self.blade_count:
            warnings.warn(f"桨叶载荷数量({len(blade_loads)})与配置不符({self.blade_count})")
            return 1.0
        
        total_interference = 0.0
        
        # 计算其他桨叶的干扰影响
        for i, load in enumerate(blade_loads):
            if i == blade_index:
                continue  # 跳过自身
            
            # 计算桨叶间的角度差
            angular_separation = 2 * np.pi * abs(i - blade_index) / self.blade_count
            
            # 计算干扰强度
            interference = self._calculate_interference_strength(
                angular_separation, azimuth_angle, load, **kwargs
            )
            
            total_interference += interference
        
        # 转换为修正因子
        correction_factor = 1.0 + self.interaction_strength * total_interference
        
        return max(correction_factor, 0.5)  # 限制最小值
    
    def _calculate_interference_strength(self, angular_separation: float,
                                       azimuth_angle: float, source_load: float,
                                       **kwargs) -> float:
        """
        计算干扰强度
        
        Args:
            angular_separation: 角度分离 [rad]
            azimuth_angle: 方位角 [rad]
            source_load: 源桨叶载荷
            **kwargs: 额外参数
            
        Returns:
            interference_strength: 干扰强度
        """
        # 距离衰减
        distance_factor = np.exp(-angular_separation / self.angular_influence_range)
        
        # 载荷影响
        load_factor = abs(source_load) / (1.0 + abs(source_load))
        
        # 相位延迟
        phase_delay = self.phase_delay_factor * angular_separation
        phase_factor = np.cos(azimuth_angle + phase_delay)
        
        # 组合干扰强度
        interference = distance_factor * load_factor * phase_factor
        
        return interference


class DuSeligRadialFlowModel(ThreeDimensionalEffectsBase):
    """
    Du & Selig径向流动模型（基于adevice_complement5.md规范）
    
    实现径向流动效应建模，包括：
    - 径向流动速度计算
    - 失速延迟效应
    - 三维边界层效应
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Du & Selig径向流动模型
        
        Args:
            config: 配置参数
        """
        self.enable_stall_delay = config.get('enable_stall_delay', True)
        self.enable_radial_flow = config.get('enable_radial_flow', True)
        self.radial_flow_strength = config.get('radial_flow_strength', 1.0)
        
        # 模型参数
        self.c1 = config.get('c1', 1.5)  # Du & Selig参数
        self.c2 = config.get('c2', 0.8)  # Du & Selig参数
        
        print(f"✅ Du & Selig径向流动模型初始化完成")
        print(f"   失速延迟: {'启用' if self.enable_stall_delay else '禁用'}")
        print(f"   径向流动: {'启用' if self.enable_radial_flow else '禁用'}")
    
    def calculate_3d_correction(self, r: float, chord: float, alpha: float,
                              omega: float, **kwargs) -> float:
        """
        计算Du & Selig三维修正因子
        
        Args:
            r: 径向位置 [m]
            chord: 弦长 [m]
            alpha: 攻角 [rad]
            omega: 角速度 [rad/s]
            **kwargs: 额外参数
            
        Returns:
            correction_factor: 三维修正因子
        """
        # 计算径向雷诺数
        Re_radial = self._calculate_radial_reynolds_number(r, chord, omega, **kwargs)
        
        # 失速延迟修正
        if self.enable_stall_delay:
            stall_delay_factor = self._calculate_stall_delay_factor(Re_radial, alpha)
        else:
            stall_delay_factor = 1.0
        
        # 径向流动修正
        if self.enable_radial_flow:
            radial_flow_factor = self._calculate_radial_flow_factor(Re_radial, alpha)
        else:
            radial_flow_factor = 1.0
        
        # 组合修正因子
        correction_factor = stall_delay_factor * radial_flow_factor
        
        return correction_factor
    
    def _calculate_radial_reynolds_number(self, r: float, chord: float,
                                        omega: float, **kwargs) -> float:
        """计算径向雷诺数"""
        # 径向速度
        V_radial = omega * r
        
        # 运动粘度
        kinematic_viscosity = kwargs.get('kinematic_viscosity', 1.81e-5 / 1.225)
        
        # 径向雷诺数
        Re_radial = V_radial * chord / kinematic_viscosity
        
        return Re_radial
    
    def _calculate_stall_delay_factor(self, Re_radial: float, alpha: float) -> float:
        """计算失速延迟因子"""
        # Du & Selig失速延迟模型
        alpha_deg = np.degrees(abs(alpha))
        
        if Re_radial > 1e5:
            # 高雷诺数情况
            delay_factor = 1.0 + self.c1 * (Re_radial / 1e6)**0.2 * np.exp(-alpha_deg / 10.0)
        else:
            # 低雷诺数情况
            delay_factor = 1.0 + self.c2 * (Re_radial / 1e5)**0.5 * np.exp(-alpha_deg / 15.0)
        
        return min(delay_factor, 2.0)  # 限制最大值
    
    def _calculate_radial_flow_factor(self, Re_radial: float, alpha: float) -> float:
        """计算径向流动因子"""
        # 径向流动对升力的影响
        alpha_deg = np.degrees(abs(alpha))
        
        # 基于雷诺数的径向流动强度
        if Re_radial > 1e5:
            flow_strength = self.radial_flow_strength * (1.0 + 0.1 * np.log10(Re_radial / 1e5))
        else:
            flow_strength = self.radial_flow_strength * 0.5
        
        # 攻角依赖性
        alpha_factor = 1.0 + 0.1 * flow_strength * np.sin(2 * np.radians(alpha_deg))
        
        return alpha_factor


class ThreeDimensionalEffectsManager:
    """
    三维效应管理器
    
    统一管理各种三维效应模型
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化三维效应管理器
        
        Args:
            config: 配置参数
        """
        self.models = {}
        
        # 初始化各种模型
        if config.get('enable_nonlinear_induced_velocity', True):
            self.models['nonlinear_induced'] = NonlinearInducedVelocityModel(
                config.get('nonlinear_induced_config', {})
            )
        
        if config.get('enable_blade_interaction', True):
            self.models['blade_interaction'] = BladeInteractionModel(
                config.get('blade_interaction_config', {})
            )
        
        if config.get('enable_du_selig_radial_flow', True):
            self.models['du_selig'] = DuSeligRadialFlowModel(
                config.get('du_selig_config', {})
            )
        
        print(f"✅ 三维效应管理器初始化完成")
        print(f"   已加载模型: {list(self.models.keys())}")
    
    def apply_all_corrections(self, base_coefficients: Tuple[float, float, float],
                            correction_params: Dict[str, Any]) -> Tuple[float, float, float]:
        """
        应用所有三维修正
        
        Args:
            base_coefficients: 基本气动系数 (Cl, Cd, Cm)
            correction_params: 修正参数
            
        Returns:
            corrected_coefficients: 修正后的气动系数
        """
        Cl, Cd, Cm = base_coefficients
        
        # 应用各种三维修正
        for model_name, model in self.models.items():
            if model_name == 'nonlinear_induced':
                correction = model.calculate_3d_correction(**correction_params)
                Cl *= correction
            elif model_name == 'blade_interaction':
                correction = model.calculate_3d_correction(**correction_params)
                Cl *= correction
                Cd *= (1.0 + 0.1 * (correction - 1.0))  # 阻力也受影响
            elif model_name == 'du_selig':
                correction = model.calculate_3d_correction(**correction_params)
                Cl *= correction
        
        return Cl, Cd, Cm


class AdvancedThreeDimensionalEffects:
    """
    高级三维效应模型

    实现完整的三维效应建模，包括：
    - 径向流动效应
    - 桨叶间气动干扰
    - 压缩性修正
    - 循环翼特殊效应
    """

    def __init__(self, config: Dict[str, Any]):
        """初始化高级三维效应模型"""
        self.config = config

        # 径向流动模型
        self.radial_flow_model = RadialFlowModel(config.get('radial_flow', {}))

        # 桨叶干扰模型
        self.blade_interaction_model = BladeInteractionModel(config.get('blade_interaction', {}))

        # 压缩性修正模型
        self.compressibility_model = CompressibilityCorrection(config.get('compressibility', {}))

        # 循环翼特殊效应
        self.cycloidal_effects = CycloidalSpecialEffects(config.get('cycloidal_effects', {}))

        print("✅ 高级三维效应模型初始化完成")

    def calculate_complete_3d_effects(self,
                                    alpha: float,
                                    velocity: float,
                                    chord: float,
                                    radius: float,
                                    azimuth: float,
                                    blade_count: int,
                                    mach_number: float = 0.0) -> Tuple[float, float, float]:
        """
        计算完整的三维效应修正

        Args:
            alpha: 攻角 [rad]
            velocity: 局部速度 [m/s]
            chord: 弦长 [m]
            radius: 径向位置 [m]
            azimuth: 方位角 [rad]
            blade_count: 桨叶数
            mach_number: 马赫数

        Returns:
            (Cl_correction, Cd_correction, Cm_correction): 三维修正因子
        """
        # 基础修正因子
        cl_correction = 1.0
        cd_correction = 1.0
        cm_correction = 1.0

        # 1. 径向流动效应
        radial_correction = self.radial_flow_model.calculate_radial_flow_correction(
            alpha, velocity, chord, radius
        )
        cl_correction *= radial_correction['cl_factor']
        cd_correction *= radial_correction['cd_factor']

        # 2. 桨叶间干扰效应
        interaction_correction = self.blade_interaction_model.calculate_blade_interaction(
            azimuth, blade_count, radius
        )
        cl_correction *= interaction_correction['cl_factor']
        cd_correction *= interaction_correction['cd_factor']

        # 3. 压缩性修正
        if mach_number > 0.3:  # 只在高速时应用
            comp_correction = self.compressibility_model.calculate_compressibility_correction(
                mach_number, alpha
            )
            cl_correction *= comp_correction['cl_factor']
            cd_correction *= comp_correction['cd_factor']

        # 4. 循环翼特殊效应
        cycloidal_correction = self.cycloidal_effects.calculate_cycloidal_correction(
            alpha, azimuth, radius
        )
        cl_correction *= cycloidal_correction['cl_factor']
        cd_correction *= cycloidal_correction['cd_factor']
        cm_correction *= cycloidal_correction['cm_factor']

        return cl_correction, cd_correction, cm_correction


class RadialFlowModel:
    """径向流动模型"""

    def __init__(self, config: Dict[str, Any]):
        """初始化径向流动模型"""
        self.enable_radial_flow = config.get('enable_radial_flow', True)
        self.flow_strength_factor = config.get('flow_strength_factor', 1.0)
        self.radial_decay_factor = config.get('radial_decay_factor', 0.5)

    def calculate_radial_flow_correction(self, alpha: float, velocity: float,
                                       chord: float, radius: float) -> Dict[str, float]:
        """计算径向流动修正"""
        if not self.enable_radial_flow:
            return {'cl_factor': 1.0, 'cd_factor': 1.0}

        # 径向雷诺数效应
        Re_radial = velocity * chord / (1.5e-5)  # 简化雷诺数

        # 径向流动强度（基于攻角和径向位置）
        radial_flow_strength = self.flow_strength_factor * np.sin(alpha) * (radius / 1.0)

        # 升力修正（径向流动增强升力）
        cl_factor = 1.0 + 0.1 * radial_flow_strength * np.exp(-self.radial_decay_factor * radius)

        # 阻力修正（径向流动增加阻力）
        cd_factor = 1.0 + 0.05 * radial_flow_strength * np.exp(-self.radial_decay_factor * radius)

        return {
            'cl_factor': cl_factor,
            'cd_factor': cd_factor,
            'radial_flow_strength': radial_flow_strength
        }


class BladeInteractionModel:
    """桨叶间干扰模型"""

    def __init__(self, config: Dict[str, Any]):
        """初始化桨叶干扰模型"""
        self.enable_interaction = config.get('enable_interaction', True)
        self.interaction_strength = config.get('interaction_strength', 0.1)
        self.decay_distance = config.get('decay_distance', 2.0)

    def calculate_blade_interaction(self, azimuth: float, blade_count: int,
                                  radius: float) -> Dict[str, float]:
        """计算桨叶间干扰效应"""
        if not self.enable_interaction or blade_count <= 1:
            return {'cl_factor': 1.0, 'cd_factor': 1.0}

        # 计算到相邻桨叶的角度距离
        blade_spacing = 2 * np.pi / blade_count

        # 干扰强度随距离衰减
        interaction_factor = 0.0
        for i in range(1, blade_count):
            angle_diff = min(abs(i * blade_spacing), abs(2 * np.pi - i * blade_spacing))
            distance_factor = np.exp(-angle_diff / self.decay_distance)
            interaction_factor += distance_factor

        # 归一化干扰强度
        interaction_factor *= self.interaction_strength / blade_count

        # 干扰效应（通常降低升力，增加阻力）
        cl_factor = 1.0 - 0.05 * interaction_factor
        cd_factor = 1.0 + 0.03 * interaction_factor

        return {
            'cl_factor': cl_factor,
            'cd_factor': cd_factor,
            'interaction_factor': interaction_factor
        }


class CompressibilityCorrection:
    """压缩性修正模型"""

    def __init__(self, config: Dict[str, Any]):
        """初始化压缩性修正模型"""
        self.enable_compressibility = config.get('enable_compressibility', True)
        self.correction_method = config.get('method', 'karman_tsien')
        self.gamma = config.get('gamma', 1.4)  # 比热比

    def calculate_compressibility_correction(self, mach_number: float,
                                           alpha: float) -> Dict[str, float]:
        """计算压缩性修正"""
        if not self.enable_compressibility or mach_number < 0.3:
            return {'cl_factor': 1.0, 'cd_factor': 1.0}

        if self.correction_method == 'karman_tsien':
            return self._karman_tsien_correction(mach_number, alpha)
        elif self.correction_method == 'prandtl_glauert':
            return self._prandtl_glauert_correction(mach_number)
        else:
            return {'cl_factor': 1.0, 'cd_factor': 1.0}

    def _karman_tsien_correction(self, mach_number: float, alpha: float) -> Dict[str, float]:
        """Karman-Tsien修正"""
        M = mach_number
        beta = np.sqrt(1 - M**2)

        if M >= 1.0:
            # 超声速简化处理
            cl_factor = 0.8
            cd_factor = 2.0
        else:
            # 亚声速Karman-Tsien修正
            cl_factor = 1.0 / (beta + (M**2 / (1 + beta)) * (1 + ((self.gamma - 1) / 2) * M**2))
            cd_factor = 1.0 + 0.5 * M**2 * (1 + 0.2 * M**2)

        return {
            'cl_factor': cl_factor,
            'cd_factor': cd_factor,
            'beta': beta
        }

    def _prandtl_glauert_correction(self, mach_number: float) -> Dict[str, float]:
        """Prandtl-Glauert修正"""
        M = mach_number
        beta = np.sqrt(abs(1 - M**2))

        if M >= 1.0:
            cl_factor = 0.8
            cd_factor = 2.0
        else:
            cl_factor = 1.0 / beta
            cd_factor = 1.0 + 0.3 * M**2

        return {
            'cl_factor': cl_factor,
            'cd_factor': cd_factor,
            'beta': beta
        }


class CycloidalSpecialEffects:
    """循环翼特殊效应模型"""

    def __init__(self, config: Dict[str, Any]):
        """初始化循环翼特殊效应模型"""
        self.enable_cycloidal_effects = config.get('enable_cycloidal_effects', True)
        self.magnus_effect_strength = config.get('magnus_effect_strength', 0.1)
        self.unsteady_factor = config.get('unsteady_factor', 0.2)

    def calculate_cycloidal_correction(self, alpha: float, azimuth: float,
                                     radius: float) -> Dict[str, float]:
        """计算循环翼特殊效应修正"""
        if not self.enable_cycloidal_effects:
            return {'cl_factor': 1.0, 'cd_factor': 1.0, 'cm_factor': 1.0}

        # Magnus效应（由于桨叶旋转）
        magnus_factor = self.magnus_effect_strength * np.sin(azimuth) * (radius / 1.0)

        # 非定常效应（由于快速变化的攻角）
        unsteady_factor = self.unsteady_factor * np.cos(2 * azimuth) * abs(alpha)

        # 升力修正
        cl_factor = 1.0 + magnus_factor + unsteady_factor

        # 阻力修正
        cd_factor = 1.0 + 0.5 * abs(magnus_factor) + 0.3 * abs(unsteady_factor)

        # 力矩修正
        cm_factor = 1.0 + 0.2 * magnus_factor

        return {
            'cl_factor': cl_factor,
            'cd_factor': cd_factor,
            'cm_factor': cm_factor,
            'magnus_factor': magnus_factor,
            'unsteady_factor': unsteady_factor
        }
