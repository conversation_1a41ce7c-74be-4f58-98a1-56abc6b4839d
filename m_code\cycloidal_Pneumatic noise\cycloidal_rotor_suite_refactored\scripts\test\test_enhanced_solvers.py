"""
增强求解器测试
=============

测试UVLM自由尾迹演化和BPM噪声模型的完整实现
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_uvlm_enhanced_wake():
    """测试UVLM增强的自由尾迹演化"""
    print("🔧 测试UVLM增强的自由尾迹演化...")
    
    try:
        from core.aerodynamics.solvers.uvlm_solver import FreeWakeManager
        
        # 创建配置
        config = {
            'max_wake_age': 2.0,
            'wake_time_step': 0.005,
            'pc_iterations': 5,
            'vortex_core_radius': 0.02,
            'enable_vatistas_core': True,
            'convergence_tolerance': 1e-7
        }
        
        # 创建自由尾迹管理器
        wake_manager = FreeWakeManager(config)
        print("✅ 增强自由尾迹管理器创建成功")
        
        # 测试尾迹演化
        dt = 0.01
        blade_positions = np.array([
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [-1.0, 0.0, 0.0]
        ])
        circulation_distribution = np.array([2.0, 2.0, 2.0])
        
        # 执行几个时间步的演化
        for i in range(5):
            wake_manager.evolve_wake_geometry(dt, blade_positions, circulation_distribution)
            
            # 旋转桨叶位置
            angle = (i + 1) * dt * 10.0  # 10 rad/s
            rotation_matrix = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
            blade_positions = np.array([rotation_matrix @ pos for pos in blade_positions])
        
        # 获取尾迹几何
        wake_geometry = wake_manager.get_wake_geometry()
        print(f"✅ 尾迹演化完成，节点数: {wake_geometry['node_count']}")
        print(f"   平均年龄: {np.mean(wake_geometry['ages']):.3f} s")
        
        return True
        
    except Exception as e:
        print(f"❌ UVLM增强尾迹演化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_biot_savart_calculation():
    """测试完整的Biot-Savart计算"""
    print("\n🔧 测试完整的Biot-Savart计算...")
    
    try:
        from core.aerodynamics.solvers.uvlm_solver import FreeWakeManager
        
        config = {
            'max_wake_age': 1.0,
            'wake_time_step': 0.01,
            'pc_iterations': 3,
            'vortex_core_radius': 0.03,
            'enable_vatistas_core': True
        }
        
        wake_manager = FreeWakeManager(config)
        
        # 添加一些尾迹节点
        wake_manager.wake_nodes = [
            {
                'position': np.array([1.0, 0.0, 0.0]),
                'circulation': 2.0,
                'age': 0.1,
                'blade_index': 0,
                'vortex_segments': [
                    {
                        'start': np.array([0.9, 0.0, 0.0]),
                        'end': np.array([1.1, 0.0, 0.0]),
                        'circulation': 2.0
                    }
                ]
            }
        ]
        
        # 测试诱导速度计算
        test_point = np.array([0.0, 1.0, 0.0])
        induced_velocity = wake_manager._calculate_induced_velocity_at_point(test_point)
        
        print(f"✅ Biot-Savart计算成功")
        print(f"   诱导速度: [{induced_velocity[0]:.4f}, {induced_velocity[1]:.4f}, {induced_velocity[2]:.4f}] m/s")
        print(f"   速度幅值: {np.linalg.norm(induced_velocity):.4f} m/s")
        
        return True
        
    except Exception as e:
        print(f"❌ Biot-Savart计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bpm_complete_noise_model():
    """测试BPM完整噪声模型"""
    print("\n🔧 测试BPM完整噪声模型...")
    
    try:
        from core.acoustics.solvers.bmp_solver import BPMSolver
        
        # 创建配置
        config_dict = {
            'fidelity_level': 'medium',
            'time_step': 0.01,
            'solver_specific_params': {
                'kinematic_viscosity': 1.5e-5,
                'turbulence_intensity': 0.05,
                'enable_tbl_te_noise': True,
                'enable_separation_noise': True,
                'enable_tip_vortex_noise': True,
                'enable_blunt_te_noise': True,
                'enable_dynamic_boundary_layer': True,
                'frequency_min': 10.0,
                'frequency_max': 10000.0,
                'frequency_points': 50,
                'sound_speed': 343.0,
                'reference_pressure': 2e-5
            }
        }
        
        # 创建简化的配置对象
        class SimpleConfig:
            def __init__(self, config_dict):
                self.fidelity_level = config_dict['fidelity_level']
                self.time_step = config_dict['time_step']
                self.solver_specific_params = config_dict['solver_specific_params']
        
        config = SimpleConfig(config_dict)
        
        # 创建BPM求解器
        bpm_solver = BPMSolver(config)
        print("✅ BPM求解器创建成功")
        
        # 测试宽带噪声计算
        velocity = 50.0  # m/s
        alpha = np.radians(8.0)  # 8度攻角
        radius = 0.8  # 80%半径处
        chord = 0.1  # 10cm弦长
        
        frequencies, psd = bmp_solver._calculate_broadband_noise_psd(
            velocity, alpha, radius, chord
        )
        
        print(f"✅ 宽带噪声计算成功")
        print(f"   频率点数: {len(frequencies)}")
        print(f"   频率范围: {frequencies[0]:.1f} - {frequencies[-1]:.1f} Hz")
        print(f"   最大PSD: {np.max(psd):.2e} Pa²/Hz")
        print(f"   总声压级: {10*np.log10(np.sum(psd)/((2e-5)**2)):.1f} dB")
        
        # 测试边界层参数计算
        bl_params = bmp_solver._calculate_boundary_layer_parameters(velocity, alpha, chord)
        print(f"✅ 边界层参数计算成功")
        print(f"   雷诺数: {bl_params['Re_c']:.0f}")
        print(f"   位移厚度: {bl_params['displacement_thickness']*1000:.2f} mm")
        
        return True
        
    except Exception as e:
        print(f"❌ BPM完整噪声模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_noise_mechanisms():
    """测试各种噪声机制"""
    print("\n🔧 测试各种噪声机制...")
    
    try:
        from core.acoustics.solvers.bmp_solver import BPMSolver
        
        # 简化配置
        config_dict = {
            'fidelity_level': 'medium',
            'time_step': 0.01,
            'solver_specific_params': {
                'kinematic_viscosity': 1.5e-5,
                'frequency_min': 100.0,
                'frequency_max': 5000.0,
                'frequency_points': 20,
                'sound_speed': 343.0,
                'reference_pressure': 2e-5
            }
        }
        
        class SimpleConfig:
            def __init__(self, config_dict):
                self.fidelity_level = config_dict['fidelity_level']
                self.time_step = config_dict['time_step']
                self.solver_specific_params = config_dict['solver_specific_params']
        
        config = SimpleConfig(config_dict)
        bmp_solver = BPMSolver(config)
        
        # 测试参数
        frequencies = np.logspace(2, 3.7, 20)  # 100 Hz to 5 kHz
        velocity = 60.0
        bl_params = {
            'displacement_thickness': 0.001,
            'chord': 0.1,
            'Re_c': 400000,
            'velocity': velocity
        }
        
        # 测试各种噪声机制
        psd_tbl = bmp_solver._calculate_tbl_te_noise(frequencies, velocity, bl_params)
        print(f"✅ TBL-TE噪声: 最大值 {np.max(psd_tbl):.2e} Pa²/Hz")
        
        psd_sep = bmp_solver._calculate_separation_noise(frequencies, velocity, np.radians(15), bl_params)
        print(f"✅ 分离流噪声: 最大值 {np.max(psd_sep):.2e} Pa²/Hz")
        
        psd_tip = bmp_solver._calculate_tip_vortex_noise(frequencies, velocity, np.radians(5))
        print(f"✅ 桨尖涡噪声: 最大值 {np.max(psd_tip):.2e} Pa²/Hz")
        
        psd_blunt = bmp_solver._calculate_blunt_te_noise(frequencies, velocity, bl_params)
        print(f"✅ 钝后缘噪声: 最大值 {np.max(psd_blunt):.2e} Pa²/Hz")
        
        return True
        
    except Exception as e:
        print(f"❌ 噪声机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强求解器测试...")
    print("=" * 60)
    
    tests = [
        ("UVLM增强尾迹演化", test_uvlm_enhanced_wake),
        ("Biot-Savart计算", test_biot_savart_calculation),
        ("BPM完整噪声模型", test_bmp_complete_noise_model),
        ("噪声机制测试", test_noise_mechanisms),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 增强求解器测试结果汇总:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 增强求解器测试全部通过！")
        return True
    else:
        print("⚠️  部分增强求解器测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
