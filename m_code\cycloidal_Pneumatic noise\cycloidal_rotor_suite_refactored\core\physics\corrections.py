"""
物理修正模块
==========

实现各种物理修正模型
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod

class PhysicalCorrection(ABC):
    """物理修正基类"""
    
    @abstractmethod
    def apply_correction(self, *args, **kwargs) -> float:
        """应用修正"""
        pass

class TipLossCorrection(PhysicalCorrection):
    """
    叶尖损失修正
    
    基于Prandtl叶尖损失理论和现代修正方法
    """
    
    def __init__(self, correction_type: str = "prandtl"):
        """
        初始化叶尖损失修正
        
        Args:
            correction_type: 修正类型 ("prandtl", "glauert", "shen")
        """
        self.correction_type = correction_type
        
        print(f"✅ 叶尖损失修正初始化: {correction_type}")
    
    def apply_correction(self, r: float, R: float, B: int, 
                        alpha: float = 0.0, **kwargs) -> float:
        """
        应用叶尖损失修正
        
        Args:
            r: 径向位置 [m]
            R: 旋翼半径 [m]
            B: 桨叶数量
            alpha: 攻角 [rad]
            
        Returns:
            correction_factor: 修正因子 [0, 1]
        """
        if r >= R:
            return 0.0
        
        if self.correction_type == "prandtl":
            return self._prandtl_correction(r, R, B)
        elif self.correction_type == "glauert":
            return self._glauert_correction(r, R, B, alpha)
        elif self.correction_type == "shen":
            return self._shen_correction(r, R, B, alpha)
        else:
            raise ValueError(f"未知的修正类型: {self.correction_type}")
    
    def _prandtl_correction(self, r: float, R: float, B: int) -> float:
        """Prandtl叶尖损失修正"""
        f = B * (R - r) / (2 * r)
        F = (2 / np.pi) * np.arccos(np.exp(-f))
        return max(F, 0.01)  # 限制最小值
    
    def _glauert_correction(self, r: float, R: float, B: int, alpha: float) -> float:
        """Glauert修正（考虑攻角影响）"""
        # 基本Prandtl修正
        F_prandtl = self._prandtl_correction(r, R, B)
        
        # 攻角修正
        alpha_correction = 1.0 + 0.1 * abs(alpha)
        
        return F_prandtl * alpha_correction
    
    def _shen_correction(self, r: float, R: float, B: int, alpha: float) -> float:
        """Shen修正（现代改进方法）"""
        # 改进的叶尖损失函数
        g = np.exp(-0.125 * (B * (R - r) / r - 1))
        F_shen = (2 / np.pi) * np.arccos(g)
        
        return max(F_shen, 0.01)

class HubLossCorrection(PhysicalCorrection):
    """
    桂毂损失修正
    
    考虑桂毂附近的流动分离效应
    """
    
    def __init__(self, correction_type: str = "prandtl"):
        """
        初始化桂毂损失修正
        
        Args:
            correction_type: 修正类型 ("prandtl", "improved")
        """
        self.correction_type = correction_type
        
        print(f"✅ 桂毂损失修正初始化: {correction_type}")
    
    def apply_correction(self, r: float, R_hub: float, B: int, **kwargs) -> float:
        """
        应用桂毂损失修正
        
        Args:
            r: 径向位置 [m]
            R_hub: 桂毂半径 [m]
            B: 桨叶数量
            
        Returns:
            correction_factor: 修正因子 [0, 1]
        """
        if r <= R_hub:
            return 0.0
        
        if self.correction_type == "prandtl":
            return self._prandtl_hub_correction(r, R_hub, B)
        elif self.correction_type == "improved":
            return self._improved_hub_correction(r, R_hub, B)
        else:
            raise ValueError(f"未知的修正类型: {self.correction_type}")
    
    def _prandtl_hub_correction(self, r: float, R_hub: float, B: int) -> float:
        """Prandtl桂毂损失修正"""
        f_hub = B * (r - R_hub) / (2 * R_hub)
        F_hub = (2 / np.pi) * np.arccos(np.exp(-f_hub))
        return max(F_hub, 0.01)
    
    def _improved_hub_correction(self, r: float, R_hub: float, B: int) -> float:
        """改进的桂毂损失修正"""
        # 考虑桂毂涡的影响
        r_ratio = r / R_hub
        
        if r_ratio < 1.5:
            # 强桂毂效应区域
            F_hub = 0.5 * (1 + np.tanh(2 * (r_ratio - 1.2)))
        else:
            # 弱桂毂效应区域
            F_hub = self._prandtl_hub_correction(r, R_hub, B)
        
        return F_hub

class CompressibilityCorrection(PhysicalCorrection):
    """
    压缩性修正
    
    考虑高速流动的压缩性效应
    """
    
    def __init__(self, correction_type: str = "prandtl_glauert"):
        """
        初始化压缩性修正
        
        Args:
            correction_type: 修正类型 ("prandtl_glauert", "karman_tsien", "laitone")
        """
        self.correction_type = correction_type
        
        print(f"✅ 压缩性修正初始化: {correction_type}")
    
    def apply_correction(self, Mach: float, **kwargs) -> float:
        """
        应用压缩性修正
        
        Args:
            Mach: 马赫数
            
        Returns:
            correction_factor: 修正因子
        """
        if Mach >= 1.0:
            return 1.0  # 超音速时不适用
        
        if self.correction_type == "prandtl_glauert":
            return self._prandtl_glauert_correction(Mach)
        elif self.correction_type == "karman_tsien":
            return self._karman_tsien_correction(Mach)
        elif self.correction_type == "laitone":
            return self._laitone_correction(Mach)
        else:
            raise ValueError(f"未知的修正类型: {self.correction_type}")
    
    def _prandtl_glauert_correction(self, Mach: float) -> float:
        """Prandtl-Glauert压缩性修正"""
        beta = np.sqrt(1 - Mach**2)
        return 1.0 / beta
    
    def _karman_tsien_correction(self, Mach: float) -> float:
        """Karman-Tsien修正（改进的压缩性修正）"""
        beta = np.sqrt(1 - Mach**2)
        return 1.0 / (beta + (Mach**2 / (1 + beta)) * (1 / beta))
    
    def _laitone_correction(self, Mach: float) -> float:
        """Laitone修正（高马赫数修正）"""
        beta = np.sqrt(1 - Mach**2)
        gamma = 1.4  # 空气比热比
        
        correction = 1.0 / beta * (1 + ((gamma - 1) / 2) * Mach**2 / (1 + beta))
        return correction


class AdvancedCompressibilityCorrection(PhysicalCorrection):
    """
    高级压缩性修正（基于adevice_complement5.md规范）

    实现完整的跨声速修正算法，包括：
    - 高阶修正算法
    - 激波-边界层相互作用
    - 临界马赫数计算
    """

    def __init__(self, correction_type: str = "advanced"):
        """
        初始化高级压缩性修正

        Args:
            correction_type: 修正类型 ("advanced", "transonic", "shock_interaction")
        """
        self.correction_type = correction_type
        self.sound_speed = 343.0  # 声速 [m/s]
        self.gamma = 1.4  # 比热比

        # 高级修正参数
        self.enable_shock_boundary_layer = True
        self.enable_critical_mach_calculation = True
        self.shock_strength_threshold = 0.1

        print(f"✅ 高级压缩性修正初始化: {correction_type}")
        print(f"   激波-边界层相互作用: {'启用' if self.enable_shock_boundary_layer else '禁用'}")

    def apply_correction(self, Cl: float, Cd: float, mach: float, **kwargs) -> Tuple[float, float]:
        """
        应用高级压缩性修正

        Args:
            Cl: 升力系数
            Cd: 阻力系数
            mach: 马赫数
            **kwargs: 额外参数（攻角、厚度比等）

        Returns:
            (Cl_corrected, Cd_corrected): 修正后的系数
        """
        if mach < 0.3:  # 低马赫数，不需要修正
            return Cl, Cd

        # 计算临界马赫数
        if self.enable_critical_mach_calculation:
            M_crit = self._calculate_critical_mach_number(Cl, **kwargs)
        else:
            M_crit = 0.7  # 默认值

        # 选择修正方法
        if self.correction_type == "advanced":
            Cl_corr, Cd_corr = self._advanced_compressibility_correction(Cl, Cd, mach, M_crit, **kwargs)
        elif self.correction_type == "transonic":
            Cl_corr, Cd_corr = self._transonic_correction(Cl, Cd, mach, M_crit, **kwargs)
        elif self.correction_type == "shock_interaction":
            Cl_corr, Cd_corr = self._shock_interaction_correction(Cl, Cd, mach, **kwargs)
        else:
            # 回退到基本修正
            Cl_corr, Cd_corr = self._prandtl_glauert_correction(Cl, Cd, mach)

        # 应用激波-边界层相互作用修正
        if self.enable_shock_boundary_layer and mach > M_crit:
            Cl_corr, Cd_corr = self._apply_shock_boundary_layer_interaction(
                Cl_corr, Cd_corr, mach, M_crit, **kwargs
            )

        return Cl_corr, Cd_corr

    def _calculate_critical_mach_number(self, Cl: float, **kwargs) -> float:
        """
        计算临界马赫数

        Args:
            Cl: 升力系数
            **kwargs: 额外参数

        Returns:
            M_crit: 临界马赫数
        """
        # 获取翼型参数
        thickness_ratio = kwargs.get('thickness_ratio', 0.12)  # 厚度比
        camber = kwargs.get('camber', 0.02)  # 弯度

        # 基于翼型几何的临界马赫数估算
        M_crit_thickness = 0.87 - 0.17 * thickness_ratio
        M_crit_camber = 0.87 - 0.1 * camber
        M_crit_lift = 0.87 - 0.05 * abs(Cl)

        # 取最小值作为临界马赫数
        M_crit = min(M_crit_thickness, M_crit_camber, M_crit_lift)

        return max(M_crit, 0.5)  # 限制最小值

    def _advanced_compressibility_correction(self, Cl: float, Cd: float, mach: float,
                                           M_crit: float, **kwargs) -> Tuple[float, float]:
        """
        高级压缩性修正（组合多种方法）
        """
        if mach < M_crit:
            # 亚临界：使用Karman-Tsien修正
            return self._karman_tsien_correction(Cl, Cd, mach)
        elif mach < 1.0:
            # 跨声速：混合修正
            return self._transonic_correction(Cl, Cd, mach, M_crit, **kwargs)
        else:
            # 超声速：使用Prandtl-Glauert修正
            return self._prandtl_glauert_correction(Cl, Cd, mach)

    def _prandtl_glauert_correction(self, Cl: float, Cd: float, mach: float) -> Tuple[float, float]:
        """Prandtl-Glauert修正（完整实现）"""
        if mach >= 1.0:
            # 超声速情况 - Ackeret理论
            beta = np.sqrt(mach**2 - 1)
            correction_factor = 1.0 / beta

            # 超声速阻力修正
            Cd_wave = 4 * Cl**2 / (self.gamma * mach**2 * beta)
            Cd_corrected = Cd + Cd_wave
        else:
            # 亚声速情况
            beta = np.sqrt(1 - mach**2)
            correction_factor = 1.0 / beta
            Cd_corrected = Cd

        Cl_corrected = Cl * correction_factor

        return Cl_corrected, Cd_corrected

    def _karman_tsien_correction(self, Cl: float, Cd: float, mach: float) -> Tuple[float, float]:
        """Karman-Tsien修正（高阶修正）"""
        if mach >= 1.0:
            return self._prandtl_glauert_correction(Cl, Cd, mach)

        beta = np.sqrt(1 - mach**2)

        # Karman-Tsien公式（完整形式）
        numerator = Cl
        denominator = beta + (mach**2 / (1 + beta)) * (Cl / 2)

        if abs(denominator) > 1e-6:
            correction_factor = 1.0 / denominator
        else:
            correction_factor = 1.0 / beta  # 回退到Prandtl-Glauert

        Cl_corrected = numerator * correction_factor

        # 阻力修正（考虑压缩性效应）
        Cd_compressible = Cd * (1 + 0.2 * mach**2)

        return Cl_corrected, Cd_compressible


class ReynoldsNumberCorrection(PhysicalCorrection):
    """
    雷诺数修正
    
    考虑雷诺数对气动特性的影响
    """
    
    def __init__(self):
        """初始化雷诺数修正"""
        print("✅ 雷诺数修正初始化")
    
    def apply_correction(self, Re: float, airfoil_type: str = "symmetric", **kwargs) -> Tuple[float, float]:
        """
        应用雷诺数修正
        
        Args:
            Re: 雷诺数
            airfoil_type: 翼型类型
            
        Returns:
            (Cl_correction, Cd_correction): 升力和阻力修正因子
        """
        if Re < 1e4:
            # 极低雷诺数
            Cl_correction = 0.5
            Cd_correction = 3.0
        elif Re < 1e5:
            # 低雷诺数
            Cl_correction = 0.7 + 0.3 * np.log10(Re / 1e4)
            Cd_correction = 2.0 - 1.0 * np.log10(Re / 1e4)
        elif Re < 1e6:
            # 中等雷诺数
            Cl_correction = 0.9 + 0.1 * np.log10(Re / 1e5)
            Cd_correction = 1.2 - 0.2 * np.log10(Re / 1e5)
        else:
            # 高雷诺数
            Cl_correction = 1.0
            Cd_correction = 1.0
        
        return Cl_correction, Cd_correction

class CorrectionManager:
    """
    修正管理器
    
    统一管理各种物理修正
    """
    
    def __init__(self):
        """初始化修正管理器"""
        self.corrections: Dict[str, PhysicalCorrection] = {}
        
        print("✅ 修正管理器初始化完成")
    
    def register_correction(self, name: str, correction: PhysicalCorrection) -> None:
        """注册修正"""
        self.corrections[name] = correction
        print(f"注册修正: {name}")
    
    def apply_all_corrections(self, base_coefficients: Tuple[float, float, float],
                            correction_params: Dict[str, Any]) -> Tuple[float, float, float]:
        """
        应用所有修正
        
        Args:
            base_coefficients: 基本气动系数 (Cl, Cd, Cm)
            correction_params: 修正参数
            
        Returns:
            corrected_coefficients: 修正后的气动系数
        """
        Cl, Cd, Cm = base_coefficients
        
        # 应用叶尖损失修正
        if "tip_loss" in self.corrections:
            tip_factor = self.corrections["tip_loss"].apply_correction(**correction_params)
            Cl *= tip_factor
        
        # 应用桂毂损失修正
        if "hub_loss" in self.corrections:
            hub_factor = self.corrections["hub_loss"].apply_correction(**correction_params)
            Cl *= hub_factor
        
        # 应用压缩性修正
        if "compressibility" in self.corrections and "Mach" in correction_params:
            comp_factor = self.corrections["compressibility"].apply_correction(**correction_params)
            Cl *= comp_factor
        
        # 应用雷诺数修正
        if "reynolds" in self.corrections and "Re" in correction_params:
            Cl_re_factor, Cd_re_factor = self.corrections["reynolds"].apply_correction(**correction_params)
            Cl *= Cl_re_factor
            Cd *= Cd_re_factor
        
        return Cl, Cd, Cm
    
    def get_correction_info(self) -> Dict[str, str]:
        """获取修正信息"""
        return {name: type(correction).__name__ for name, correction in self.corrections.items()}

# 工厂函数
def create_standard_corrections() -> CorrectionManager:
    """创建标准修正集合"""
    manager = CorrectionManager()
    
    # 注册标准修正
    manager.register_correction("tip_loss", TipLossCorrection("prandtl"))
    manager.register_correction("hub_loss", HubLossCorrection("prandtl"))
    manager.register_correction("compressibility", CompressibilityCorrection("prandtl_glauert"))
    manager.register_correction("reynolds", ReynoldsNumberCorrection())
    
    return manager

def create_advanced_corrections() -> CorrectionManager:
    """创建高级修正集合"""
    manager = CorrectionManager()
    
    # 注册高级修正
    manager.register_correction("tip_loss", TipLossCorrection("shen"))
    manager.register_correction("hub_loss", HubLossCorrection("improved"))
    manager.register_correction("compressibility", CompressibilityCorrection("karman_tsien"))
    manager.register_correction("reynolds", ReynoldsNumberCorrection())
    
    return manager


# ==================== 增强的压缩性修正方法（基于adevice_complement4.md规范） ====================

class TransonicCorrection(PhysicalCorrection):
    """
    跨声速效应修正（马赫数0.3-0.9）- 基于adevice_complement4.md规范
    """

    def __init__(self):
        """初始化跨声速修正"""
        super().__init__()
        self.gamma = 1.4  # 比热比

    def apply_correction(self, cl: float, mach_number: float, alpha: float = 0.0, **kwargs) -> float:
        """
        应用跨声速修正（实现抽象方法）

        Args:
            cl: 升力系数
            mach_number: 马赫数
            alpha: 攻角 [rad]

        Returns:
            修正后的升力系数
        """
        return self.apply_transonic_corrections(mach_number, cl)

    def apply_transonic_corrections(self, mach_number: float, cl: float) -> float:
        """
        跨声速效应修正

        Args:
            mach_number: 马赫数
            cl: 升力系数

        Returns:
            corrected_cl: 修正后的升力系数
        """
        try:
            # 跨声速范围检查
            if mach_number < 0.3:
                # 低速范围，使用标准压缩性修正
                return cl * self._subsonic_correction(mach_number)
            elif mach_number > 0.9:
                # 超声速范围，使用线性化理论
                return self._supersonic_correction(mach_number, cl)
            else:
                # 跨声速范围，使用增强修正
                return self._transonic_correction_enhanced(mach_number, cl)

        except Exception as e:
            print(f"   ⚠️ 跨声速修正失败: {e}")
            return cl

    def _subsonic_correction(self, mach_number: float) -> float:
        """亚声速修正"""
        beta = np.sqrt(1 - mach_number**2)
        return 1.0 / beta

    def _transonic_correction_enhanced(self, mach_number: float, cl: float) -> float:
        """增强的跨声速修正"""
        # 临界马赫数估计
        M_crit = self._estimate_critical_mach_number(cl)

        if mach_number < M_crit:
            # 亚临界区域
            beta = np.sqrt(1 - mach_number**2)
            correction_factor = 1.0 / beta

            # 非线性修正项
            nonlinear_term = 0.1 * cl**2 * mach_number**2
            correction_factor *= (1.0 + nonlinear_term)

        else:
            # 超临界区域（局部超声速）
            # 使用经验修正公式
            delta_M = mach_number - M_crit
            shock_factor = 1.0 - 0.5 * delta_M / (0.9 - M_crit)
            shock_factor = max(0.5, shock_factor)  # 限制最小值

            correction_factor = shock_factor / np.sqrt(abs(1 - mach_number**2))

        return cl * correction_factor

    def _estimate_critical_mach_number(self, cl: float) -> float:
        """估计临界马赫数"""
        # 基于升力系数的临界马赫数估计
        M_crit_base = 0.7  # 基准临界马赫数

        # 升力系数对临界马赫数的影响
        cl_effect = -0.1 * abs(cl)  # 升力系数越大，临界马赫数越小

        M_crit = M_crit_base + cl_effect
        return max(0.5, min(0.85, M_crit))  # 限制在合理范围内

    def _supersonic_correction(self, mach_number: float, cl: float) -> float:
        """超声速修正"""
        # 线性化超声速理论
        beta = np.sqrt(mach_number**2 - 1)
        correction_factor = 1.0 / beta

        # 激波-边界层相互作用修正
        shock_bl_factor = 1.0 - 0.1 * (mach_number - 1.0)
        correction_factor *= shock_bl_factor

        return cl * correction_factor

class HighOrderPrandtlGlauert(PhysicalCorrection):
    """
    Prandtl-Glauert修正的高阶项（基于adevice_complement4.md规范）
    """

    def __init__(self):
        """初始化高阶Prandtl-Glauert修正"""
        super().__init__()
        self.gamma = 1.4  # 比热比

    def apply_correction(self, mach_number: float, alpha: float = 0.0, **kwargs) -> float:
        """
        应用高阶Prandtl-Glauert修正（实现抽象方法）

        Args:
            mach_number: 马赫数
            alpha: 攻角 [rad]

        Returns:
            修正因子
        """
        return self.compute_prandtl_glauert_higher_order(mach_number)

    def compute_prandtl_glauert_higher_order(self, mach_number: float) -> float:
        """
        Prandtl-Glauert修正的高阶项

        Args:
            mach_number: 马赫数

        Returns:
            correction_factor: 高阶修正因子
        """
        try:
            if mach_number >= 1.0:
                return 1.0  # 超声速时不适用

            beta = np.sqrt(1 - mach_number**2)

            # 一阶项（标准Prandtl-Glauert）
            first_order = 1.0 / beta

            # 二阶修正项
            second_order_coeff = (self.gamma + 1) / 4
            second_order = second_order_coeff * mach_number**2 / beta**3

            # 三阶修正项
            third_order_coeff = (self.gamma + 1)**2 / 16
            third_order = third_order_coeff * mach_number**4 / beta**5

            # 总修正因子
            correction_factor = first_order * (1.0 + second_order + third_order)

            return correction_factor

        except Exception as e:
            print(f"   ⚠️ 高阶Prandtl-Glauert修正失败: {e}")
            return 1.0

class ShockBoundaryLayerInteraction(PhysicalCorrection):
    """
    激波-边界层相互作用效应（基于adevice_complement4.md规范）
    """

    def __init__(self):
        """初始化激波-边界层相互作用修正"""
        super().__init__()

    def apply_correction(self, cl: float, mach_number: float, alpha: float = 0.0, **kwargs) -> float:
        """
        应用激波-边界层相互作用修正（实现抽象方法）

        Args:
            cl: 升力系数
            mach_number: 马赫数
            alpha: 攻角 [rad]

        Returns:
            修正后的升力系数
        """
        flow_data = {
            'cl': cl,
            'mach_number': mach_number,
            'alpha': alpha
        }
        corrected_data = self.apply_shock_boundary_layer_interaction(flow_data)
        return corrected_data.get('cl', cl)

    def apply_shock_boundary_layer_interaction(self, flow_data: Dict) -> Dict:
        """
        激波-边界层相互作用效应

        Args:
            flow_data: 流动数据字典

        Returns:
            corrected_flow_data: 修正后的流动数据
        """
        try:
            corrected_data = flow_data.copy()

            mach_number = flow_data.get('mach_number', 0.5)
            pressure_coefficient = flow_data.get('cp', 0.0)

            # 只在跨声速和超声速时应用
            if mach_number < 0.8:
                return corrected_data

            # 激波强度估计
            shock_strength = self._estimate_shock_strength(mach_number, pressure_coefficient)

            if shock_strength > 0.1:  # 显著激波
                # 边界层增厚效应
                bl_thickening_factor = 1.0 + 0.5 * shock_strength

                # 修正边界层参数
                if 'boundary_layer_thickness' in corrected_data:
                    corrected_data['boundary_layer_thickness'] *= bl_thickening_factor

                if 'displacement_thickness' in corrected_data:
                    corrected_data['displacement_thickness'] *= bl_thickening_factor

                # 修正阻力系数
                if 'cd' in corrected_data:
                    drag_increase = 0.02 * shock_strength  # 激波阻力增加
                    corrected_data['cd'] += drag_increase

                # 修正升力系数（激波诱导分离）
                if 'cl' in corrected_data:
                    lift_reduction = 0.1 * shock_strength
                    corrected_data['cl'] *= (1.0 - lift_reduction)

            return corrected_data

        except Exception as e:
            print(f"   ⚠️ 激波-边界层相互作用修正失败: {e}")
            return flow_data

    def _estimate_shock_strength(self, mach_number: float, cp: float) -> float:
        """估计激波强度"""
        # 基于马赫数和压力系数的激波强度估计
        if mach_number < 0.8:
            return 0.0

        # 简化的激波强度模型
        shock_strength = max(0.0, (mach_number - 0.8) / 0.4)  # 0.8-1.2范围内线性增长

        # 压力系数的影响
        cp_effect = max(0.0, -cp / 2.0)  # 负压力系数增强激波

        total_strength = min(1.0, shock_strength + cp_effect)
        return total_strength

def create_enhanced_compressibility_corrections() -> CorrectionManager:
    """创建增强的压缩性修正集合"""
    manager = CorrectionManager()

    # 注册增强的压缩性修正
    manager.register_correction("transonic", TransonicCorrection())
    manager.register_correction("high_order_pg", HighOrderPrandtlGlauert())
    manager.register_correction("shock_bl", ShockBoundaryLayerInteraction())

    return manager
